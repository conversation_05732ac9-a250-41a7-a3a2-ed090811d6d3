# Name your project! Project names should contain only lowercase characters
# and underscores. A good package name should reflect your organization's
# name or the intended use of these models
name: 'fanfix_dbt'
version: '1.0.0'

# This setting configures which "profile" dbt uses for this project.
profile: 'default'

# These configurations specify where dbt should look for different types of files.
# The `model-paths` config, for example, states that models in this project can be
# found in the "models/" directory. You probably won't need to change these!
model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target"  # directory which will store compiled SQL files
clean-targets:         # directories to be removed by `dbt clean`
  - "target"
  - "dbt_packages"

vars:
  # Environment and date variables
  TIMEZONE: 'UTC'
  START_DATE: '2023-01-01'
  END_DATE: "{{ (modules.datetime.date.today() + modules.datetime.timedelta(days=30)).strftime('%Y-%m-%d') }}"
  # env: 'dev'

  # Database variables - default database
  fanfix_database: fanfix_raw
  stripe_database: fanfix_stripe

  # Schema variables - default schema
  stripe_schema: stripe
  
  # Schema Variables
  fanfix_user_schema: 'fanfix_user'  # default schema for fanfix_user source


# Configuring models
# Full documentation: https://docs.getdbt.com/docs/configuring-models

# In dbt, the default materialization for a model is a view. This means, when you run 
# dbt run or dbt build, all of your models will be built as a view in your data platform. 
# The configuration below will override this setting for models in the example folder to 
# instead be materialized as tables. Any models you add to the root of the models folder will 
# continue to be built as views. These settings can be overridden in the individual model files
# using the `{{ config(...) }}` macro.

models:
  fanfix_dbt:
    # Staging models - materialized as views for transformation and cost efficiency
    staging:
      +materialized: view
      +schema: staging

    # Mart models - materialized as tables for performance
    mart:
      +materialized: table

    # Analytics models - materialized as tables for performance
    intermediate:
      +materialized: table
      +schema: intermediate

    # # Fact tables
    # fact_tables:
    #   +materialized: table
    #   +schema: analytics
    #   +tags: ["fact"]

    # # Dimension tables
    # dimensions:
    #   +materialized: table
    #   +schema: analytics
    #   +tags: ["dimension"]