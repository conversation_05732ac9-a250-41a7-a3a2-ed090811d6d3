{% macro get_creator_launch_revenue_cohort(launch_revenue) %}
        case when launch_revenue < 3000 and launch_revenue >= 100 then '1. Launch Revenue $100 - $3000'
            when launch_revenue < 10000 and launch_revenue >= 3000 then '2. Launch Revenue $3000 - $10000'
            when launch_revenue < 20000 and launch_revenue >= 10000 then '3. Launch Revenue $10000 - $20000'
            when launch_revenue < 50000 and launch_revenue >= 20000 then '4. Launch Revenue $20000 - $50000'
            when launch_revenue < 100000 and launch_revenue >= 50000 then '5. Launch Revenue $50000 - $100000'
            when launch_revenue >= 100000 then '6. Launch Revenue > $100000'
            else 'other'
        end
{% endmacro %}