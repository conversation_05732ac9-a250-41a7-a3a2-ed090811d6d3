with livestream_data_complete as (
    select pl.created_at as livestream_created_at, audience_type, creator_id, cp.fanfix_username, cp.account_management_agency,
            ended_at, pl.livestream_id as livestream_id,  session_id, started_at, title, total_tip_amount_in_cents,
            activity_type, comment, pla.created_at as event_created_at, pla.livestream_activity_id as activity_id, user_id,
            pla.fan_tip_amount_in_cents,
            pl.subscriber_price_in_cents as subscriber_unlock_price,
            pl.non_subscriber_price_in_cents as non_subscriber_unlock_price,
            pl.pricing,
            pl.pricing_model_type,
            pl.minimum_comment_tip_amount_in_cents/100 as minimum_comment_tip_amount,
            pl.allows_comments,
            pl.allows_tips
    from {{ ref('stg_fanfix_livestream_public_livestreams') }} pl
    left join {{ ref('stg_fanfix_livestream_public_livestream_activities') }} pla on pla.livestream_id = pl.livestream_id
    join {{ ref('dim_creator_profile') }} cp on cp.fanfix_id = creator_id
),
summary_by_stream as (
    select creator_id, fanfix_username, livestream_id, audience_type, 
            allows_comments,
            allows_tips,
            minimum_comment_tip_amount,
            subscriber_unlock_price,
            non_subscriber_unlock_price,
            pricing,
            pricing_model_type,
            livestream_created_at, ended_at, 
            datediff('minutes', livestream_created_at, ended_at) as duration_in_minutes,
            count(distinct case when activity_type = 'joined' then user_id else null end) as total_user_joined,
            count(distinct case when activity_type = 'unlocked' then user_id else null end) as total_user_unlocked,
            sum( case when activity_type = 'comment' then 1 else 0 end) as total_comments,
            round(avg(total_tip_amount_in_cents)/100, 2) as total_tip_amount, 
            sum(case when activity_type = 'tip' then fan_tip_amount_in_cents else 0 end) as tip_amount,
            sum(case when activity_type = 'unlocked' then fan_tip_amount_in_cents else 0 end) as unlock_amount
    from livestream_data_complete
    group by 1,2,3,4,5,6,7,8,9,10,11, 12, 13
)
,viewer_metrics AS (
    SELECT 
        LIVESTREAM_ID,
        SUM(concurrent_viewers) as total_minutes_watched,
        ROUND(AVG(concurrent_viewers), 0) as average_concurrent_users
    FROM {{ ref('fact_livestream_concurrent_viewers') }}
    GROUP BY 1
)
, viewers as (
    select
        distinct 
        livestream_id, 
        livestream_created_at,
        creator_id, 
        user_id
    from livestream_data_complete
    where activity_type = 'joined'
)
,tippers AS (
    SELECT 
        LIVESTREAM_ID,
        count(distinct USER_ID) as tippers
    FROM {{ ref('stg_fanfix_livestream_public_livestream_activities') }}
    WHERE ACTIVITY_TYPE = 'tip'
    GROUP BY 1
)
,subscribers_conversion as (
    select
        v.livestream_id, 
        count(distinct v.user_id) as subscribers_converted
    from viewers v
    join {{ ref('stg_fanfix_subscription_subscriptions') }} s
        on v.user_id = s.FAN_ID
        and v.creator_id = s.CREATOR_ID
        and s.CREATED_AT >= v.livestream_created_at and s.CREATED_AT <= dateadd('hour', 3, v.livestream_created_at)
    group by 1
)
,followers_conversion as (
    select
        v.livestream_id, 
        count(distinct v.user_id) as followers_converted
    from viewers v
    join {{ ref('stg_fanfix_creator_follows') }} cf
        on v.user_id = cf.FOLLOWING_USER_ID
        and v.creator_id = cf.FOLLOWED_creator_ID
        and cf.CREATED_AT >= v.livestream_created_at and cf.CREATED_AT <= dateadd('hour', 3, v.livestream_created_at)
    group by 1
)
,existing_subscribers as (
    select
        sbs.livestream_id, 
        sum(das.subscriber_count) as existing_subscribers
    from summary_by_stream sbs
    join {{ ref('fact_daily_active_subscriber_by_creator') }} das
        on sbs.creator_id = das.CREATOR_FANFIX_ID
        and date(das.DATE) = date(sbs.livestream_created_at)
    group by 1
)
,non_engaged_users as (
    select 
        livestream_id,
        count(distinct user_id) as non_engaged_users
    from (
        select 
            livestream_id,
            user_id,
            sum(case when activity_type = 'tip' then 1 else 0 end) as tip_count,
            sum(case when activity_type = 'comment' then 1 else 0 end) as comment_count
        from livestream_data_complete
        group by 1, 2
        having tip_count = 0 and comment_count = 0
    ) idle_users
    group by 1
)
select
    SBS.*,
    COALESCE(VM.TOTAL_MINUTES_WATCHED,0) as total_minutes_watched,
    COALESCE(VM.AVERAGE_CONCURRENT_USERS, 0) as average_concurrent_users,
    coalesce(t.tippers, 0) as tippers,
    coalesce(sc.subscribers_converted, 0) as subscribers_converted,
    coalesce(fc.followers_converted, 0) as followers_converted,
    coalesce(es.existing_subscribers, 0) as existing_subscribers,
    coalesce(neu.non_engaged_users, 0) as non_engaged_users,
    1 - non_engaged_users / nullif(total_user_joined,0) as engagement_rate
from summary_by_stream SBS
LEFT JOIN VIEWER_METRICS VM
    ON SBS.LIVESTREAM_ID = VM.LIVESTREAM_ID
left join tippers t
    on sbs.livestream_id = t.livestream_id
left join subscribers_conversion sc
    on sbs.livestream_id = sc.livestream_id
left join followers_conversion fc
    on sbs.livestream_id = fc.livestream_id
left join existing_subscribers es
    on sbs.livestream_id = es.livestream_id
left join non_engaged_users neu
    on sbs.livestream_id = neu.livestream_id
ORDER BY existing_subscribers desc