WITH creator_revenue_by_day AS (
    SELECT DISTINCT
        charges.payment_intent
        , charge_id
        , charges.captured_at AS time
        , charges.card_country
        , charges.card_funding
        , tax.card_type AS payment_method
        , charges.card_id
        , p.related_entity_id
        , COALESCE(LOWER(p.tip_type), {{ charge_type('charges.description') }}) AS type
        , am.account_id AS account_id
        , am.value AS creator_fanfix_id
        , cm.value AS fan_fanfix_id

        -- Main gross revenue metric for reporting within the company;
        -- per Finance request, this metric should deduct sales tax (tax_amount from fact_payment_tax_collection)

        , CAST((charges.amount / 100) AS DECIMAL(10, 2)) - COALESCE(tax.tax_amount, 0) AS gross_revenue 

        -- This is the full amount coming from Stripe database, this should line up with Stripe reports
        , CAST((charges.amount / 100) AS DECIMAL(10, 2)) as stripe_gross_revenue

        -- sales tax
        , COALESCE(tax.tax_amount, 0) AS sales_tax

        -- subtotal, this should be the same as the "price" for the content being sold
        , CAST((charges.amount / 100) - COALESCE(tax.tax_amount, 0) - COALESCE(tax.processing_fee, 0) AS DECIMAL(10, 2)) AS sub_total

        -- This is the portion we'll pay to the creator; It's 80% of the subtotal
        , CAST((charges.amount / 100) - COALESCE(application_fees.amount / 100, 0) AS DECIMAL(10, 2)) AS net_revenue 

        -- total fee charged, including application fee and proceessing fee
        , CAST(COALESCE(application_fees.amount / 100, 0) AS DECIMAL(10, 3)) AS total_fee

        -- platform fee, this is 20% of subtotal
        , CASE
            WHEN tax.application_fee IS NOT NULL AND tax.application_fee > 0 THEN tax.application_fee
            ELSE GREATEST(CAST((application_fees.amount / 100) - COALESCE(tax.tax_amount, 0) AS DECIMAL(10, 2)), 0)
        END AS platform_fee -- IN prevent of missing platform fee FROM PAYMENT db. (fanfix fee)

        -- processing fee is an additional fee charged per transation
        , COALESCE(tax.processing_fee, 0)  AS processing_fee  --(stripe fee)
    FROM {{ ref('stg_stripe_charges') }} charges 
    LEFT JOIN {{ ref('stg_fanfix_payment_payments') }} p on charges.payment_intent = p.payment_processor_transaction_id
    LEFT JOIN (SELECT * FROM {{ ref('stg_stripe_connected_accounts_metadata') }} WHERE key = 'id') am ON am.account_id = charges.destination_id
    LEFT JOIN (SELECT * FROM {{ ref('stg_stripe_customers_metadata') }} WHERE key = 'appUserId') cm ON charges.customer_id = cm.customer_id
    LEFT JOIN {{ ref('stg_stripe_application_fees') }} application_fees ON application_fees.application_fee_id = charges.application_fee_id
    LEFT JOIN {{ ref('fact_payment_tax_collection') }} tax ON charges.payment_intent = tax.payment_processor_transaction_id
    WHERE 1 = 1
        AND paid = true
        AND charges.amount > 0
        AND charges.status = 'succeeded'
        AND charges.merchant_id = 'acct_1Ir538D2AfqkY9Hk'

),
name_resolved_revenue AS (
    SELECT
        creator_revenue_by_day.charge_id
        , creator_revenue_by_day.time
        , card_country
        , card_funding
        , payment_method
        , card_id
        , related_entity_id::varchar as entity_id
        , CASE
            WHEN creator_revenue_by_day.type = 'publication' THEN 'tip jar'
            ELSE creator_revenue_by_day.type
        END AS type
        , CASE 
            WHEN type like '%subscription%' THEN 'subscription' 
            WHEN type = 'livestream' THEN 'livestream tip'
            WHEN type = 'post' THEN 'post tip'
            ELSE type 
        END AS revenue_category
        , account_id
        , creator_fanfix_id
        , creator.username AS creator_username
        , fan_fanfix_id
        , fan.username AS fan_username
        , creator_revenue_by_day.sales_tax
        , creator_revenue_by_day.total_fee
        , creator_revenue_by_day.platform_fee
        , creator_revenue_by_day.net_revenue
        , creator_revenue_by_day.gross_revenue
        , creator_revenue_by_day.stripe_gross_revenue
        , creator_revenue_by_day.net_revenue * 1.25 AS external_reporting_gross_revenue
        , creator_revenue_by_day.sub_total
        , creator_revenue_by_day.processing_fee
        , row_number() over (partition by creator_fanfix_id, fan_fanfix_id, type order by time asc) as payment_occurance_rank_by_pair 
    FROM CREATOR_REVENUE_BY_DAY
    LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} creator ON creator.user_id = creator_fanfix_id
    LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} fan ON fan.user_id = fan_fanfix_id
    ),
name_resolved_revenue_media_unlock AS (
    SELECT
        r.charge_id
        , r.entity_id::varchar as entity_id
        , r.type
        , CASE
            WHEN m.message_blast_id IS NOT NULL AND m.message_blast_id <> '' THEN 'media unlock - message blast'
            ELSE 'media unlock - direct message'
        END AS revenue_category_detail
    FROM name_resolved_revenue r
    INNER JOIN {{ ref('stg_fanfix_messaging_messages') }} m on m.message_id::varchar = r.entity_id::varchar
    WHERE type = 'media unlock'
),
name_resolved_revenue_livestream_unlock AS (
    SELECT
        r.charge_id
        , r.entity_id::varchar as entity_id
        , r.type
        , CASE
            WHEN l.livestream_type = 'call' THEN 'livestream unlock - 1:1 call'
            ELSE'livestream unlock - regular'
        END AS revenue_category_detail
    FROM name_resolved_revenue r
    INNER JOIN {{ ref('stg_fanfix_livestream_livestreams') }} l on l.livestream_id::varchar = r.entity_id::varchar
    WHERE type = 'livestream unlock'
),
name_resolved_revenue_subscription AS (
    SELECT
        r.charge_id
        , r.entity_id::varchar AS entity_id
        , r.type
        , CASE 
            when (type = 'subscription creation' or (type = 'subscription charge' and  payment_occurance_rank_by_pair = 1)) then 'subscription - new'
            WHEN (type = 'subscription charge' and payment_occurance_rank_by_pair > 1) or type = 'subscription update' or type = 'subscription renewal' then 'subscription - renewal'
            ELSE revenue_category
        END AS revenue_category_detail
    FROM name_resolved_revenue r
    WHERE type LIKE 'subscription%'
),
dispute_counts AS (
    SELECT 
        dispute_id
        , charge_id
        , created_at AS dispute_created_time
        , reason
        , status
        , row_number() over (partition by charge_id ORDER BY created_at desc) AS dispute_rank
    FROM {{ ref('stg_stripe_disputes') }}
),
dedupped_disputes AS (
    SELECT 
        *
    FROM DISPUTE_COUNTS
    WHERE dispute_rank = 1
)
SELECT 
    r.*
    , COALESCE(d.revenue_category_detail, r.revenue_category) as revenue_category_detail
    , dispute_id
    , dispute_created_time
    , reason
    , status
FROM NAME_RESOLVED_REVENUE r
LEFT JOIN (
    SELECT * FROM name_resolved_revenue_media_unlock
    UNION 
    SELECT * FROM name_resolved_revenue_livestream_unlock
    UNION
    SELECT * FROM name_resolved_revenue_subscription
) d ON r.charge_id = d.charge_id
LEFT JOIN DEDUPPED_DISPUTES dd ON dd.charge_id = r.charge_id
WHERE account_id is NOT null
ORDER BY time DESC
