SELECT
    p.payment_processor
    , p.payment_processor_transaction_id
    , p.tip_type
    , p.creator_id
    , p.fan_id
    , p.amount AS sales_amount
    , p.sales_tax_amount AS tax_amount
    , p.application_fee
    , p.processing_fee
    , p.sales_tax_billing_location
    , p.sales_tax_countryCode
    , p.sales_tax_region
    , p.sales_tax_zip
    , c.card_type
    , p.created_at
FROM {{ ref('stg_fanfix_payment_public_payments') }} p
LEFT JOIN {{ ref('stg_fanfix_payment_public_cards') }} c ON c.card_id = p.card_id
-- WHERE tip_type != '<nil>'
-- WHERE sales_tax_amount_in_cents> 0 AND REPLACE(try_parse_json(SALES_TAX_BILLING_LOCATION):region, '"','') is NOT null AND created_at > '2023-08-30'
ORDER BY 1 DESC