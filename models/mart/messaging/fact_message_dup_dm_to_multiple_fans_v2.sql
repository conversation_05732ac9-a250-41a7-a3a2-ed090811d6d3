WITH params AS (
    SELECT
        10  AS suspected_same_content_min_sends,     -- >=10 identical sends
        10  AS suspected_same_content_max_minutes,   -- within 10 minutes
        20  AS suspected_unique_recipients_day,      -- >=20 unique recipients/day
        30  AS suspected_total_sends_day,            -- >=30 sends/day
        10  AS min_content_len                       -- exclude ultra-short content
),

-- 1) Base message set (non-greeting, non-media)
base AS (
    SELECT
        m.message_id,
        m.sender_user_id AS creator_id,
        u.fanfix_username,
        u.account_management_agency,
        m.recipient_user_id,
        m.created_at,
        m.created_at::date AS msg_date,
        m.content,
        m.message_blast_id,
        -- normalization: lower, trim, collapse whitespace, remove common punctuation
        REGEXP_REPLACE(
            REGEXP_REPLACE(LOWER(TRIM(m.content)), '\\s+', ' '),
            '[\\p{Punct}]',
            ''
        ) AS content_norm
    FROM {{ ref('stg_fanfix_messaging_messages') }} m
    INNER JOIN {{ ref('dim_creator_profile') }} u ON m.sender_user_id = u.fanfix_id
    LEFT JOIN {{ ref('stg_fanfix_greeting_messages') }} gm ON m.content = gm.text and m.sender_user_id = gm.creator_id
    WHERE 1=1
        AND gm.id IS NULL
        AND m.content IS NOT NULL
        AND LENGTH(TRIM(m.content)) >= (SELECT min_content_len FROM params)
        AND m.created_at::date >= '2024-06-01'
        AND m.content NOT IN ('Media sent','Media Sent','Voice note sent','Test Message')
),

-- 2) Blast templates per creator (normalized)
blast_templates AS (
    SELECT
        b.message_blast_id,
        b.creator_id,
        b.text AS blast_text,
        REGEXP_REPLACE(
            REGEXP_REPLACE(LOWER(TRIM(b.text)), '\\s+', ' '),
            '[\\p{Punct}]',
            ''
        ) AS blast_text_norm
    FROM {{ ref('stg_fanfix_messaging_message_blast') }} b
    WHERE b.text IS NOT NULL
),

-- 3) Enrich base messages with blast template match signals
enriched AS (
    SELECT
        b.*,
        bt.blast_id AS matched_blast_id,
        IFF(bt.blast_id IS NOT NULL, 1, 0) AS is_blast_template_match
    FROM base b
    LEFT JOIN blast_templates bt ON b.creator_id = bt.creator_id AND b.content_norm = bt.blast_text_norm
),

-- 4) Behavior metrics: per creator/day, and per creator/content/day
creator_day AS (
    SELECT
        creator_id,
        msg_date,
        COUNT(*) AS total_sends_day,
        COUNT(DISTINCT recipient_user_id) AS unique_recipients_day
    FROM enriched
    GROUP BY 1,2
),

creator_content_day AS (
    SELECT
        creator_id,
        msg_date,
        content_norm,
        COUNT(*) AS sends_same_content_day,
        COUNT(DISTINCT recipient_user_id) AS unique_recipients_same_content_day,
        MIN(created_at) AS first_sent_at,
        MAX(created_at) AS last_sent_at,
        DATEDIFF('minute', MIN(created_at), MAX(created_at)) AS span_minutes
    FROM enriched
    GROUP BY 1,2,3
),

final AS (
    SELECT
        e.creator_id,
        e.fanfix_username,
        e.account_management_agency,
        e.msg_date,
        e.created_at,
        e.content,
        e.message_blast_id,
        e.matched_blast_id,
        cd.total_sends_day,
        cd.unique_recipients_day,
        ccd.sends_same_content_day,
        ccd.unique_recipients_same_content_day,
        ccd.span_minutes,
        -- velocity guard (avoid div by 0)
        (ccd.sends_same_content_day / NULLIF(GREATEST(ccd.span_minutes, 1), 0)) AS sends_per_min_same_content,

        CASE
            -- A) proper blast
            WHEN e.message_blast_id IS NOT NULL
            AND e.message_blast_id <> '<nil>'
            THEN 'proper_blast'

            -- B) blast-like manual (template match but missing blast_id)
            WHEN (e.message_blast_id IS NULL OR e.message_blast_id = '<nil>')
            AND e.matched_blast_id IS NOT NULL
            THEN 'blast_like_manual'

            -- C) suspected manual (behavior-based; conservative)
            WHEN (e.message_blast_id IS NULL OR e.message_blast_id = '<nil>')
            AND (
                    (
                    ccd.sends_same_content_day >= (SELECT suspected_same_content_min_sends FROM params)
                    AND ccd.span_minutes <= (SELECT suspected_same_content_max_minutes FROM params)
                    )
                    OR
                    (
                    cd.unique_recipients_day >= (SELECT suspected_unique_recipients_day FROM params)
                    AND cd.total_sends_day >= (SELECT suspected_total_sends_day FROM params)
                    )
            )
            THEN 'suspected_manual'

            -- D) normal dm
            ELSE 'normal_dm'
        END AS classification

    FROM enriched e
    LEFT JOIN creator_day cd ON e.creator_id = cd.creator_id AND e.msg_date = cd.msg_date
    LEFT JOIN creator_content_day ccd ON e.creator_id = ccd.creator_id AND e.msg_date = ccd.msg_date AND e.content_norm = ccd.content_norm
)

SELECT *
FROM final
-- Optional: focus on the interesting buckets first
-- WHERE classification IN ('blast_like_manual','suspected_manual','proper_blast')
ORDER BY msg_date DESC, 
    account_management_agency, 
    fanfix_username, 
    classification, 
    sends_same_content_day DESC