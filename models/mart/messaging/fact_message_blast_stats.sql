WITH b AS(
    SELECT
        b.message_blast_id
        , b.text AS content
        , b.sent_user_count
        , SUM(CASE
                WHEN m.unlocked_at IS NOT NULL THEN 1
                ELSE 0
            END
        ) AS unblock_count
    FROM {{ ref('stg_fanfix_messaging_message_blast') }} b
    INNER JOIN {{ ref('stg_fanfix_messaging_messages') }} m ON b.message_blast_id = m.message_blast_id
    WHERE b.sent_user_count > 0
    GROUP BY 1, 2, 3
),
content_urls as (
    select 
        b.message_blast_id, 
        fa.mime_type as media_type, 
        fa.original_url
    from b
    INNER JOIN {{ ref('stg_fanfix_messaging_message_blast_assets') }} ba ON b.message_blast_id = ba.message_blast_id
    INNER JOIN {{ ref('stg_fanfix_assets') }} fa ON fa.asset_id = ba.asset_id
),
list_url_by_blast as (
    select 
        message_blast_id, 
        listagg(original_url) within group (order by media_type desc) as blast_image_urls
    from content_urls
    group by 1
)
SELECT DISTINCT
    m.created_at::date AS date
    , creator_contact.agent_id
    , creator_contact.lead_source AS lead_source
    , creator_contact.referrer_name AS referrer_name
    , creator_contact.account_management_agency AS account_management_agency
    , u.username as creator_username
    , b.content AS content
    , ub.blast_image_urls
    , b.message_blast_id
    , b.sent_user_count
    , b.unblock_count
    , ROUND(b.unblock_count/b.sent_user_count * 100, 3) AS unblock_rate
    , ROUND(m.UNLOCK_PRICE_IN_CENTS / 100.0 , 2) AS unblock_price
    , LISTAGG(DISTINCT f.username, ', ')  AS unblocked_fan_list
FROM {{ ref('stg_fanfix_messaging_messages') }} m
INNER JOIN {{ ref('stg_fanfix_user_profiles') }} u ON m.sender_user_id = u.user_id
LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} f ON m.recipient_user_id = f.user_id
INNER JOIN b ON b.message_blast_id = m.message_blast_id
INNER JOIN {{ ref('stg_hubspot_contacts') }} creator_contact ON m.sender_user_id = creator_contact.fanfix_id
INNER JOIN list_url_by_blast ub on ub.message_blast_id = b.message_blast_id
WHERE
    m.payment_amount > 0
    AND m.message_blast_id <> '<nil>' AND m.message_blast_id IS NOT NULL
GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13
ORDER BY
    2 ASC
    , 3 ASC
    , 6 ASC
    , 1 ASC
    , 7 ASC