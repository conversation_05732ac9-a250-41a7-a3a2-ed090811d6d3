
-- 4) Behavior metrics: per creator/day, and per creator/content/day
WITH creator_day AS (
    SELECT
        creator_id
        , msg_date
        , COUNT(*) AS total_sends_day
        , COUNT(DISTINCT recipient_user_id) AS unique_recipients_day
    FROM ref {{ ref('fact_message_dup_dm_to_multiple_fans') }}
    GROUP BY 1,2
),

creator_content_day AS (
    SELECT
        creator_id,
        msg_date,
        content_norm,
        COUNT(*) AS sends_same_content_day,
        COUNT(DISTINCT recipient_user_id) AS unique_recipients_same_content_day,
        MIN(created_at) AS first_sent_at,
        MAX(created_at) AS last_sent_at,
        DATEDIFF('minute', MIN(created_at), MAX(created_at)) AS span_minutes
    FROM ref {{ ref('fact_message_dup_dm_to_multiple_fans') }}
    GROUP BY 1,2,3
),

final_stats AS (
    SELECT
        e.creator_id
        , e.fanfix_username
        , e.account_management_agency
        , e.msg_date
        , e.created_at
        , e.content
        , e.matched_blast_id
        , cd.total_sends_day
        , cd.unique_recipients_day
        , ccd.sends_same_content_day
        , ccd.unique_recipients_same_content_day
        , ccd.span_minutes
        -- velocity guard (avoid div by 0)
        , (ccd.sends_same_content_day / NULLIF(GREATEST(ccd.span_minutes, 1), 0)) AS sends_per_min_same_content

        , CASE
            -- -- A) proper blast
            -- WHEN e.message_blast_id IS NOT NULL AND e.message_blast_id <> '<nil>' THEN 'proper_blast'

            -- B) blast-like manual (template match but missing blast_id)
            WHEN (e.message_blast_id IS NULL OR e.message_blast_id = '<nil>') AND e.matched_blast_id IS NOT NULL THEN 'blast_like_manual'

            -- C) suspected manual (behavior-based; conservative)
            WHEN (e.message_blast_id IS NULL OR e.message_blast_id = '<nil>')
            AND (
                    (
                    ccd.sends_same_content_day >= (SELECT suspected_same_content_min_sends FROM params)
                    AND ccd.span_minutes <= (SELECT suspected_same_content_max_minutes FROM params)
                    )
                    OR
                    (
                    cd.unique_recipients_day >= (SELECT suspected_unique_recipients_day FROM params)
                    AND cd.total_sends_day >= (SELECT suspected_total_sends_day FROM params)
                    )
            )
            THEN 'suspected_manual'

            -- D) normal dm
            ELSE 'normal_dm'
        END AS classification

    FROM ref {{ ref('fact_message_dup_dm_to_multiple_fans') }} e
    LEFT JOIN creator_day cd ON e.creator_id = cd.creator_id AND e.msg_date = cd.msg_date
    LEFT JOIN creator_content_day ccd ON e.creator_id = ccd.creator_id AND e.msg_date = ccd.msg_date AND e.content_norm = ccd.content_norm
)

SELECT 
    *
FROM final_stats
-- Optional: focus on the interesting buckets first
-- WHERE classification IN ('blast_like_manual','suspected_manual','proper_blast')
ORDER BY msg_date DESC, 
    account_management_agency, 
    fanfix_username, 
    classification, 
    sends_same_content_day DESC