with outgoing_messages as (
    select
        date_trunc(month, m.created_at) as month, 
        m.SENDER_USER_ID as creator_user_id,
        count(distinct m.message_id) as outgoing_msg,
        count(distinct case when gm.id is null then m.message_id end) as outgoing_msg_without_gm
    from {{ ref('stg_fanfix_messaging_messages') }} m
    join {{ ref('stg_fanfix_user_users') }} u
        on m.SENDER_USER_ID = u.user_ID
    left join {{ ref('stg_fanfix_greeting_messages') }} gm
        on m.content = gm.text and m.sender_user_id = gm.creator_id
    where u.ROLES = '["creator"]'
        and (m.MESSAGE_BLAST_ID is null or m.MESSAGE_BLAST_ID = '<nil>')
    group by 1, 2
), incoming_messages as (
    select
        date_trunc(month, m.created_at) as month, 
        m.RECIPIENT_USER_ID as creator_user_id,
        count(distinct m.message_id) as incoming_msg
    from {{ ref('stg_fanfix_messaging_messages') }} m
    join {{ ref('stg_fanfix_user_users') }} u
        on m.RECIPIENT_USER_ID = u.user_ID
    where u.ROLES = '["creator"]'
        and (m.MESSAGE_BLAST_ID is null or m.MESSAGE_BLAST_ID = '<nil>')
    group by 1, 2
), outgoing_incoming_messages as (
    select
        o.month, 
        o.creator_user_id, 
        o.outgoing_msg,
        o.outgoing_msg_without_gm,
        i.incoming_msg,
        round(outgoing_msg / incoming_msg, 2) as outgoing_incoming_msg_ratio,
        round(outgoing_msg_without_gm / incoming_msg, 2) as outgoing_incoming_msg_ratio_without_gm,
    from outgoing_messages o
    left join incoming_messages i
    on o.month = i.month and o.creator_user_id = i.creator_user_id
), message_flows AS (
    SELECT 
        m.message_id,
        m.CREATED_AT,
        m.SENDER_USER_ID,
        m.RECIPIENT_USER_ID,
        DATE_TRUNC('month', m.CREATED_AT) as month,
        CASE 
            WHEN sender_u.ROLES = '["creator"]' THEN m.SENDER_USER_ID
            WHEN recipient_u.ROLES = '["creator"]' THEN m.RECIPIENT_USER_ID
            ELSE NULL
        END as creator_user_id,
        CASE 
            WHEN sender_u.ROLES = '["creator"]' THEN 'creator'
            WHEN sender_u.ROLES = '["fan"]' THEN 'fan'
            ELSE 'unknown'
        END as sender_type,
        -- message channel alternative
        CONCAT(LEAST(m.SENDER_USER_ID, m.RECIPIENT_USER_ID), '_', GREATEST(m.SENDER_USER_ID, m.RECIPIENT_USER_ID)) as conversation_id,
        ROW_NUMBER() OVER (PARTITION BY CONCAT(LEAST(m.SENDER_USER_ID, m.RECIPIENT_USER_ID), '_', GREATEST(m.SENDER_USER_ID, m.RECIPIENT_USER_ID)) ORDER BY m.CREATED_AT)
        as message_sequence
    FROM {{ ref('stg_fanfix_messaging_messages') }} m
    JOIN {{ ref('stg_fanfix_user_users') }} sender_u 
        ON m.SENDER_USER_ID = sender_u.user_ID
    JOIN {{ ref('stg_fanfix_user_users') }} recipient_u 
        ON m.RECIPIENT_USER_ID = recipient_u.user_ID
    LEFT JOIN {{ ref('stg_fanfix_greeting_messages') }} gm
        ON m.content = gm.text and m.sender_user_id = gm.creator_id
    WHERE (m.MESSAGE_BLAST_ID is null or m.MESSAGE_BLAST_ID = '<nil>')
        AND gm.id is null
        -- AND (sender_u.ROLES like '%creator%' OR recipient_u.ROLES like '%creator%')
), fan_messages AS (
    SELECT 
    *
    FROM message_flows 
    WHERE sender_type = 'fan'
        AND creator_user_id IS NOT NULL
), creator_responses AS (
    SELECT 
        fm.creator_user_id,
        fm.conversation_id,
        fm.created_at as fan_message_time,
        fm.month,
        MIN(mf.CREATED_AT) as creator_response_time,
        MIN(mf.message_sequence) as response_sequence
    FROM fan_messages fm
    LEFT JOIN message_flows mf 
        ON fm.conversation_id = mf.conversation_id
        AND fm.creator_user_id = mf.creator_user_id
        AND mf.sender_type = 'creator'
        AND mf.message_sequence > fm.message_sequence
        AND mf.CREATED_AT > fm.created_at
    GROUP BY 1,2,3,4
), response as (
    SELECT 
        month,
        creator_user_id,
        COUNT(*) as total_fan_messages,
        COUNT(creator_response_time) as responded_messages,
        ROUND(COUNT(creator_response_time) / NULLIF(COUNT(*), 0), 2) as response_rate,
        ROUND(AVG(
            CASE 
                WHEN creator_response_time IS NOT NULL 
                THEN DATEDIFF('second', fan_message_time, creator_response_time) / 3600.0
            END
        ), 2) as avg_response_time_hours,
        ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY 
            CASE 
                WHEN creator_response_time IS NOT NULL 
                THEN DATEDIFF('second', fan_message_time, creator_response_time) / 3600.0
            END
        ), 2) as median_response_time_hours,
        ROUND(COUNT(CASE WHEN DATEDIFF('minute', fan_message_time, creator_response_time) <= 10 THEN 1 END) / NULLIF(COUNT(*), 0), 2) as responses_within_10min,
        ROUND(COUNT(CASE WHEN DATEDIFF('minute', fan_message_time, creator_response_time) <= 30 THEN 1 END) / NULLIF(COUNT(*), 0), 2) as responses_within_30min,
        ROUND(COUNT(CASE WHEN DATEDIFF('hour', fan_message_time, creator_response_time) <= 1 THEN 1 END) / NULLIF(COUNT(*), 0), 2) as responses_within_1hour,
        ROUND(COUNT(CASE WHEN DATEDIFF('day', fan_message_time, creator_response_time) <= 1 THEN 1 END) / NULLIF(COUNT(*), 0), 2) as responses_within_1day
    FROM creator_responses
    GROUP BY 1,2
)
, active_days as (
    select
        month,
        creator_user_id,
        count(distinct date_trunc(day, created_at)) as active_days
    from message_flows
    where sender_type = 'creator'
        and creator_user_id is not null
    group by 1, 2
)
select 
    oi.month,
    oi.creator_user_id,
    coalesce(ad.active_days, 0) as active_days,
    oi.outgoing_msg,
    oi.outgoing_msg_without_gm,
    oi.incoming_msg,
    oi.outgoing_incoming_msg_ratio,
    oi.outgoing_incoming_msg_ratio_without_gm,
    coalesce(r.total_fan_messages, 0) as total_fan_messages,
    coalesce(r.responded_messages, 0) as responded_messages,
    coalesce(r.response_rate, 0) as response_rate,
    coalesce(r.avg_response_time_hours,0) as avg_response_time_hours,
    coalesce(r.median_response_time_hours,0) as median_response_time_hours,
    coalesce(r.responses_within_10min, 0) as responses_within_10min,
    coalesce(r.responses_within_30min, 0) as responses_within_30min,
    coalesce(r.responses_within_1hour, 0) as responses_within_1hour,
    coalesce(r.responses_within_1day, 0) as responses_within_1day
from outgoing_incoming_messages oi
left join response r 
    on r.month = oi.month and r.creator_user_id = oi.creator_user_id
left join active_days ad
    on r.month = ad.month and r.creator_user_id = ad.creator_user_id
order by 1 desc, 3 desc