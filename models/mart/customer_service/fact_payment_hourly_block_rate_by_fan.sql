WITH block_rate AS (
    SELECT date_trunc('hour',c.created_at) AS hourly,
            c.customer_id,
            cm.value AS fan_fanfix_id,
            COUNT(DISTINCT charge_id) AS total_payment_count,
            COUNT(DISTINCT CASE WHEN  outcome_type = 'blocked' THEN charge_id ELSE null END) AS blocked_payment_count,
            COUNT(DISTINCT CASE WHEN  outcome_type = 'blocked' THEN charge_id ELSE null END)/COUNT(DISTINCT charge_id) AS block_rate
    FROM {{ref("stg_stripe_charges")}}  c
    LEFT JOIN (SELECT * FROM {{ref("stg_stripe_customers_metadata")}} WHERE key = 'appUserId') cm ON c.customer_id = cm.customer_id
    GROUP BY 1,2,3
)
SELECT hourly, customer_id, fan_fanfix_id, fan.username AS fan_username, total_payment_count, blocked_payment_count, block_rate,
        CASE WHEN( block_rate >= 0.35 AND blocked_payment_count >= 10) THEN True ELSE false END AS high_risk_flag
FROM BLOCK_RATE br
LEFT JOIN {{ref("stg_fanfix_user_users")}} fan ON fan.user_id = fan_fanfix_id