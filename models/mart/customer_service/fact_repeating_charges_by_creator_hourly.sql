WITH charges_on_hourly AS(
    SELECT date_trunc('hour', time) AS hour,
            c.*
    FROM {{ref("fact_creator_charges")}} c
),
charges_with_fan_fraud_flag AS (
   SELECT c.hour, c.charge_id,
            c.creator_fanfix_id, c.creator_username,
            c.fan_fanfix_id, c.fan_username,
            CASE WHEN (rc.fan_fanfix_id is NOT null AND high_risk_flag = True )THEN True ELSE False END AS fraud_fan_account_flag,
            c.type, c.gross_revenue
    FROM CHARGES_ON_HOURLY c
    LEFT JOIN {{ref("fact_repeating_charges_by_fan_hourly")}}  rc
    ON c.hour = rc.hour AND c.fan_fanfix_id = rc.fan_fanfix_id AND c.type = rc.type AND c.gross_revenue = rc.payment_amount_per_charge
),
hourly_aggregated_charges AS (
    SELECT  hour,
            creator_fanfix_id, creator_username,
            gross_revenue AS payment_amount_per_charge, type, --card_id,
            COUNT(*) AS payment_count,
            SUM(GROSS_REVENUE) AS total_amount,
            COUNT(CASE WHEN fraud_fan_account_flag = True THEN charge_id ELSE null END) AS potential_fraud_payment_count,
            SUM(CASE WHEN fraud_fan_account_flag = True THEN gross_revenue ELSE 0 END) AS potential_fraud_payment_amount,

            LISTAGG(DISTINCT fan_fanfix_id, ' , ') WITHIN GROUP (ORDER BY fan_fanfix_id) AS suspicious_fan_id_list,
            LISTAGG(DISTINCT fan_username, ' , ') WITHIN GROUP (ORDER BY fan_username) AS suspicious_fan_username_list
    FROM CHARGES_WITH_FAN_FRAUD_FLAG
    GROUP BY 1,2,3,4,5
)
SELECT c.* , cp.deal_creator, cp.account_management_agency,
    CASE
        WHEN ((potential_fraud_payment_count >= 10 ) AND (type = 'post' OR type = 'creator tip' or type like '%livestream%')) THEN 'high' -- post tip AND creator tip are more likely to be fraud
        WHEN ((potential_fraud_payment_count >= 10 ) AND (type != 'post' AND type != 'creator tip')) THEN 'low'
        ELSE 'no risk'
    END AS risk_level
FROM HOURLY_AGGREGATED_CHARGES c
LEFT JOIN {{ref("dim_creator_profile")}} cp ON cp.fanfix_id = creator_fanfix_id
ORDER BY hour desc