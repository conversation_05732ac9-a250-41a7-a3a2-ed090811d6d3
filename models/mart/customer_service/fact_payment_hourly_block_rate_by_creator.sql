WITH block_rate AS (
    SELECT date_trunc('hour', charges.created_at) AS hourly,
            destination_id,
            am.value AS creator_fanfix_id,
            COUNT(DISTINCT charge_id) AS total_payment_count,
            COUNT(DISTINCT CASE WHEN  outcome_type = 'blocked' THEN charge_id ELSE null END) AS blocked_payment_count,
            COUNT(DISTINCT CASE WHEN  outcome_type = 'blocked' THEN charge_id ELSE null END)/COUNT(DISTINCT charge_id) AS block_rate
    FROM {{ref("stg_stripe_charges")}} charges
    LEFT JOIN (SELECT * FROM {{ref("stg_stripe_connected_accounts_metadata")}} WHERE key = 'id') am ON am.account_id = charges.destination_id
    GROUP BY 1,2,3
)
SELECT hourly, destination_id AS stripe_account_id,
        creator_fanfix_id, creator.username AS creator_fanfix_username, cp.deal_creator, cp.account_management_agency,
        total_payment_count, blocked_payment_count, block_rate,
        CASE WHEN( block_rate >= 0.3 AND blocked_payment_count >= 20) THEN True ELSE false END AS high_risk_flag
FROM BLOCK_RATE br
LEFT JOIN {{ref("stg_fanfix_user_users")}} creator ON creator.user_id = creator_fanfix_id
LEFT JOIN {{ref("dim_creator_profile")}} cp ON cp.fanfix_id = creator_fanfix_id