SELECT
    date_trunc('day', c.reverse_payment_time)::date as chargeback_day
    , c.creator_fanfix_id
    , c.creator_username
    , creator_contact.agent_id 
    , creator_contact.lead_source
    , creator_contact.referrer_name
    , creator_contact.account_management_agency
    , sum(c.gross_revenue) as total_gross_chargeback
    , sum(c.net_revenue) as total_net_chargeback
    , sum(c.external_reporting_gross_revenue) as total_external_facing_gross_chargeback 
FROM {{ ref('fact_creator_chargeback') }} c
INNER JOIN {{ ref('stg_hubspot_contacts') }} creator_contact ON creator_contact.fanfix_id = c.creator_fanfix_id
GROUP BY 
    date_trunc('day', c.reverse_payment_time)::date
    , c.creator_fanfix_id
    , c.creator_username
    , creator_contact.agent_id 
    , creator_contact.lead_source
    , creator_contact.referrer_name
    , creator_contact.account_management_agency
