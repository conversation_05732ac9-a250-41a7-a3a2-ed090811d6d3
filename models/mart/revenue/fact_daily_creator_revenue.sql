SELECT
    charges.charge_id
    , charges.time
    , CASE
        WHEN type LIKE 'subscription%' THEN 'subscription'
        ELSE type
    END AS type
    , charges.creator_fanfix_id
    , charges.creator_username
    , charges.fan_fanfix_id
    , charges.fan_username
    , creator_contact.agent_id
    , creator_contact.lead_source
    , creator_contact.referrer_name
    , creator_contact.account_management_agency
    , creator_contact.n3rd_party_management
    , creator_contact.prospected_by AS prospected_by_id
    , charges.sales_tax
    , charges.platform_fee
    , charges.net_revenue
    , charges.external_reporting_gross_revenue AS gross_revenue
FROM {{ ref('fact_creator_charges') }} charges
INNER JOIN {{ ref('stg_hubspot_contacts') }} creator_contact ON creator_contact.fanfix_id = charges.creator_fanfix_id
ORDER BY 4, 2