SELECT
    CHARGES.charge_id
    , CHARGES.time
    , CASE
        WHEN type like 'subscription%' THEN 'subscription'
        ELSE type
    END AS type
    , CHARGES.creator_fanfix_id
    , CHARGES.creator_username
    , CHARGES.fan_fanfix_id
    , CHARGES.fan_username
    , creator_contact.agent_id
    , creator_contact.lead_source
    , creator_contact.referrer_name
    , creator_contact.account_management_agency
    , creator_contact.n3rd_party_management
    , creator_contact.prospected_by AS prospected_by_id
    , CHARGES.sales_tax
    , CHARGES.platform_fee
    , CHARGES.net_revenue
    , CHARGES.external_reporting_gross_revenue AS gross_revenue
FROM {{ ref('fact_creator_charges') }} CHARGES
INNER JOIN {{ ref('stg_hubspot_contacts') }} creator_contact ON creator_contact.fanfix_id = CHARGES.creator_fanfix_id
ORDER BY
    4,
    2