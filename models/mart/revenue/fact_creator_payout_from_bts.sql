{{ config(
    materialized='table',
    pre_hook="SET (TIMEZONE, START_DATE, END_DATE) = ('{{ var('TIMEZONE') }}', '{{ var('START_DATE') }}', '{{ var('END_DATE') }}')"
) }}

SELECT
    cam.value AS fanfix_id
    , cp.fanfix_username
    , cpi.payout_id AS balance_transfer_join_id
    , bts.source_id AS payout_id
    , cpi.payout_status
    , bts.total_with_tax_and_processing_fee
    , bts.total
    , bts.net
    , bts.currency
    , payout_expected_arrival_date as payout_date
FROM  {{ ref('stg_stripe_connected_account_balance_transactions') }} bts
LEFT JOIN {{ ref('stg_stripe_financial_report_connect_payouts_itemized') }} cpi on cpi.payout_id = bts.automatic_transfer_id
LEFT JOIN (
        SELECT * 
        FROM {{ ref('stg_stripe_connected_accounts_metadata') }} 
        WHERE key = 'id'
        ) cam ON cam.account_id = bts.account
LEFT JOIN {{ ref('dim_creator_profile') }} cp ON cam.value = cp.fanfix_id
WHERE 1 = 1
    AND (bts.reporting_category = 'payout' OR bts.reporting_category = 'payout_reversal' OR bts.reporting_category = 'transfer')
    AND cpi.payout_id IS NOT NULL
    AND cpi.payout_status = 'paid'