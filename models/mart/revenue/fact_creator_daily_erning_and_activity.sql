SELECT 
    CASE 
        WHEN e.creator_id is NOT null THEN e.creator_id 
        ELSE a.creator_id 
    END AS creator_id,
    CASE 
        WHEN e.username is NOT null THEN e.username 
        ELSE a.username 
    END AS username,
    CASE 
        WHEN e.day_created is NOT null THEN e.day_created 
        ELSE a.created_day 
    END AS day_created,
    e.total_revenue as gross_total_revenue,
    e.subscriptions_revenue as gross_subscriptions_revenue,
    e.tips_revenue as gross_tips_revenue,
    e.media_unlock_revenue as gross_media_unlock_revenue,
    e.post_unlock_revenue as gross_post_unlock_revenue,
    e.livestream_unlock_revenue AS gross_livestream_unlock_revenue,

    e.net_total_revenue,
    e.net_subscriptions_revenue,
    e.net_tips_revenue,
    e.net_media_unlock_revenue,
    e.net_post_unlock_revenue,
    e.net_livestream_unlock_revenue,

    a.post_count,
    a.blast_count,
    a.outgoing_dm_count
FROM {{ ref('fact_daily_creator_earnings') }} e
FULL OUTER JOIN {{ ref('fact_daily_creator_activity') }} a ON a.creator_id  = e.creator_id AND e.day_created = a.created_day