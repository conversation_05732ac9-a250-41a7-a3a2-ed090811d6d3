WITH transfer_reversal AS (
    SELECT 
        transfer_reversal_created_time AS reverse_payment_time,
        tr.charge_id,
        tr.charge_amount,
        tr.transfer_reversal_id,
        r.refund_id,
        CASE WHEN refund_id is NOT null THEN 'refund' ELSE transfer_reversal_type END AS reverse_payment_type
    FROM {{ ref('fact_transfer_reversal') }} tr
    LEFT JOIN {{ ref('fact_successful_refunds') }} r ON r.charge_id = tr.charge_id
)
SELECT rp.*,
    c.type, c.account_id, c.creator_fanfix_id, c.creator_username, c.fan_fanfix_id, c.fan_username,
    sales_tax, total_fee, platform_fee, net_revenue, gross_revenue, sub_total, processing_fee, external_reporting_gross_revenue
FROM TRANSFER_REVERSAL rp
LEFT JOIN {{ ref('fact_creator_charges') }} c ON rp.charge_id = c.charge_id
;