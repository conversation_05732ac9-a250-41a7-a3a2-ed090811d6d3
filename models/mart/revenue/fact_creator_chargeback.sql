WITH transfer_reversal AS (
    SELECT 
        transfer_reversal_created_time AS reverse_payment_time,
        tr.charge_id,
        tr.charge_amount,
        tr.transfer_reversal_id,
        r.refund_id,
        CASE 
            WHEN refund_id IS NOT NULL THEN 'refund' 
            ELSE transfer_reversal_type 
        END AS reverse_payment_type
    FROM {{ ref('fact_transfer_reversal') }} tr
    LEFT JOIN {{ ref('fact_successful_refunds') }} r ON r.charge_id = tr.charge_id
)
SELECT 
    rp.*,
    c.type, 
    c.account_id, 
    c.creator_fanfix_id, 
    c.creator_username, 
    c.fan_fanfix_id, 
    c.fan_username,
    c.sales_tax, 
    c.total_fee, 
    c.platform_fee, 
    c.net_revenue, 
    c.gross_revenue, 
    c.sub_total, 
    c.processing_fee, 
    c.external_reporting_gross_revenue,
    c.stripe_gross_revenue
FROM transfer_reversal rp
LEFT JOIN {{ ref('fact_creator_charges') }} c ON rp.charge_id = c.charge_id