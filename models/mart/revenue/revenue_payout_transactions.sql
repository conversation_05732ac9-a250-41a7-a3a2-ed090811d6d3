--TODO: update table sources
WITH pi_zip AS (
    SELECT
        payment_intent_id
        , merchant_id
        , value
    FROM {{ ref('stg_stripe_payment_intents_metadata') }}
    WHERE key = 'zip'
),
pi_region AS (
    SELECT
        payment_intent_id
        , merchant_id
        , value
    FROM {{ ref('stg_stripe_payment_intents_metadata') }}
    WHERE key = 'region'
),
pi_country AS (
    SELECT
        payment_intent_id
        , merchant_id
        , value
    FROM {{ ref('stg_stripe_payment_intents_metadata') }}
    WHERE key = 'country'
),
sub_zip AS (
    SELECT
        subscription_id
        , merchant_id
        , value
    FROM {{ ref('stg_stripe_subscriptions_metadata') }}
    WHERE key = 'zip'
),
sub_region AS (
    SELECT
        subscription_id
        , merchant_id
        , value
    FROM {{ ref('stg_stripe_subscriptions_metadata') }}
    WHERE key = 'region'
),
sub_country AS (
    SELECT
        subscription_id
        , merchant_id
        , value
    FROM {{ ref('stg_stripe_subscriptions_metadata') }}
    WHERE key = 'country'
),
sub_inv AS (
SELECT
    invoice_id
    , MAX(amount::float / 100.00) AS subscription_subtotal
FROM {{ ref('stg_stripe_invoice_line_items') }}
WHERE NOT contains (description, 'Subscription Sales Tax')
GROUP BY invoice_id
),
sub_inv_tax AS (
SELECT
    invoice_id
    , MAX(amount::float / 100.00) AS subscription_tax
FROM {{ ref('stg_stripe_invoice_line_items') }}
WHERE contains(description, 'Subscription Sales Tax')
GROUP BY invoice_id
)
SELECT DISTINCT
    charges.created_at AS charge_timestamp
    , invoices.invoice_date
    , charges.charge_id
    , charges.payment_intent AS payment_intent_id
    , charges.invoice_id
    , charges.customer_id
    , connected_accounts_metadata.value AS account_id -- creator fanfix_id
    , connected_accounts.connected_account_id AS "connected account"
    , user.username
    , customers_metadata.value AS fan_fanfix_id
    , charges.description AS description
    , CASE
        WHEN REGEXP_SUBSTR(LOWER(charges.description), '(invoice)', 1) = 'invoice' THEN 'subscription'
        ELSE REGEXP_SUBSTR(LOWER(charges.description),
        '(subscription|creator tip|media unlock|message|post unlock|post|publication|livestream unlock|livestream)',

            1
        )
    END AS revenue_stream
    -- , p.tip_type AS revenue_stream
    -- , p.payment_processor
    , 'Stripe' AS payment_processor
    , CAST((charges.amount / 100) AS DECIMAL(10, 2)) AS total
    , COALESCE(tax.tax_amount, 0) AS sales_tax
    , CAST((charges.amount / 100) - COALESCE(tax.tax_amount, 0) - COALESCE(tax.processing_fee, 0) AS DECIMAL(10, 2)) AS sub_total
    , CAST((charges.amount / 100) - COALESCE(application_fees.amount / 100, 0) AS DECIMAL(10, 2)) AS creator_payout -- net revenue for agency
    , CAST(COALESCE(application_fees.amount / 100, 0) AS DECIMAL(10, 3)) AS total_fee
    , CASE
        WHEN tax.application_fee IS NOT NULL AND tax.application_fee > 0 THEN tax.application_fee
        ELSE GREATEST(CAST((application_fees.amount / 100) - COALESCE(tax.tax_amount, 0) AS DECIMAL(10, 2)), 0)
    END AS platform_fee -- IN prevent of missing platform fee FROM PAYMENT db. (fanfix fee)
    , COALESCE(tax.processing_fee, 0)  AS processing_fee  --(stripe fee)
    , CASE
        WHEN tax.payment_processor_transaction_id IS NULL THEN True
        ELSE NULL
    END AS "[QA] CHECK MISSING RECORDS"
    ,CASE
        WHEN ROUND((charges.amount / 100) - COALESCE(tax.tax_amount, 0) - creator_payout - platform_fee - COALESCE(tax.processing_fee, 0),0) = 0 THEN NULL
        ELSE ROUND((charges.amount / 100) - COALESCE(tax.tax_amount, 0) - creator_payout - platform_fee - COALESCE(tax.processing_fee, 0), 2)
    END AS "[QA] CHECK TOTAL"
    , CASE
        WHEN ROUND(creator_payout / ((charges.amount / 100) - COALESCE(tax.processing_fee, 0) - COALESCE(tax.tax_amount, 0)) * 100, 0) = 80 THEN NULL
        ELSE COALESCE(ROUND(creator_payout / ((charges.amount / 100) - COALESCE(tax.processing_fee, 0) - COALESCE(tax.tax_amount, 0)) * 100, 2),0)
    END AS "[QA] CHECK CREATOR PAYOUT 80%"
    , CASE
        WHEN ROUND(platform_fee /sub_total * 100, 0) = 20 THEN NULL
        ELSE COALESCE(ROUND(platform_fee /sub_total * 100, 2),0)
    END AS "[QA] CHECK PLATFORM FEE %"
    , CASE
        WHEN ROUND(COALESCE(tax.processing_fee, 0) /((charges.amount / 100) - COALESCE(tax.processing_fee, 0)) * 100, 0) = 12 THEN NULL
        ELSE COALESCE(ROUND(COALESCE(tax.processing_fee, 0) /((charges.amount / 100) - COALESCE(tax.processing_fee, 0)) * 100, 2),0)
    END AS "[QA] CHECK PROCESSING FEE %"
    , CAST((transfers.amount / 100) AS DECIMAL(10, 2)) AS transferred_total_local_cur
    , transfers.currency AS transfer_currency
    , charges.status AS charge_status
    , charges.refunded
    , charges.card_currency AS card_currency
    , COALESCE(charges.card_address_zip , pi_zip.value, sub_zip.value) AS card_zipcode
    , COALESCE(charges.card_address_state, pi_region.value, sub_region.value) AS card_state
    , COALESCE(charges.card_address_country, pi_country.value, sub_country.value, payment_methods.card_country) AS card_country
    , tax.sales_tax_region
    , tax.sales_tax_zip
    , tax.sales_tax_countryCode
    , invoices.subscription_id
    , subscriptions.status AS subscription_status
    , subscriptions.created::date AS subscription_first_created_date
    , CASE
        WHEN
            ABS(DATEDIFF(hour, invoices.status_transitions_paid_at, invoice_line_items.period_start)) <= 3
        THEN invoices.status_transitions_paid_at
        ELSE invoice_line_items.period_start
    END::date AS current_subscription_period_start
    , invoice_line_items.period_end::date AS current_subscription_period_end
FROM {{ ref('stg_stripe_charges') }} charges
LEFT JOIN {{ ref('stg_stripe_connected_accounts') }} connected_accounts ON connected_accounts.connected_account_id = charges.destination_id
LEFT JOIN {{ ref('stg_stripe_connected_accounts_metadata') }} connected_accounts_metadata ON connected_accounts_metadata.account_id = connected_accounts.connected_account_id
LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} user ON user.user_id = connected_accounts_metadata.value
LEFT JOIN {{ ref('stg_stripe_customers_metadata') }} customers_metadata ON charges.customer_id = customers_metadata.customer_id
LEFT JOIN {{ ref('stg_stripe_application_fees') }} ON application_fees.application_fee_id = charges.application_fee_id
LEFT JOIN {{ ref('stg_stripe_transfers') }} transfers ON charges.transfer_id = transfers.transfer_id
LEFT JOIN {{ ref('fact_payment_tax_collection') }} tax ON charges.payment_intent = tax.payment_processor_transaction_id
LEFT JOIN {{ ref('stg_stripe_invoices') }} invoices ON charges.invoice_id = invoices.invoice_id
LEFT JOIN (
    SELECT
        invoice_id, MAX(period_start) AS period_start, MAX(period_end) AS period_end
    FROM {{ ref('stg_stripe_invoice_line_items') }}
    GROUP BY invoice_id
) AS invoice_line_items ON invoice_line_items.invoice_id = invoices.invoice_id
LEFT JOIN {{ ref('stg_stripe_subscriptions') }} subscriptions ON invoices.subscription_id = subscriptions.subscription_id
LEFT JOIN {{ ref('stg_stripe_payment_methods') }} payment_methods ON charges.payment_method_id = payment_methods.payment_method_id
LEFT JOIN PI_ZIP ON charges.payment_intent = pi_zip.payment_intent_id
LEFT JOIN PI_COUNTRY ON charges.payment_intent = pi_country.payment_intent_id
LEFT JOIN PI_REGION ON charges.payment_intent = pi_region.payment_intent_id
LEFT JOIN SUB_ZIP ON invoices.subscription_id = sub_zip.subscription_id
LEFT JOIN SUB_COUNTRY ON invoices.subscription_id = sub_country.subscription_id
LEFT JOIN SUB_REGION ON invoices.subscription_id = sub_region.subscription_id
LEFT JOIN SUB_INV ON invoices.invoice_id = sub_inv.invoice_id
LEFT JOIN SUB_INV_TAX ON invoices.invoice_id = sub_inv_tax.invoice_id
WHERE 1 = 1
    AND charges.merchant_id = 'acct_1Ir538D2AfqkY9Hk'
    AND (charges.payment_method_type = 'card' OR charges.payment_method_type = 'cashapp')
    AND charges.created::date >= '2023-06-07' AND charges.created::date < SYSDATE()::date
    AND charges.paid = true
    AND charges.status = 'succeeded'
    AND charges.amount > 0
    AND (
        (connected_accounts_metadata.key = 'id' AND customers_metadata.key = 'appUserId')
        OR charges.destination_id IS NULL
        )
ORDER BY charges.created DESC
