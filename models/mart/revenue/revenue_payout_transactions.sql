WITH pi_zip AS (
    SELECT
        payment_intent_id
        , merchant_id
        , value
    FROM {{ ref('stg_stripe_payment_intents_metadata') }}
    WHERE key = 'zip'
),
pi_region AS (
    SELECT
        payment_intent_id
        , merchant_id
        , value
    FROM {{ ref('stg_stripe_payment_intents_metadata') }}
    WHERE key = 'region'
),
pi_country AS (
    SELECT
        payment_intent_id
        , merchant_id
        , value
    FROM {{ ref('stg_stripe_payment_intents_metadata') }}
    WHERE key = 'country'
),
sub_zip AS (
    SELECT
        subscription_id
        , merchant_id
        , value
    FROM {{ ref('stg_stripe_subscriptions_metadata') }}
    WHERE key = 'zip'
),
sub_region AS (
    SELECT
        subscription_id
        , merchant_id
        , value
    FROM {{ ref('stg_stripe_subscriptions_metadata') }}
    WHERE key = 'region'
),
sub_country AS (
    SELECT
        subscription_id
        , merchant_id
        , value
    FROM {{ ref('stg_stripe_subscriptions_metadata') }}
    WHERE key = 'country'
),
sub_inv AS (
SELECT
    invoice_id
    , MAX(amount::float / 100.00) AS subscription_subtotal
FROM {{ ref('stg_stripe_invoice_line_items') }}
WHERE NOT contains (description, 'Subscription Sales Tax')
GROUP BY invoice_id
),
sub_inv_tax AS (
SELECT
    invoice_id
    , MAX(amount::float / 100.00) AS subscription_tax
FROM {{ ref('stg_stripe_invoice_line_items') }}
WHERE contains(description, 'Subscription Sales Tax')
GROUP BY invoice_id
)
SELECT DISTINCT
    charges.time AS charge_timestamp
    , invoices.invoice_date
    , charges.charge_id
    , stripe_charges.payment_intent AS payment_intent_id
    , stripe_charges.invoice_id
    , stripe_charges.customer_id
    , charges.creator_fanfix_id AS account_id
    , charges.account_id AS "connected account"
    , charges.creator_username as username
    , charges.fan_fanfix_id
    , stripe_charges.description AS description
    , {{ revenue_stream('stripe_charges.description') }}  AS revenue_stream
    , 'Stripe' AS payment_processor
    , charges.stripe_gross_revenue AS total
    , charges.sales_tax
    , charges.sub_total
    , charges.net_revenue AS creator_payout -- net revenue for agency
    , charges.total_fee
    , charges.platform_fee 
    , charges.processing_fee  --(stripe fee)
    , CASE
        WHEN tax.payment_processor_transaction_id IS NULL THEN True
        ELSE NULL
    END AS "[QA] CHECK MISSING RECORDS"
    ,CASE
        WHEN ROUND(charges.stripe_gross_revenue - COALESCE(tax.tax_amount, 0) - creator_payout - platform_fee - COALESCE(tax.processing_fee, 0),0) = 0 THEN NULL
        ELSE ROUND(charges.stripe_gross_revenue - COALESCE(tax.tax_amount, 0) - creator_payout - platform_fee - COALESCE(tax.processing_fee, 0), 2)
    END AS "[QA] CHECK TOTAL"
    , CASE
        WHEN ROUND(creator_payout / (charges.stripe_gross_revenue - COALESCE(tax.processing_fee, 0) - COALESCE(tax.tax_amount, 0)) * 100, 0) = 80 THEN NULL
        ELSE COALESCE(ROUND(creator_payout / (charges.stripe_gross_revenue - COALESCE(tax.processing_fee, 0) - COALESCE(tax.tax_amount, 0)) * 100, 2),0)
    END AS "[QA] CHECK CREATOR PAYOUT 80%"
    , CASE
        WHEN ROUND(platform_fee /sub_total * 100, 0) = 20 THEN NULL
        ELSE COALESCE(ROUND(platform_fee /sub_total * 100, 2),0)
    END AS "[QA] CHECK PLATFORM FEE %"
    , CASE
        WHEN ROUND(COALESCE(tax.processing_fee, 0) /(charges.stripe_gross_revenue - COALESCE(tax.processing_fee, 0)) * 100, 0) = 12 THEN NULL
        ELSE COALESCE(ROUND(COALESCE(tax.processing_fee, 0) /(charges.stripe_gross_revenue - COALESCE(tax.processing_fee, 0)) * 100, 2),0)
    END AS "[QA] CHECK PROCESSING FEE %"
    , CAST((transfers.amount / 100) AS DECIMAL(10, 2)) AS transferred_total_local_cur
    , transfers.currency AS transfer_currency
    , stripe_charges.status AS charge_status
    , stripe_charges.refunded
    , stripe_charges.card_currency AS card_currency
    , COALESCE(stripe_charges.card_address_zip , pi_zip.value, sub_zip.value) AS card_zipcode
    , COALESCE(stripe_charges.card_address_state, pi_region.value, sub_region.value) AS card_state
    , COALESCE(stripe_charges.card_address_country, pi_country.value, sub_country.value, payment_methods.card_country) AS card_country
    , tax.sales_tax_region
    , tax.sales_tax_zip
    , tax.sales_tax_countryCode
    , invoices.subscription_id
    , subs.subscription_status
    , subs.subscription_created_date AS subscription_first_created_date
    , CASE
        WHEN
            ABS(DATEDIFF(hour, invoices.status_transitions_paid_at, invoice_line_items.period_start)) <= 3
        THEN invoices.status_transitions_paid_at
        ELSE invoice_line_items.period_start
    END::date AS current_subscription_period_start
    , invoice_line_items.period_end::date AS current_subscription_period_end
FROM {{ ref('fact_creator_charges') }} charges
LEFT JOIN {{ ref('stg_stripe_charges') }} stripe_charges ON charges.charge_id = stripe_charges.charge_id
LEFT JOIN {{ ref('fact_payment_tax_collection') }} tax ON stripe_charges.payment_intent = tax.payment_processor_transaction_id
LEFT JOIN {{ ref('fact_subscriptions') }} subs ON stripe_charges.invoice_id = subs.invoice_id
LEFT JOIN {{ ref('stg_stripe_transfers') }} transfers ON stripe_charges.transfer_id = transfers.transfer_id
LEFT JOIN {{ ref('stg_stripe_invoices') }} invoices ON stripe_charges.invoice_id = invoices.invoice_id
LEFT JOIN (
    SELECT
        invoice_id, MAX(period_start) AS period_start, MAX(period_end) AS period_end
    FROM {{ ref('stg_stripe_invoice_line_items') }}
    GROUP BY invoice_id
) AS invoice_line_items ON invoice_line_items.invoice_id = invoices.invoice_id
LEFT JOIN {{ ref('stg_stripe_payment_methods') }} payment_methods ON stripe_charges.payment_method_id = payment_methods.payment_method_id
LEFT JOIN PI_ZIP ON stripe_charges.payment_intent = pi_zip.payment_intent_id
LEFT JOIN PI_COUNTRY ON stripe_charges.payment_intent = pi_country.payment_intent_id
LEFT JOIN PI_REGION ON stripe_charges.payment_intent = pi_region.payment_intent_id
LEFT JOIN SUB_ZIP ON invoices.subscription_id = sub_zip.subscription_id
LEFT JOIN SUB_COUNTRY ON invoices.subscription_id = sub_country.subscription_id
LEFT JOIN SUB_REGION ON invoices.subscription_id = sub_region.subscription_id
LEFT JOIN SUB_INV ON invoices.invoice_id = sub_inv.invoice_id
LEFT JOIN SUB_INV_TAX ON invoices.invoice_id = sub_inv_tax.invoice_id
WHERE 1 = 1
    AND charges.time::date >= '2023-06-07' 
    AND charges.time::date < SYSDATE()::date
ORDER BY charges.time DESC
