WITH monthly_earnings AS (
    SELECT 
        creator_id, date_trunc('month', day_created) AS month, 
        username,
        SUM(total_revenue) AS total_revenue,
        SUM(net_total_revenue) AS total_net_revenue,
        SUM(subscriptions_revenue) AS subscriptions_revenue,
        SUM(tips_revenue) AS tips_revenue,
        SUM(subscriptions_creation_revenue) AS subscriptions_creation_revenue,
        SUM(subscriptions_update_revenue) AS subscriptions_update_revenue,
        SUM(media_unlock_revenue) AS media_unlock_revenue,
        SUM(message_tip_revenue) AS message_tip_revenue,
        SUM(livestream_unlock_revenue) AS livestream_unlock_revenue,
        SUM(livestream_tip_revenue) AS livestream_tip_revenue,
        SUM(post_unlock_revenue) AS post_unlock_revenue,
        SUM(post_tip_revenue) AS post_tip_revenue,
        SUM(publication_revenue) AS publication_revenue,
        SUM(creator_tip_revenue) AS creator_tip_revenue
    FROM {{ ref("fact_daily_creator_earnings_bi") }}
    GROUP BY 1,2,3 ORDER BY 2 desc, 3 desc
)
SELECT 
    *,
    lag(total_revenue, 1) OVER (partition by creator_id ORDER BY month ASC) AS last_month_gross_revenue,
    lag(total_net_revenue, 1) OVER (partition by creator_id ORDER BY month ASC) last_month_net_revenue,
    CASE 
        WHEN last_month_gross_revenue>0 THEN (total_revenue-last_month_gross_revenue)/last_month_gross_revenue 
        ELSE null 
    END AS gross_percentage_delta_vs_last_month,
    CASE 
        WHEN last_month_net_revenue>0 THEN (last_month_net_revenue-total_net_revenue)/last_month_net_revenue 
        ELSE null 
    END AS net_percentage_delta_vs_last_month,
    percent_rank() OVER (partition by month ORDER BY total_revenue) AS rev_percentile,
    rank() OVER (partition by month ORDER BY total_revenue desc) AS monthly_ranking ,
    CASE
        WHEN last_month_gross_revenue >= 5000 AND total_revenue/last_month_gross_revenue <= 0.5 THEN 'MoM revenue dropped by 50%'
        WHEN last_month_gross_revenue >= 5000 AND total_revenue < 5000 THEN 'drop out of key active creators'
        ELSE null
    END AS alert_message
FROM MONTHLY_EARNINGS
ORDER BY month DESC, monthly_ranking