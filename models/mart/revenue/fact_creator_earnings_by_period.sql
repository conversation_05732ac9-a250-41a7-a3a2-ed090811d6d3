WITH current_30d_revenue AS (
    SELECT
        creator_id
        , SUM(total_revenue) AS current_30d_gross_total_revenue
        , SUM(subscriptions_revenue) AS current_30d_gross_sub_revenue
        , SUM(tips_revenue) AS current_30d_gross_tips_revenue
        , SUM(net_total_revenue) AS current_30d_net_total_revenue
        , SUM(net_subscriptions_revenue) AS current_30d_net_sub_revenue
        , SUM(net_tips_revenue) AS current_30d_net_tips_revenue
    FROM {{ ref('fact_daily_creator_earnings_bi') }}
    WHERE day_created >= dateadd(day, -30, current_date())
    GROUP BY 1
)
, last_30d_revenue AS (
    SELECT
        creator_id
        , SUM(total_revenue) AS last_30d_gross_total_revenue
        , SUM(subscriptions_revenue) AS last_30d_gross_sub_revenue
        , SUM(tips_revenue) AS last_30d_gross_tips_revenue
        , SUM(net_total_revenue) AS last_30d_net_total_revenue
        , SUM(net_subscriptions_revenue) AS last_30d_net_sub_revenue
        , SUM(net_tips_revenue) AS last_30d_net_tips_revenue
    FROM {{ ref('fact_daily_creator_earnings_bi') }}
    WHERE day_created >= dateadd(day, -60, current_date()) 
        AND day_created < dateadd(day, -30, current_date())
    GROUP BY 1
)
, current_60d_revenue AS (
    SELECT
        creator_id
        , SUM(total_revenue) AS current_60d_gross_total_revenue
        , SUM(subscriptions_revenue) AS current_60d_gross_sub_revenue
        , SUM(tips_revenue) AS current_60d_gross_tips_revenue
        , SUM(net_total_revenue) AS current_60d_net_total_revenue
        , SUM(net_subscriptions_revenue) AS current_60d_net_sub_revenue
        , SUM(net_tips_revenue) AS current_60d_net_tips_revenue
    FROM {{ ref('fact_daily_creator_earnings_bi') }}
    WHERE day_created >= dateadd(day, -60, current_date())
    GROUP BY 1
)
, last_60d_revenue AS (
    SELECT
        creator_id
        , SUM(total_revenue) AS last_60d_gross_total_revenue
        , SUM(subscriptions_revenue) AS last_60d_gross_sub_revenue
        , SUM(tips_revenue) AS last_60d_gross_tips_revenue
        , SUM(net_total_revenue) AS last_60d_net_total_revenue
        , SUM(net_subscriptions_revenue) AS last_60d_net_sub_revenue
        , SUM(net_tips_revenue) AS last_60d_net_tips_revenue
    FROM {{ ref('fact_daily_creator_earnings_bi') }}
    WHERE day_created >= dateadd(day, -120, current_date()) 
        AND day_created < dateadd(day, -60, current_date())
    GROUP BY 1
)
, current_90d_revenue AS (
    SELECT
        creator_id
        , SUM(total_revenue) AS current_90d_gross_total_revenue
        , SUM(subscriptions_revenue) AS current_90d_gross_sub_revenue
        , SUM(tips_revenue) AS current_90d_gross_tips_revenue
        , SUM(net_total_revenue) AS current_90d_net_total_revenue
        , SUM(net_subscriptions_revenue) AS current_90d_net_sub_revenue
        , SUM(net_tips_revenue) AS current_90d_net_tips_revenue
    FROM {{ ref('fact_daily_creator_earnings_bi') }}
    WHERE day_created >= dateadd(day, -90, current_date())
    GROUP BY 1
)
, last_90d_revenue AS (
    SELECT
        creator_id
        , SUM(total_revenue) AS last_90d_gross_total_revenue
        , SUM(subscriptions_revenue) AS last_90d_gross_sub_revenue
        , SUM(tips_revenue) AS last_90d_gross_tips_revenue
        , SUM(net_total_revenue) AS last_90d_net_total_revenue
        , SUM(net_subscriptions_revenue) AS last_90d_net_sub_revenue
        , SUM(net_tips_revenue) AS last_90d_net_tips_revenue
    FROM {{ ref('fact_daily_creator_earnings_bi') }}
    WHERE day_created >= dateadd(day, -180, current_date()) 
        AND day_created < dateadd(day, -90, current_date())
    GROUP BY 1
)
SELECT DISTINCT
    p.fanfix_id AS creator_id
    , p.fanfix_username AS username
    , p.account_management_agency AS agency_name
    , p.inactive_creator
    , current_30d_gross_total_revenue
    , last_30d_gross_total_revenue
    , CASE
        WHEN last_30d_gross_total_revenue = 0 THEN null
        ELSE (current_30d_gross_total_revenue-last_30d_gross_total_revenue)/last_30d_gross_total_revenue
    END AS total_gross_revenue_30d_delta

    , current_30d_gross_sub_revenue
    , last_30d_gross_sub_revenue
    , CASE
        WHEN last_30d_gross_sub_revenue =0 THEN null
        ELSE (current_30d_gross_sub_revenue-last_30d_gross_sub_revenue)/last_30d_gross_sub_revenue
    END AS gross_sub_revenue_30d_delta
    , current_30d_gross_tips_revenue
    , last_30d_gross_tips_revenue
    , CASE
        WHEN last_30d_gross_tips_revenue = 0 THEN null
        ELSE (current_30d_gross_tips_revenue-last_30d_gross_tips_revenue)/last_30d_gross_tips_revenue
    END AS gross_tips_revenue_30d_delta

    , current_30d_net_total_revenue
    , last_30d_net_total_revenue
    , CASE
        WHEN last_30d_net_total_revenue = 0 THEN null
        ELSE (current_30d_net_total_revenue-last_30d_net_total_revenue)/last_30d_net_total_revenue
    END AS total_net_revenue_30d_delta

    , current_30d_net_sub_revenue
    , last_30d_net_sub_revenue
    , CASE
        WHEN last_30d_net_sub_revenue =0 THEN null
        ELSE (current_30d_net_sub_revenue-last_30d_net_sub_revenue)/last_30d_net_sub_revenue
    END AS net_sub_revenue_30d_delta

    , current_30d_net_tips_revenue
    , last_30d_net_tips_revenue
    , CASE
        WHEN last_30d_net_tips_revenue = 0 THEN null
        ELSE (current_30d_net_tips_revenue-last_30d_net_tips_revenue)/last_30d_net_tips_revenue
    END AS net_tips_revenue_delta

   , current_60d_gross_total_revenue
    , last_60d_gross_total_revenue

    , CASE
        WHEN last_60d_gross_total_revenue = 0 THEN null
        ELSE (current_60d_gross_total_revenue-last_60d_gross_total_revenue)/last_60d_gross_total_revenue
    END AS total_gross_revenue_60d_delta

    , current_60d_gross_sub_revenue
    , last_60d_gross_sub_revenue
    , CASE
        WHEN last_60d_gross_sub_revenue =0 THEN null
        ELSE (current_60d_gross_sub_revenue-last_60d_gross_sub_revenue)/last_60d_gross_sub_revenue
    END AS gross_sub_revenue_60d_delta

    , current_60d_gross_tips_revenue
    , last_60d_gross_tips_revenue
    , CASE
        WHEN last_60d_gross_tips_revenue = 0 THEN null
        ELSE (current_60d_gross_tips_revenue-last_60d_gross_tips_revenue)/last_60d_gross_tips_revenue
    END AS gross_tips_revenue_60d_delta

    , current_60d_net_total_revenue
    , last_60d_net_total_revenue

    , CASE
        WHEN last_60d_net_total_revenue = 0 THEN null
        ELSE (current_60d_net_total_revenue-last_60d_net_total_revenue)/last_60d_net_total_revenue
    END AS total_net_revenue_60d_delta

    , current_60d_net_sub_revenue
    , last_60d_net_sub_revenue
    , CASE
        WHEN last_60d_net_sub_revenue =0 THEN null
        ELSE (current_60d_net_sub_revenue-last_60d_net_sub_revenue)/last_60d_net_sub_revenue
    END AS net_sub_revenue_60d_delta

    , current_60d_net_tips_revenue
    , last_60d_net_tips_revenue
    , CASE
        WHEN last_60d_net_tips_revenue = 0 THEN null
        ELSE (current_60d_net_tips_revenue-last_60d_net_tips_revenue)/last_60d_net_tips_revenue
    END AS net_tips_revenue_60d_delta

   , current_90d_gross_total_revenue
    , last_90d_gross_total_revenue

    , CASE
        WHEN last_90d_gross_total_revenue = 0 THEN null
        ELSE (current_90d_gross_total_revenue-last_90d_gross_total_revenue)/last_90d_gross_total_revenue
    END AS total_gross_revenue_90d_delta

    , current_90d_gross_sub_revenue
    , last_90d_gross_sub_revenue
    , CASE
        WHEN last_90d_gross_sub_revenue =0 THEN null
        ELSE (current_90d_gross_sub_revenue-last_90d_gross_sub_revenue)/last_90d_gross_sub_revenue
    END AS gross_sub_revenue_90d_delta

    , current_90d_gross_tips_revenue
    , last_90d_gross_tips_revenue
    , CASE
        WHEN last_90d_gross_tips_revenue = 0 THEN null
        ELSE (current_90d_gross_tips_revenue-last_90d_gross_tips_revenue)/last_90d_gross_tips_revenue
    END AS gross_tips_revenue_90d_delta

    , current_90d_net_total_revenue
    , last_90d_net_total_revenue

    , CASE
        WHEN last_90d_net_total_revenue = 0 THEN null
        ELSE (current_90d_net_total_revenue-last_90d_net_total_revenue)/last_90d_net_total_revenue
    END AS total_net_revenue_90d_delta

    , current_90d_net_sub_revenue
    , last_90d_net_sub_revenue
    , CASE
        WHEN last_90d_net_sub_revenue =0 THEN null
        ELSE (current_90d_net_sub_revenue-last_90d_net_sub_revenue)/last_90d_net_sub_revenue
    END AS net_sub_revenue_90d_delta

    , current_90d_net_tips_revenue
    , last_90d_net_tips_revenue
    , CASE
        WHEN last_90d_net_tips_revenue = 0 THEN null
        ELSE (current_90d_net_tips_revenue-last_60d_net_tips_revenue)/last_90d_net_tips_revenue
    END AS net_tips_revenue_90d_delta

    , a.day_since_last_post
    , a.day_since_last_blast
    , a.day_since_last_dm
FROM {{ ref('dim_creator_profile') }} p
LEFT JOIN current_30d_revenue c30r ON c30r.creator_id = p.fanfix_id
LEFT JOIN last_30d_revenue l30r ON l30r.creator_id = p.fanfix_id
LEFT JOIN current_60d_revenue c60r ON c60r.creator_id = p.fanfix_id
LEFT JOIN last_60d_revenue l60r ON l60r.creator_id = p.fanfix_id
LEFT JOIN current_90d_revenue c90r ON c90r.creator_id = p.fanfix_id
LEFT JOIN last_90d_revenue l90r ON l90r.creator_id = p.fanfix_id
LEFT JOIN {{ ref('fact_creator_last_activity') }} a ON p.fanfix_id = a.creator_id
ORDER BY 3 DESC, 2 ASC