{{ config(
    materialized='table',
    meta={
            'pre_hook': "SET (TIMEZONE, START_DATE, END_DATE) = ('{{ var('TIMEZONE') }}', '{{ var('START_DATE') }}', '{{ var('END_DATE') }}')"

        }
) }}

SELECT
    cam.value AS fanfix_id
    , cp.fanfix_username
    , cpi.payout_id AS balance_transfer_join_id
    , cpi.payout_status
    , bts.total_with_tax_and_processing_fee
    , bts.total
    , bts.net
    , bts.reporting_category
    , bts.description
    , bts.currency
    , r.created_at AS refund_time
FROM {{ ref('stg_stripe_connected_account_refunds') }} r
LEFT JOIN {{ ref('stg_stripe_connected_account_balance_transactions') }} bts ON r.connected_account_refund_id = bts.source_id
LEFT JOIN {{ ref('stg_stripe_financial_report_connect_payouts_itemized') }} cpi ON cpi.payout_id = bts.automatic_transfer_id
LEFT JOIN (
    SELECT * 
    FROM {{ ref('stg_stripe_connected_accounts_metadata') }} 
    WHERE key = 'id'
    ) cam ON cam.account_id = bts.account
LEFT JOIN {{ ref('dim_creator_profile') }} cp ON cam.value = cp.fanfix_id
WHERE 1 = 1
    AND bts.reporting_category = 'refund'
    AND cpi.payout_id IS NOT NULL
    -- AND payout_status = 'paid'