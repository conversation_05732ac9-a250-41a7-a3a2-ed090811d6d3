{{ config(
    materialized='table',
    pre_hook="SET (TIMEZONE, START_DATE, END_DATE) = ('{{ var('TIMEZONE') }}', '{{ var('START_DATE') }}', '{{ var('END_DATE') }}')"
) }}

SELECT
    cam.value AS fanfix_id
    , cp.fanfix_username
    , cpi.payout_id AS balance_transfer_join_id
    , cpi.payout_status
    , bts.total_with_tax_and_processing_fee
    , bts.total
    , bts.net
    , bts.reporting_category
    , bts.description
    , bts.currency
    , r.created_at AS refund_time
    , fccb.stripe_gross_revenue AS stripe_gross_usd
    , fccb.net_revenue AS net_usd 
    , fccb.platform_fee AS platform_fee_usd
    , fccb.processing_fee AS processing_fee_usd
    , fccb.sales_tax AS sales_tax_usd
    , ROUND(fccb.external_reporting_gross_revenue, 2) as reporting_gross_usd
FROM {{ ref('stg_stripe_connected_account_refunds') }} r
LEFT JOIN {{ ref('stg_stripe_connected_account_balance_transactions') }} bts ON r.connected_account_refund_id = bts.source_id
LEFT JOIN {{ ref('stg_stripe_financial_report_connect_payouts_itemized') }} cpi ON cpi.payout_id = bts.automatic_transfer_id
LEFT JOIN (
    SELECT * 
    FROM {{ ref('stg_stripe_connected_accounts_metadata') }} 
    WHERE key = 'id'
    ) cam ON cam.account_id = bts.account
LEFT JOIN {{ ref('dim_creator_profile') }} cp ON cam.value = cp.fanfix_id
LEFT JOIN {{ ref('stg_stripe_transfer_reversals') }} tr on tr.transfer_reversal_id = r.refund_transfer_reversal_id
LEFT JOIN {{ ref('fact_creator_chargeback') }} fccb ON tr.source_refund_id = fccb.refund_id 
WHERE 1 = 1
    AND bts.reporting_category = 'refund'
    AND cpi.payout_id IS NOT NULL
    -- AND payout_status = 'paid'