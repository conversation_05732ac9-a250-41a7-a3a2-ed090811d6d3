WITH summary AS (
  SELECT
  creator_fanfix_id AS creator_id,
  creator_username AS username,
  time::date AS day_created,
  MAX(time) AS last_transaction_timestamp,
  SUM(gross_revenue) AS total_revenue,
  SUM(net_revenue*1.25) AS gross_total_revenue,
  SUM(net_revenue) AS net_total_revenue,
  SUM(sales_tax) AS total_sales_tax,
  SUM(platform_fee) AS total_platform_fee,
  COUNT(*) AS total_payment_count, -- for Brian's report
  SUM(CASE WHEN type like '%subscription%' THEN 1 ELSE 0 END ) AS subscription_payment_count, -- for Brian's report
  SUM(CASE WHEN (type NOT like '%subscription%' AND type NOT like '%invoice%') THEN 1 ELSE 0 END ) AS tip_payment_count, -- for Brian's report

  COALESCE(SUM(CASE WHEN type like '%subscription%' THEN gross_revenue ELSE 0 END), 0) AS subscriptions_revenue,
  COALESCE(SUM(CASE WHEN (type NOT like '%subscription%' AND type NOT like '%invoice%') THEN gross_revenue ELSE 0 END ), 0) AS tips_revenue,
  COALESCE(SUM(CASE WHEN type = 'subscription creation' THEN gross_revenue ELSE 0 END), 0) AS subscriptions_creation_revenue,
  COALESCE(SUM(CASE WHEN type = 'subscription update' THEN gross_revenue ELSE 0 END), 0) AS subscriptions_update_revenue,
  COALESCE(SUM(CASE WHEN type = 'media unlock' THEN gross_revenue ELSE 0 END), 0) AS media_unlock_revenue,
  COALESCE(SUM(CASE WHEN type = 'message' THEN gross_revenue ELSE 0 END), 0) AS message_tip_revenue,

  COALESCE(SUM(CASE WHEN type = 'livestream unlock' THEN gross_revenue ELSE 0 END), 0) AS livestream_unlock_revenue,
  COALESCE(SUM(CASE WHEN type = 'livestream' THEN gross_revenue ELSE 0 END), 0) AS livestream_tip_revenue,
  COALESCE(SUM(CASE WHEN type = 'livestream' THEN gross_revenue ELSE 0 END), 0) AS livestream_revenue,

  COALESCE(SUM(CASE WHEN type = 'post unlock' THEN gross_revenue ELSE 0 END), 0) AS post_unlock_revenue,
  COALESCE(SUM(CASE WHEN type = 'post' THEN gross_revenue ELSE 0 END), 0) AS post_tip_revenue,
  COALESCE(SUM(CASE WHEN type = 'tip jar' THEN gross_revenue ELSE 0 END), 0) AS publication_revenue,
  COALESCE(SUM(CASE WHEN type = 'creator tip' THEN gross_revenue ELSE 0 END), 0) AS creator_tip_revenue,
  COALESCE(SUM(CASE WHEN type = 'invoice' THEN gross_revenue ELSE 0 END), 0) AS invoice_revenue,

  COALESCE(SUM(CASE WHEN type like '%subscription%' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_subscriptions_revenue,
  COALESCE(SUM(CASE WHEN (type NOT like '%subscription%' AND type NOT like '%invoice%') THEN net_revenue*1.25 ELSE 0 END ), 0) AS gross_tips_revenue,
  COALESCE(SUM(CASE WHEN type = 'subscription creation' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_subscriptions_creation_revenue,
  COALESCE(SUM(CASE WHEN type = 'subscription update' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_subscriptions_update_revenue,
  COALESCE(SUM(CASE WHEN type = 'media unlock' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_media_unlock_revenue,
  COALESCE(SUM(CASE WHEN type = 'message' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_message_tip_revenue,
  COALESCE(SUM(CASE WHEN type = 'livestream' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_livestream_tip_revenue,
  COALESCE(SUM(CASE WHEN type = 'livestream unlock' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_livestream_unlock_revenue,
  COALESCE(SUM(CASE WHEN type = 'livestream' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_livestream_revenue,

  COALESCE(SUM(CASE WHEN type = 'post unlock' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_post_unlock_revenue,
  COALESCE(SUM(CASE WHEN type = 'post' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_post_tip_revenue,
  COALESCE(SUM(CASE WHEN type = 'tip jar' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_publication_revenue,
  COALESCE(SUM(CASE WHEN type = 'creator tip' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_creator_tip_revenue,
  COALESCE(SUM(CASE WHEN type = 'invoice' THEN net_revenue*1.25 ELSE 0 END), 0) AS gross_invoice_revenue,

  COALESCE(SUM(CASE WHEN type like '%subscription%' THEN net_revenue ELSE 0 END), 0) AS net_subscriptions_revenue,
  COALESCE(SUM(CASE WHEN (type NOT like '%subscription%' AND type NOT like '%invoice%') THEN net_revenue ELSE 0 END ), 0) AS net_tips_revenue,
  COALESCE(SUM(CASE WHEN type = 'subscription creation' THEN net_revenue ELSE 0 END), 0) AS net_subscriptions_creation_revenue,
  COALESCE(SUM(CASE WHEN type = 'subscription update' THEN net_revenue ELSE 0 END), 0) AS net_subscriptions_update_revenue,
  COALESCE(SUM(CASE WHEN type = 'media unlock' THEN net_revenue ELSE 0 END), 0) AS net_media_unlock_revenue,
  COALESCE(SUM(CASE WHEN type = 'message' THEN net_revenue ELSE 0 END), 0) AS net_message_tip_revenue,

  COALESCE(SUM(CASE WHEN type = 'livestream unlock' THEN net_revenue ELSE 0 END), 0) AS net_livestream_unlock_revenue,
  COALESCE(SUM(CASE WHEN type = 'livestream' THEN net_revenue ELSE 0 END), 0) AS net_livestream_tip_revenue,
  COALESCE(SUM(CASE WHEN type = 'livestream' THEN net_revenue ELSE 0 END), 0) AS net_livestream_revenue,

  COALESCE(SUM(CASE WHEN type = 'post unlock' THEN net_revenue ELSE 0 END), 0) AS net_post_unlock_revenue,
  COALESCE(SUM(CASE WHEN type = 'post' THEN net_revenue ELSE 0 END), 0) AS net_post_tip_revenue,
  COALESCE(SUM(CASE WHEN type = 'tip jar' THEN net_revenue ELSE 0 END), 0) AS net_publication_revenue,
  COALESCE(SUM(CASE WHEN type = 'creator tip' THEN net_revenue ELSE 0 END), 0) AS net_creator_tip_revenue,
  COALESCE(SUM(CASE WHEN type = 'invoice' THEN net_revenue ELSE 0 END), 0) AS net_invoice_revenue
  FROM {{ ref('fact_creator_charges') }}
  GROUP BY 1,2,3
)
, chargeback AS (
    SELECT
        creator_fanfix_id
        , reverse_payment_time::date AS reverse_payment_date
        , SUM(net_revenue) AS chargeback_amount
        , SUM(CASE WHEN REVERSE_PAYMENT_TYPE NOT like '%refund%' THEN net_revenue ELSE 0 END) AS chargeback_dispute
        , SUM(CASE WHEN REVERSE_PAYMENT_TYPE like '%refund%' THEN net_revenue ELSE 0 END) AS chargeback_refund
    FROM {{ ref('fact_creator_chargeback') }}
    GROUP BY 1, 2
)
SELECT
    s.*
    , COALESCE(c.chargeback_amount, 0) AS chargeback_amount
    , COALESCE(c.chargeback_dispute, 0) AS chargeback_dispute
    , COALESCE(c.chargeback_refund, 0) AS chargeback_refund
FROM SUMMARY s
LEFT JOIN CHARGEBACK c ON s.creator_id = c.creator_fanfix_id AND s.day_created = c.reverse_payment_date
GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57
ORDER BY 1 DESC, 3 DESC