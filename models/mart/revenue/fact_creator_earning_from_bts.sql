--TODO: once dim_creator_profile is built, remove the comment from the join below
{{ config(
    materialized='table',
    pre_hook="SET (TIMEZONE, START_DATE, END_DATE) = ('{{ var('TIMEZONE') }}', '{{ var('START_DATE') }}', '{{ var('END_DATE') }}')"
) }}

SELECT
    cam.value AS fanfix_id
    , cp.fanfix_username
    , cpi.payout_id AS balance_transfer_join_id
    , cpi.payout_status
    , bts.total_with_tax_and_processing_fee
    , bts.total
    , bts.net
    , bts.reporting_category
    , bts.description
    , bts.currency
    , c.created_at AS charge_time
    , c.charge_amount
    , CASE 
        WHEN r.created_at IS NOT NULL THEN True
        ELSE False
    END AS refunded_charge
FROM {{ ref('stg_stripe_connected_account_charges') }} c
LEFT JOIN {{ ref('stg_stripe_connected_account_balance_transactions') }} bts ON c.connected_account_charge_id = bts.source_id
LEFT JOIN {{ ref('stg_stripe_financial_report_connect_payouts_itemized') }} cpi ON cpi.payout_id = bts.automatic_transfer_id
LEFT JOIN (
    SELECT *
    FROM {{ ref('stg_stripe_connected_accounts_metadata') }}
    WHERE key = 'id'
) cam ON cam.account_id = bts.account
LEFT JOIN {{ ref('dim_creator_profile') }} cp ON cam.value = cp.fanfix_id
LEFT JOIN {{ ref('stg_stripe_connected_account_refunds') }} r ON r.connected_account_refund_id = bts.source_id
WHERE 1 = 1
    AND (bts.reporting_category = 'charge' OR bts.reporting_category = 'fee')
    AND cpi.payout_id IS NOT NULL
    -- and payout_status = 'paid'
