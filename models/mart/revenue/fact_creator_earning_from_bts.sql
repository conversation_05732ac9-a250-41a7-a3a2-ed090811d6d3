{{ config(
    materialized='table',
    pre_hook="SET (TIMEZONE, START_DATE, END_DATE) = ('{{ var('TIMEZONE') }}', '{{ var('START_DATE') }}', '{{ var('END_DATE') }}')"
) }}

SELECT
    cam.value AS fanfix_id
    , cp.fanfix_username
    , cpi.payout_id AS balance_transfer_join_id
    , bts.source_id AS payment_id
    , cpi.payout_status
    , fcc.charge_id
    , fcc.type
    , fcc.entity_id
    , bts.total_with_tax_and_processing_fee
    , bts.total
    , bts.net
    , bts.reporting_category
    , bts.description
    , bts.currency
    , c.created_at AS charge_time
    , c.charge_amount
    , CASE 
        WHEN r.created_at IS NOT NULL THEN True
        ELSE False
    END AS refunded_charge
    , fcc.stripe_gross_revenue AS stripe_gross_usd
    , fcc.net_revenue AS net_usd 
    , fcc.platform_fee AS platform_fee_usd
    , fcc.processing_fee AS processing_fee_usd
    , fcc.sales_tax AS sales_tax_usd
    , ROUND(fcc.external_reporting_gross_revenue, 2) as reporting_gross_usd
FROM {{ ref('stg_stripe_connected_account_charges') }} c
LEFT JOIN {{ ref('stg_stripe_connected_account_balance_transactions') }} bts ON c.connected_account_charge_id = bts.source_id
LEFT JOIN {{ ref('stg_stripe_financial_report_connect_payouts_itemized') }} cpi ON cpi.payout_id = bts.automatic_transfer_id
LEFT JOIN (
    SELECT *
    FROM {{ ref('stg_stripe_connected_accounts_metadata') }}
    WHERE key = 'id'
) cam ON cam.account_id = bts.account
LEFT JOIN {{ ref('dim_creator_profile') }} cp ON cam.value = cp.fanfix_id
LEFT JOIN {{ ref('stg_stripe_transfers') }}  tr on tr.destination_payment_id = bts.source_id
LEFT JOIN {{ ref('fact_creator_charges') }}  fcc ON tr.source_transaction_id = fcc.charge_id 
LEFT JOIN {{ ref('stg_stripe_connected_account_refunds') }} r ON r.connected_account_refund_id = bts.source_id
WHERE 1 = 1
    AND (bts.reporting_category = 'charge' OR bts.reporting_category = 'fee')
    AND cpi.payout_id IS NOT NULL
    -- and payout_status = 'paid'
