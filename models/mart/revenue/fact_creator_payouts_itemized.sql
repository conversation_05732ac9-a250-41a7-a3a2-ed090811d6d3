{{ config(
    materialized='table',
    meta={
            'pre_hook': "SET (TIMEZONE, START_DATE, END_DATE) = ('{{ var('TIMEZONE') }}', '{{ var('START_DATE') }}', '{{ var('END_DATE') }}')"

        }
) }}

SELECT
    cam.value as fanfix_id
    , cp.fanfix_username
    , cpi.payout_id
    , cpi.payout_destination_id
    , cpi.reporting_category
    , cpi.payout_status
    , cpi.currency
    , cpi.gross::float AS amount
    , cpi.description
    , cpi.payout_expected_arrival_date::date AS payout_date
FROM {{ ref('stg_stripe_financial_report_connect_payouts_itemized') }} cpi
LEFT JOIN (
    SELECT *
    FROM {{ ref('stg_stripe_connected_accounts_metadata') }}
    WHERE key = 'id'
) cam ON cam.account_id = cpi.connected_account
LEFT JOIN {{ ref('dim_creator_profile') }} cp ON cam.value = cp.fanfix_id