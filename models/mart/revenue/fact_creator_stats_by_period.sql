WITH current_r7_stats AS (
    SELECT 
        creator_id
        , sum(gross_total_revenue) AS current_r7_total_gmv
        , sum(gross_subscriptions_revenue) AS current_r7_sub_gmv
        , sum(gross_tips_revenue) AS current_r7_tips_gmv
        , sum(net_total_revenue) AS current_r7_total_nmv
        , sum(net_subscriptions_revenue) AS current_r7_sub_nmv
        , sum(net_tips_revenue) AS current_r7_tips_nmv
        , sum(post_count) AS current_r7_posts
        , sum(blast_count) AS current_r7_blasts
        , sum(outgoing_dm_count) AS current_r7_dm
    FROM {{ ref('fact_creator_daily_earning_and_activity') }}
    WHERE day_created>= dateadd(day, -7, current_date())
    GROUP BY 1
)
, last_r7_stats AS (
    SELECT 
        creator_id
        , sum(gross_total_revenue) AS last_r7_total_gmv
        , sum(gross_subscriptions_revenue) AS last_r7_sub_gmv
        , sum(gross_tips_revenue) AS last_r7_tips_gmv
        , sum(net_total_revenue) AS last_r7_total_nmv
        , sum(net_subscriptions_revenue) AS last_r7_sub_nmv
        , sum(net_tips_revenue) AS last_r7_tips_nmv
        , sum(post_count) AS last_r7_posts
        , sum(blast_count) AS last_r7_blasts
        , sum(outgoing_dm_count) AS last_r7_dm
    FROM {{ ref('fact_creator_daily_earning_and_activity') }}
    WHERE day_created>= dateadd(day, -14, current_date()) and day_created < dateadd(day, -7, current_date())
    GROUP BY 1
)
, current_r30_stats AS (
    SELECT 
        creator_id
        , sum(gross_total_revenue) AS current_r30_total_gmv
        , sum(gross_subscriptions_revenue) AS current_r30_sub_gmv
        , sum(gross_tips_revenue) AS current_r30_tips_gmv
        , sum(net_total_revenue) AS current_r30_total_nmv
        , sum(net_subscriptions_revenue) AS current_r30_sub_nmv
        , sum(net_tips_revenue) AS current_r30_tips_nmv
        , sum(post_count) AS current_r30_posts
        , sum(blast_count) AS current_r30_blasts
        , sum(outgoing_dm_count) AS current_r30_dm
    FROM {{ ref('fact_creator_daily_earning_and_activity') }}
    WHERE day_created>= dateadd(day, -30, current_date())
    GROUP BY 1
)
, last_r30_stats AS (
    SELECT 
        creator_id
        , sum(gross_total_revenue) AS last_r30_total_gmv
        , sum(gross_subscriptions_revenue) AS last_r30_sub_gmv
        , sum(gross_tips_revenue) AS last_r30_tips_gmv
        , sum(net_total_revenue) AS last_r30_total_nmv
        , sum(net_subscriptions_revenue) AS last_r30_sub_nmv
        , sum(net_tips_revenue) AS last_r30_tips_nmv
        , sum(post_count) AS last_r30_posts
        , sum(blast_count) AS last_r30_blasts
        , sum(outgoing_dm_count) AS last_r30_dm
    FROM {{ ref('fact_creator_daily_earning_and_activity') }}
    WHERE day_created>= dateadd(day, -60, current_date()) and day_created < dateadd(day, -30, current_date())
    GROUP BY 1
)
, current_r60_stats AS (
    SELECT 
        creator_id
        , sum(gross_total_revenue) AS current_r60_total_gmv
        , sum(gross_subscriptions_revenue) AS current_r60_sub_gmv
        , sum(gross_tips_revenue) AS current_r60_tips_gmv
        , sum(net_total_revenue) AS current_r60_total_nmv
        , sum(net_subscriptions_revenue) AS current_r60_sub_nmv
        , sum(net_tips_revenue) AS current_r60_tips_nmv
        , sum(post_count) AS current_r60_posts
        , sum(blast_count) AS current_r60_blasts
        , sum(outgoing_dm_count) AS current_r60_dm
    FROM {{ ref('fact_creator_daily_earning_and_activity') }}
    WHERE day_created>= dateadd(day, -60, current_date())
    GROUP BY 1
)
, last_r60_stats AS (
    SELECT 
        creator_id
        , sum(gross_total_revenue) AS last_r60_total_gmv
        , sum(gross_subscriptions_revenue) AS last_r60_sub_gmv
        , sum(gross_tips_revenue) AS last_r60_tips_gmv
        , sum(net_total_revenue) AS last_r60_total_nmv
        , sum(net_subscriptions_revenue) AS last_r60_sub_nmv
        , sum(net_tips_revenue) AS last_r60_tips_nmv
        , sum(post_count) AS last_r60_posts
        , sum(blast_count) AS last_r60_blasts
        , sum(outgoing_dm_count) AS last_r60_dm
    FROM {{ ref('fact_creator_daily_earning_and_activity') }}
    WHERE day_created>= dateadd(day, -120, current_date()) and day_created < dateadd(day, -60, current_date())
    GROUP BY 1
)
, current_r90_stats AS (
    SELECT 
        creator_id
        , sum(gross_total_revenue) AS current_r90_total_gmv
        , sum(gross_subscriptions_revenue) AS current_r90_sub_gmv
        , sum(gross_tips_revenue) AS current_r90_tips_gmv
        , sum(net_total_revenue) AS current_r90_total_nmv
        , sum(net_subscriptions_revenue) AS current_r90_sub_nmv
        , sum(net_tips_revenue) AS current_r90_tips_nmv
        , sum(post_count) AS current_r90_posts
        , sum(blast_count) AS current_r90_blasts
        , sum(outgoing_dm_count) AS current_r90_dm
    FROM {{ ref('fact_creator_daily_earning_and_activity') }}
    WHERE day_created>= dateadd(day, -90, current_date())
    GROUP BY 1
)
, last_r90_stats AS (
    SELECT 
        creator_id
        , sum(gross_total_revenue) AS last_r90_total_gmv
        , sum(gross_subscriptions_revenue) AS last_r90_sub_gmv
        , sum(gross_tips_revenue) AS last_r90_tips_gmv
        , sum(net_total_revenue) AS last_r90_total_nmv
        , sum(net_subscriptions_revenue) AS last_r90_sub_nmv
        , sum(net_tips_revenue) AS last_r90_tips_nmv
        , sum(post_count) AS last_r90_posts
        , sum(blast_count) AS last_r90_blasts
        , sum(outgoing_dm_count) AS last_r90_dm
    FROM {{ ref('fact_creator_daily_earning_and_activity') }}
    WHERE day_created>= dateadd(day, -180, current_date()) and day_created < dateadd(day, -90, current_date())
    GROUP BY 1
)
, all_measures as (

SELECT DISTINCT
    fanfix_id AS creator_id 
    , fanfix_username AS username 
    , account_management_agency AS agency_name
    , inactive_creator
    , current_r7_total_gmv
    , last_r7_total_gmv 
    , CASE 
        WHEN last_r7_total_gmv = 0 THEN null 
        ELSE (current_r7_total_gmv-last_r7_total_gmv)/last_r7_total_gmv
    END AS total_r7_gmv_delta
    
    , current_r7_sub_gmv 
    , last_r7_sub_gmv 
    , CASE 
        WHEN last_r7_sub_gmv =0 THEN null
        ELSE (current_r7_sub_gmv-last_r7_sub_gmv)/last_r7_sub_gmv 
    END AS sub_r7_gmv_delta
    
    , current_r7_tips_gmv
    , last_r7_tips_gmv
    , CASE 
        WHEN last_r7_total_gmv = 0 THEN null 
        ELSE (current_r7_tips_gmv-last_r7_tips_gmv)/last_r7_total_gmv 
    END AS tips_r7_gmv_delta

    , current_r7_total_nmv
    , last_r7_total_nmv 
    , CASE 
        WHEN last_r7_total_nmv = 0 THEN null 
        ELSE (current_r7_total_nmv-last_r7_total_nmv)/last_r7_total_nmv
    END AS total_r7_nmv_delta
    
    , current_r7_sub_nmv 
    , last_r7_sub_nmv 
    , CASE 
        WHEN last_r7_sub_nmv =0 THEN null
        ELSE (current_r7_sub_nmv-last_r7_sub_nmv)/last_r7_sub_nmv 
    END AS sub_r7_nmv_delta
    
    , current_r7_tips_nmv
    , last_r7_tips_nmv
    , CASE 
        WHEN last_r7_total_nmv = 0 THEN null 
        ELSE (current_r7_tips_nmv-last_r7_tips_nmv)/last_r7_total_nmv 
    END AS tips_r7_nmv_delta

    , current_r7_posts
    , last_r7_posts
    , CASE 
        WHEN last_r7_posts =0  THEN null
        ELSE (current_r7_posts - last_r7_posts)/last_r7_posts
    END AS posts_r7_delta

    , current_r7_blasts
    , last_r7_blasts
    , CASE 
        WHEN last_r7_blasts =0  THEN null
        ELSE (current_r7_blasts - last_r7_blasts)/last_r7_blasts
    END AS blasts_r7_delta

    , current_r7_dm
    , last_r7_dm
    , CASE 
        WHEN last_r7_dm =0  THEN null
        ELSE (current_r7_dm - last_r7_dm)/last_r7_dm
    END AS dm_r7_delta
    
    , current_r30_total_gmv
    , last_r30_total_gmv 
    , CASE 
        WHEN last_r30_total_gmv = 0 THEN null 
        ELSE (current_r30_total_gmv-last_r30_total_gmv)/last_r30_total_gmv
    END AS total_r30_gmv_delta
    
    , current_r30_sub_gmv 
    , last_r30_sub_gmv 
    , CASE 
        WHEN last_r30_sub_gmv =0 THEN null
        ELSE (current_r30_sub_gmv-last_r30_sub_gmv)/last_r30_sub_gmv 
    END AS sub_r30_gmv_delta
    
    , current_r30_tips_gmv
    , last_r30_tips_gmv
    , CASE 
        WHEN last_r30_total_gmv = 0 THEN null 
        ELSE (current_r30_tips_gmv-last_r30_tips_gmv)/last_r30_total_gmv 
    END AS tips_r30_gmv_delta

    , current_r30_total_nmv
    , last_r30_total_nmv 
    , CASE 
        WHEN last_r30_total_nmv = 0 THEN null 
        ELSE (current_r30_total_nmv-last_r30_total_nmv)/last_r30_total_nmv
    END AS total_r30_nmv_delta
    
    , current_r30_sub_nmv 
    , last_r30_sub_nmv 
    , CASE 
        WHEN last_r30_sub_nmv =0 THEN null
        ELSE (current_r30_sub_nmv-last_r30_sub_nmv)/last_r30_sub_nmv 
    END AS sub_r30_nmv_delta
    
    , current_r30_tips_nmv
    , last_r30_tips_nmv
    , CASE 
        WHEN last_r30_total_nmv = 0 THEN null 
        ELSE (current_r30_tips_nmv-last_r30_tips_nmv)/last_r30_total_nmv 
    END AS tips_r30_nmv_delta

    , current_r30_posts
    , last_r30_posts
    , CASE 
        WHEN last_r30_posts =0  THEN null
        ELSE (current_r30_posts - last_r30_posts)/last_r30_posts
    END AS posts_r30_delta

    , current_r30_blasts
    , last_r30_blasts
    , CASE 
        WHEN last_r30_blasts =0  THEN null
        ELSE (current_r30_blasts - last_r30_blasts)/last_r30_blasts
    END AS blasts_r30_delta

    , current_r30_dm
    , last_r30_dm
    , CASE 
        WHEN last_r30_dm =0  THEN null
        ELSE (current_r30_dm - last_r30_dm)/last_r30_dm
    END AS dm_r30_delta

    , current_r60_total_gmv
    , last_r60_total_gmv 
    , CASE 
        WHEN last_r60_total_gmv = 0 THEN null 
        ELSE (current_r60_total_gmv-last_r60_total_gmv)/last_r60_total_gmv
    END AS total_r60_gmv_delta
    
    , current_r60_sub_gmv 
    , last_r60_sub_gmv 
    , CASE 
        WHEN last_r60_sub_gmv =0 THEN null
        ELSE (current_r60_sub_gmv-last_r60_sub_gmv)/last_r60_sub_gmv 
    END AS sub_r60_gmv_delta
    
    , current_r60_tips_gmv
    , last_r60_tips_gmv
    , CASE 
        WHEN last_r60_total_gmv = 0 THEN null 
        ELSE (current_r60_tips_gmv-last_r60_tips_gmv)/last_r60_total_gmv 
    END AS tips_r60_gmv_delta

    , current_r60_total_nmv
    , last_r60_total_nmv 
    , CASE 
        WHEN last_r60_total_nmv = 0 THEN null 
        ELSE (current_r60_total_nmv-last_r60_total_nmv)/last_r60_total_nmv
    END AS total_r60_nmv_delta
    
    , current_r60_sub_nmv 
    , last_r60_sub_nmv 
    , CASE 
        WHEN last_r60_sub_nmv =0 THEN null
        ELSE (current_r60_sub_nmv-last_r60_sub_nmv)/last_r60_sub_nmv 
    END AS sub_r60_nmv_delta
    
    , current_r60_tips_nmv
    , last_r60_tips_nmv
    , CASE 
        WHEN last_r60_total_nmv = 0 THEN null 
        ELSE (current_r60_tips_nmv-last_r60_tips_nmv)/last_r60_total_nmv 
    END AS tips_r60_nmv_delta

    , current_r60_posts
    , last_r60_posts
    , CASE 
        WHEN last_r60_posts =0  THEN null
        ELSE (current_r60_posts - last_r60_posts)/last_r60_posts
    END AS posts_r60_delta

    , current_r60_blasts
    , last_r60_blasts
    , CASE 
        WHEN last_r60_blasts =0  THEN null
        ELSE (current_r60_blasts - last_r60_blasts)/last_r60_blasts
    END AS blasts_r60_delta

    , current_r60_dm
    , last_r60_dm
    , CASE 
        WHEN last_r60_dm =0  THEN null
        ELSE (current_r60_dm - last_r60_dm)/last_r60_dm
    END AS dm_r60_delta

    , current_r90_total_gmv
    , last_r90_total_gmv 
    , CASE 
        WHEN last_r90_total_gmv = 0 THEN null 
        ELSE (current_r90_total_gmv-last_r90_total_gmv)/last_r90_total_gmv
    END AS total_r90_gmv_delta
    
    , current_r90_sub_gmv 
    , last_r90_sub_gmv 
    , CASE 
        WHEN last_r90_sub_gmv =0 THEN null
        ELSE (current_r90_sub_gmv-last_r90_sub_gmv)/last_r90_sub_gmv 
    END AS sub_r90_gmv_delta
    
    , current_r90_tips_gmv
    , last_r90_tips_gmv
    , CASE 
        WHEN last_r90_total_gmv = 0 THEN null 
        ELSE (current_r90_tips_gmv-last_r90_tips_gmv)/last_r90_total_gmv 
    END AS tips_r90_gmv_delta

    , current_r90_total_nmv
    , last_r90_total_nmv 
    , CASE 
        WHEN last_r90_total_nmv = 0 THEN null 
        ELSE (current_r90_total_nmv-last_r90_total_nmv)/last_r90_total_nmv
    END AS total_r90_nmv_delta
    
    , current_r90_sub_nmv 
    , last_r90_sub_nmv 
    , CASE 
        WHEN last_r90_sub_nmv =0 THEN null
        ELSE (current_r90_sub_nmv-last_r90_sub_nmv)/last_r90_sub_nmv 
    END AS sub_r90_nmv_delta
    
    , current_r90_tips_nmv
    , last_r90_tips_nmv
    , CASE 
        WHEN last_r90_total_nmv = 0 THEN null 
        ELSE (current_r90_tips_nmv-last_r90_tips_nmv)/last_r90_total_nmv 
    END AS tips_r90_nmv_delta

    , current_r90_posts
    , last_r90_posts
    , CASE 
        WHEN last_r90_posts =0  THEN null
        ELSE (current_r90_posts - last_r90_posts)/last_r90_posts
    END AS posts_r90_delta

    , current_r90_blasts
    , last_r90_blasts
    , CASE 
        WHEN last_r90_blasts =0  THEN null
        ELSE (current_r90_blasts - last_r90_blasts)/last_r90_blasts
    END AS blasts_r90_delta

    , current_r90_dm
    , last_r90_dm
    , CASE 
        WHEN last_r90_dm =0  THEN null
        ELSE (current_r90_dm - last_r90_dm)/last_r90_dm
    END AS dm_r90_delta

    , a.day_since_last_post
    , a.day_since_last_blast
    , a.day_since_last_dm
FROM {{ ref('dim_creator_profile') }} p
LEFT JOIN current_r7_stats cr7 ON cr7.creator_id = p.fanfix_id
LEFT JOIN last_r7_stats lr7 ON lr7.creator_id = p.fanfix_id
LEFT JOIN current_r30_stats cr30 ON cr30.creator_id = p.fanfix_id
LEFT JOIN last_r30_stats lr30 ON lr30.creator_id = p.fanfix_id
LEFT JOIN current_r60_stats cr60 ON cr60.creator_id = p.fanfix_id
LEFT JOIN last_r60_stats lr60 ON lr60.creator_id = p.fanfix_id
LEFT JOIN current_r90_stats cr90 ON cr90.creator_id = p.fanfix_id
LEFT JOIN last_r90_stats lr90 ON lr90.creator_id = p.fanfix_id
LEFT JOIN {{ ref('fact_creator_last_activity') }} a ON p.fanfix_id = a.creator_id
)
SELECT
    CREATOR_ID,
    USERNAME,
    AGENCY_NAME,
    INACTIVE_CREATOR,

    CASE WHEN CURRENT_R7_TOTAL_GMV IS NULL THEN 0 ELSE CURRENT_R7_TOTAL_GMV END AS CURRENT_R7_TOTAL_GMV,
    CASE WHEN LAST_R7_TOTAL_GMV IS NULL THEN 0 ELSE LAST_R7_TOTAL_GMV END AS LAST_R7_TOTAL_GMV,
    TOTAL_R7_GMV_DELTA,
    CASE WHEN CURRENT_R7_SUB_GMV IS NULL THEN 0 ELSE CURRENT_R7_SUB_GMV END AS CURRENT_R7_SUB_GMV,
    CASE WHEN LAST_R7_SUB_GMV IS NULL THEN 0 ELSE LAST_R7_SUB_GMV END AS LAST_R7_SUB_GMV,
    SUB_R7_GMV_DELTA,
    CASE WHEN CURRENT_R7_TIPS_GMV IS NULL THEN 0 ELSE CURRENT_R7_TIPS_GMV END AS CURRENT_R7_TIPS_GMV,
    CASE WHEN LAST_R7_TIPS_GMV IS NULL THEN 0 ELSE LAST_R7_TIPS_GMV END AS LAST_R7_TIPS_GMV,
    TIPS_R7_GMV_DELTA,
    CASE WHEN CURRENT_R7_TOTAL_NMV IS NULL THEN 0 ELSE CURRENT_R7_TOTAL_NMV END AS CURRENT_R7_TOTAL_NMV,
    CASE WHEN LAST_R7_TOTAL_NMV IS NULL THEN 0 ELSE LAST_R7_TOTAL_NMV END AS LAST_R7_TOTAL_NMV,
    TOTAL_R7_NMV_DELTA,
    CASE WHEN CURRENT_R7_SUB_NMV IS NULL THEN 0 ELSE CURRENT_R7_SUB_NMV END AS CURRENT_R7_SUB_NMV,
    CASE WHEN LAST_R7_SUB_NMV IS NULL THEN 0 ELSE LAST_R7_SUB_NMV END AS LAST_R7_SUB_NMV,
    SUB_R7_NMV_DELTA,
    CASE WHEN CURRENT_R7_TIPS_NMV IS NULL THEN 0 ELSE CURRENT_R7_TIPS_NMV END AS CURRENT_R7_TIPS_NMV,
    CASE WHEN LAST_R7_TIPS_NMV IS NULL THEN 0 ELSE LAST_R7_TIPS_NMV END AS LAST_R7_TIPS_NMV,
    TIPS_R7_NMV_DELTA,
    CASE WHEN CURRENT_R7_POSTS IS NULL THEN 0 ELSE CURRENT_R7_POSTS END AS CURRENT_R7_POSTS,
    CASE WHEN LAST_R7_POSTS IS NULL THEN 0 ELSE LAST_R7_POSTS END AS LAST_R7_POSTS,
    POSTS_R7_DELTA,
    CASE WHEN CURRENT_R7_BLASTS IS NULL THEN 0 ELSE CURRENT_R7_BLASTS END AS CURRENT_R7_BLASTS,
    CASE WHEN LAST_R7_BLASTS IS NULL THEN 0 ELSE LAST_R7_BLASTS END AS LAST_R7_BLASTS,
    BLASTS_R7_DELTA,
    CASE WHEN CURRENT_R7_DM IS NULL THEN 0 ELSE CURRENT_R7_DM END AS CURRENT_R7_DM,
    CASE WHEN LAST_R7_DM IS NULL THEN 0 ELSE LAST_R7_DM END AS LAST_R7_DM,
    DM_R7_DELTA,

    CASE WHEN CURRENT_R30_TOTAL_GMV IS NULL THEN 0 ELSE CURRENT_R30_TOTAL_GMV END AS CURRENT_R30_TOTAL_GMV,
    CASE WHEN LAST_R30_TOTAL_GMV IS NULL THEN 0 ELSE LAST_R30_TOTAL_GMV END AS LAST_R30_TOTAL_GMV,
    TOTAL_R30_GMV_DELTA,
    CASE WHEN CURRENT_R30_SUB_GMV IS NULL THEN 0 ELSE CURRENT_R30_SUB_GMV END AS CURRENT_R30_SUB_GMV,
    CASE WHEN LAST_R30_SUB_GMV IS NULL THEN 0 ELSE LAST_R30_SUB_GMV END AS LAST_R30_SUB_GMV,
    SUB_R30_GMV_DELTA,
    CASE WHEN CURRENT_R30_TIPS_GMV IS NULL THEN 0 ELSE CURRENT_R30_TIPS_GMV END AS CURRENT_R30_TIPS_GMV,
    CASE WHEN LAST_R30_TIPS_GMV IS NULL THEN 0 ELSE LAST_R30_TIPS_GMV END AS LAST_R30_TIPS_GMV,
    TIPS_R30_GMV_DELTA,
    CASE WHEN CURRENT_R30_TOTAL_NMV IS NULL THEN 0 ELSE CURRENT_R30_TOTAL_NMV END AS CURRENT_R30_TOTAL_NMV,
    CASE WHEN LAST_R30_TOTAL_NMV IS NULL THEN 0 ELSE LAST_R30_TOTAL_NMV END AS LAST_R30_TOTAL_NMV,
    TOTAL_R30_NMV_DELTA,
    CASE WHEN CURRENT_R30_SUB_NMV IS NULL THEN 0 ELSE CURRENT_R30_SUB_NMV END AS CURRENT_R30_SUB_NMV,
    CASE WHEN LAST_R30_SUB_NMV IS NULL THEN 0 ELSE LAST_R30_SUB_NMV END AS LAST_R30_SUB_NMV,
    SUB_R30_NMV_DELTA,
    CASE WHEN CURRENT_R30_TIPS_NMV IS NULL THEN 0 ELSE CURRENT_R30_TIPS_NMV END AS CURRENT_R30_TIPS_NMV,
    CASE WHEN LAST_R30_TIPS_NMV IS NULL THEN 0 ELSE LAST_R30_TIPS_NMV END AS LAST_R30_TIPS_NMV,
    TIPS_R30_NMV_DELTA,
    CASE WHEN CURRENT_R30_POSTS IS NULL THEN 0 ELSE CURRENT_R30_POSTS END AS CURRENT_R30_POSTS,
    CASE WHEN LAST_R30_POSTS IS NULL THEN 0 ELSE LAST_R30_POSTS END AS LAST_R30_POSTS,
    POSTS_R30_DELTA,
    CASE WHEN CURRENT_R30_BLASTS IS NULL THEN 0 ELSE CURRENT_R30_BLASTS END AS CURRENT_R30_BLASTS,
    CASE WHEN LAST_R30_BLASTS IS NULL THEN 0 ELSE LAST_R30_BLASTS END AS LAST_R30_BLASTS,
    BLASTS_R30_DELTA,
    CASE WHEN CURRENT_R30_DM IS NULL THEN 0 ELSE CURRENT_R30_DM END AS CURRENT_R30_DM,
    CASE WHEN LAST_R30_DM IS NULL THEN 0 ELSE LAST_R30_DM END AS LAST_R30_DM,
    DM_R30_DELTA,

    CASE WHEN CURRENT_R60_TOTAL_GMV IS NULL THEN 0 ELSE CURRENT_R60_TOTAL_GMV END AS CURRENT_R60_TOTAL_GMV,
    CASE WHEN LAST_R60_TOTAL_GMV IS NULL THEN 0 ELSE LAST_R60_TOTAL_GMV END AS LAST_R60_TOTAL_GMV,
    TOTAL_R60_GMV_DELTA,
    CASE WHEN CURRENT_R60_SUB_GMV IS NULL THEN 0 ELSE CURRENT_R60_SUB_GMV END AS CURRENT_R60_SUB_GMV,
    CASE WHEN LAST_R60_SUB_GMV IS NULL THEN 0 ELSE LAST_R60_SUB_GMV END AS LAST_R60_SUB_GMV,
    SUB_R60_GMV_DELTA,
    CASE WHEN CURRENT_R60_TIPS_GMV IS NULL THEN 0 ELSE CURRENT_R60_TIPS_GMV END AS CURRENT_R60_TIPS_GMV,
    CASE WHEN LAST_R60_TIPS_GMV IS NULL THEN 0 ELSE LAST_R60_TIPS_GMV END AS LAST_R60_TIPS_GMV,
    TIPS_R60_GMV_DELTA,
    CASE WHEN CURRENT_R60_TOTAL_NMV IS NULL THEN 0 ELSE CURRENT_R60_TOTAL_NMV END AS CURRENT_R60_TOTAL_NMV,
    CASE WHEN LAST_R60_TOTAL_NMV IS NULL THEN 0 ELSE LAST_R60_TOTAL_NMV END AS LAST_R60_TOTAL_NMV,
    TOTAL_R60_NMV_DELTA,
    CASE WHEN CURRENT_R60_SUB_NMV IS NULL THEN 0 ELSE CURRENT_R60_SUB_NMV END AS CURRENT_R60_SUB_NMV,
    CASE WHEN LAST_R60_SUB_NMV IS NULL THEN 0 ELSE LAST_R60_SUB_NMV END AS LAST_R60_SUB_NMV,
    SUB_R60_NMV_DELTA,
    CASE WHEN CURRENT_R60_TIPS_NMV IS NULL THEN 0 ELSE CURRENT_R60_TIPS_NMV END AS CURRENT_R60_TIPS_NMV,
    CASE WHEN LAST_R60_TIPS_NMV IS NULL THEN 0 ELSE LAST_R60_TIPS_NMV END AS LAST_R60_TIPS_NMV,
    TIPS_R60_NMV_DELTA,
    CASE WHEN CURRENT_R60_POSTS IS NULL THEN 0 ELSE CURRENT_R60_POSTS END AS CURRENT_R60_POSTS,
    CASE WHEN LAST_R60_POSTS IS NULL THEN 0 ELSE LAST_R60_POSTS END AS LAST_R60_POSTS,
    POSTS_R60_DELTA,
    CASE WHEN CURRENT_R60_BLASTS IS NULL THEN 0 ELSE CURRENT_R60_BLASTS END AS CURRENT_R60_BLASTS,
    CASE WHEN LAST_R60_BLASTS IS NULL THEN 0 ELSE LAST_R60_BLASTS END AS LAST_R60_BLASTS,
    BLASTS_R60_DELTA,
    CASE WHEN CURRENT_R60_DM IS NULL THEN 0 ELSE CURRENT_R60_DM END AS CURRENT_R60_DM,
    CASE WHEN LAST_R60_DM IS NULL THEN 0 ELSE LAST_R60_DM END AS LAST_R60_DM,
    DM_R60_DELTA,
    CASE WHEN CURRENT_R90_TOTAL_GMV IS NULL THEN 0 ELSE CURRENT_R90_TOTAL_GMV END AS CURRENT_R90_TOTAL_GMV,
    CASE WHEN LAST_R90_TOTAL_GMV IS NULL THEN 0 ELSE LAST_R90_TOTAL_GMV END AS LAST_R90_TOTAL_GMV,
    TOTAL_R90_GMV_DELTA,
    CASE WHEN CURRENT_R90_SUB_GMV IS NULL THEN 0 ELSE CURRENT_R90_SUB_GMV END AS CURRENT_R90_SUB_GMV,
    CASE WHEN LAST_R90_SUB_GMV IS NULL THEN 0 ELSE LAST_R90_SUB_GMV END AS LAST_R90_SUB_GMV,
    SUB_R90_GMV_DELTA,
    CASE WHEN CURRENT_R90_TIPS_GMV IS NULL THEN 0 ELSE CURRENT_R90_TIPS_GMV END AS CURRENT_R90_TIPS_GMV,
    CASE WHEN LAST_R90_TIPS_GMV IS NULL THEN 0 ELSE LAST_R90_TIPS_GMV END AS LAST_R90_TIPS_GMV,
    TIPS_R90_GMV_DELTA,
    CASE WHEN CURRENT_R90_TOTAL_NMV IS NULL THEN 0 ELSE CURRENT_R90_TOTAL_NMV END AS CURRENT_R90_TOTAL_NMV,
    CASE WHEN LAST_R90_TOTAL_NMV IS NULL THEN 0 ELSE LAST_R90_TOTAL_NMV END AS LAST_R90_TOTAL_NMV,
    TOTAL_R90_NMV_DELTA,
    CASE WHEN CURRENT_R90_SUB_NMV IS NULL THEN 0 ELSE CURRENT_R90_SUB_NMV END AS CURRENT_R90_SUB_NMV,
    CASE WHEN LAST_R90_SUB_NMV IS NULL THEN 0 ELSE LAST_R90_SUB_NMV END AS LAST_R90_SUB_NMV,
    SUB_R90_NMV_DELTA,
    CASE WHEN CURRENT_R90_TIPS_NMV IS NULL THEN 0 ELSE CURRENT_R90_TIPS_NMV END AS CURRENT_R90_TIPS_NMV,
    CASE WHEN LAST_R90_TIPS_NMV IS NULL THEN 0 ELSE LAST_R90_TIPS_NMV END AS LAST_R90_TIPS_NMV,
    TIPS_R90_NMV_DELTA,
    CASE WHEN CURRENT_R90_POSTS IS NULL THEN 0 ELSE CURRENT_R90_POSTS END AS CURRENT_R90_POSTS,
    CASE WHEN LAST_R90_POSTS IS NULL THEN 0 ELSE LAST_R90_POSTS END AS LAST_R90_POSTS,
    POSTS_R90_DELTA,
    CASE WHEN CURRENT_R90_BLASTS IS NULL THEN 0 ELSE CURRENT_R90_BLASTS END AS CURRENT_R90_BLASTS,
    CASE WHEN LAST_R90_BLASTS IS NULL THEN 0 ELSE LAST_R90_BLASTS END AS LAST_R90_BLASTS,
    BLASTS_R90_DELTA,
    CASE WHEN CURRENT_R90_DM IS NULL THEN 0 ELSE CURRENT_R90_DM END AS CURRENT_R90_DM,
    CASE WHEN LAST_R90_DM IS NULL THEN 0 ELSE LAST_R90_DM END AS LAST_R90_DM,
    DM_R90_DELTA,

    DAY_SINCE_LAST_POST,
    DAY_SINCE_LAST_BLAST,
    DAY_SINCE_LAST_DM
    
FROM all_measures
ORDER BY 5 DESC