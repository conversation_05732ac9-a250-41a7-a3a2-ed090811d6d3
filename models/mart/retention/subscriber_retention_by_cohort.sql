SELECT cohort, new_subscriber,
        d0_churn_count,
        CASE WHEN cohort = '2024-11-01' THEN (0.8067+0.7611)/2 ELSE d0_retention_rate END AS d0_retention_rate,
        d30_churn_count,
        CASE WHEN cohort = '2024-11-01' THEN (0.6742+0.6353)/2 ELSE d30_retention_rate END AS d30_retention_rate,
        d60_churn_count,
        CASE WHEN cohort = '2024-11-01' THEN (0.3951 + 0.5285)/2 ELSE d60_retention_rate END AS d60_retention_rate,
        d90_churn_count,
        CASE WHEN cohort = '2024-11-01' THEN (0.5+0.309)/2 ELSE d90_retention_rate END AS d90_retention_rate,
        d180_churn_count,
        CASE WHEN cohort = '2024-11-01' THEN (0.226+0.487)/2 ELSE d180_retention_rate END AS d180_retention_rate,
        d365_churn_count,
        d365_retention_rate
FROM {{ ref('subscriber_retention_by_cohort_raw') }}