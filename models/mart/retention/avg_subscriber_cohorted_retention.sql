SELECT  'Rolling 18 Months' AS aggregated_metric,
        SUM(new_subscriber) AS total_new_subscriber,
        SUM(new_subscriber*d0_retention_rate)/SUM(CASE WHEN d0_retention_rate is null THEN 0 ELSE new_subscriber END) AS avg_d0_retention_rate,
        SUM(new_subscriber*d30_retention_rate)/SUM(CASE WHEN d30_retention_rate is null THEN 0 ELSE new_subscriber END) AS avg_d30_retention_rate,
        SUM(new_subscriber*d60_retention_rate)/SUM(CASE WHEN d60_retention_rate is null THEN 0 ELSE new_subscriber END) AS avg_d60_retention_rate,
        SUM(new_subscriber*d90_retention_rate)/SUM(CASE WHEN d90_retention_rate is null THEN 0 ELSE new_subscriber END) AS avg_d90_retention_rate,
        SUM(new_subscriber*d180_retention_rate)/SUM(CASE WHEN d180_retention_rate is null THEN 0 ELSE new_subscriber END) AS avg_d180_retention_rate,
        SUM(new_subscriber*d365_retention_rate)/SUM(CASE WHEN d365_retention_rate is null THEN 0 ELSE new_subscriber END) AS avg_d365_retention_rate
FROM {{ ref('subscriber_retention_by_cohort') }}
WHERE datediff('month', cohort, getdate())<= 18