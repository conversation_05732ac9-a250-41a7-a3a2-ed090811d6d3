with fan_first_month as (
    select fan_id, min(month) as first_month
    from {{ref("fact_monthly_fan_engagement")}}
    where num_creators_subscribed > 0
    group by 1
),
monthly_new_subs as (
    select first_month, count(distinct fan_id) as new_sub
    from fan_first_month 
    group by 1
),
monthly_engagement_expanded as (
    select * ,
            lag(num_creators_subscribed) over (partition by fan_id order by month) as last_month_num_creators_subscribed
    from {{ref("fact_monthly_fan_engagement")}}
),
monthly_summary as (
    select month, 
            count(distinct case when num_creators_subscribed > 0 then fan_id else null end) as subscriber_count,
            count(distinct case when num_creators_subscribed > 0  and last_month_num_creators_subscribed >0 then fan_id 
                    else null 
                end) as recurring_subscriber_count
    from monthly_engagement_expanded
    group by 1
)
select month, subscriber_count, recurring_subscriber_count, new_sub,
       recurring_subscriber_count/ (lag(subscriber_count) over (order by month)) as subscriber_retention_rate_mom
from monthly_summary ms
left join monthly_new_subs mns on mns.first_month = ms.month
order by month asc
