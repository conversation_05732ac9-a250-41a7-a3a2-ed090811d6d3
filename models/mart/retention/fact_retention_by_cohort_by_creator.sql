with sub_first_and_last_day as (
    select creator_id, customer_id, 
    min(created) as first_day, date_trunc('month', first_day) as first_month,
    max(case when canceled_at is null then dateadd('year', 12, current_date()) else canceled_at end) as last_day, date_trunc('month', last_day) as last_month,
    datediff('day', first_day, last_day) as interval
    from {{ ref ('fact_subscription_cohort')}}
    group by 1, 2
)
select creator_id, first_month as cohort, -- cp.fanfix_username,
        count(distinct customer_id) as new_subscriber, 
        count(distinct case when last_day <= current_date() then customer_id else null end) as total_churns,
        sum(case when interval <= 0 then 1 else 0 end) as d0_churn_count, 
        1- d0_churn_count/new_subscriber as d0_retention_rate,
        case 
            when datediff('day', first_month, getdate()) > 30 then sum(case when interval <= 30 then 1 else 0 end) 
            else null 
        end as d30_churn_count, 
        1- d30_churn_count/new_subscriber as d30_retention_rate,
        case 
            when datediff('day', first_month, getdate()) > 60 then sum(case when interval <= 60 then 1 else 0 end) 
            else null
        end as d60_churn_count,
        1 - d60_churn_count/new_subscriber as d60_retention_rate,
        case 
            when datediff('day', first_month, getdate()) > 90 then sum(case when interval <= 90 then 1 else 0 end)
            else null
        end as d90_churn_count,
        1 - d90_churn_count/new_subscriber as d90_retention_rate,
        case 
            when datediff('day', first_month, getdate()) > 180 then sum(case when interval <= 180 then 1 else 0 end)
            else null
        end as d180_churn_count,
        1 - d180_churn_count/new_subscriber as d180_retention_rate,
        case
            when datediff('day', first_month, getdate()) > 365 then sum(case when interval <= 365 then 1 else 0 end) 
            else null
        end as d365_churn_count,
        1 - d365_churn_count/new_subscriber as d365_retention_rate
from sub_first_and_last_day  r
group by 1, 2 order by cohort desc