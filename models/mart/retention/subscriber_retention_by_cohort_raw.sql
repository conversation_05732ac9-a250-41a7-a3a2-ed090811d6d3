WITH sub_first_and_last_day AS (
    -- SELECT customer_id,
    --     <PERSON><PERSON>(subscription_created_date) AS first_day, date_trunc('month', first_day) AS first_month,
    --     MAX(subscription_created_date) AS last_sub_creation_day,
    --     MAX(CASE WHEN subscription_canceled_at is null THEN dateadd('year', 12, current_date()) ELSE subscription_canceled_at END) AS last_day, date_trunc('month', last_day) AS last_month,
    -- datediff('day', first_day, last_day) AS interval
    -- FROM FANFIX_VIEW_PROD.ANALYTICS.FACT_SUBSCRIPTIONS_WITH_VALID_CHARGES
    -- GROUP BY 1
    SELECT customer_id,
        MIN(created) AS first_day, date_trunc('month', first_day) AS first_month,
        MAX(created) AS last_sub_creation_day,
        MAX(CASE WHEN canceled_at is null THEN dateadd('year', 12, current_date()) ELSE canceled_at END) AS last_day, date_trunc('month', last_day) AS last_month,
    datediff('day', first_day, last_day) AS interval
    FROM {{ ref('fact_subscription_cohort') }} s
    GROUP BY 1
)
SELECT first_month AS cohort, COUNT(DISTINCT customer_id) AS new_subscriber,
        SUM(CASE WHEN interval <= 0 THEN 1 ELSE 0 END) AS d0_churn_count,
        1- d0_churn_count/new_subscriber AS d0_retention_rate,
        CASE
            WHEN datediff('day', first_month, getdate()) > 30 THEN SUM(CASE WHEN interval <= 30 THEN 1 ELSE 0 END)
            ELSE null
        END AS d30_churn_count,
        1- d30_churn_count/new_subscriber AS d30_retention_rate,
        CASE
            WHEN datediff('day', first_month, getdate()) > 60 THEN SUM(CASE WHEN interval <= 60 THEN 1 ELSE 0 END)
            ELSE null
        END AS d60_churn_count,
        1 - d60_churn_count/new_subscriber AS d60_retention_rate,
        CASE
            WHEN datediff('day', first_month, getdate()) > 90 THEN SUM(CASE WHEN interval <= 90 THEN 1 ELSE 0 END)
            ELSE null
        END AS d90_churn_count,
        1 - d90_churn_count/new_subscriber AS d90_retention_rate,
        CASE
            WHEN datediff('day', first_month, getdate()) > 180 THEN SUM(CASE WHEN interval <= 180 THEN 1 ELSE 0 END)
            ELSE null
        END AS d180_churn_count,
        1 - d180_churn_count/new_subscriber AS d180_retention_rate,
        CASE
            WHEN datediff('day', first_month, getdate()) > 365 THEN SUM(CASE WHEN interval <= 365 THEN 1 ELSE 0 END)
            ELSE null
        END AS d365_churn_count,
        1 - d365_churn_count/new_subscriber AS d365_retention_rate
FROM SUB_FIRST_AND_LAST_DAY
GROUP BY first_month
HAVING new_subscriber > 0
ORDER BY 1