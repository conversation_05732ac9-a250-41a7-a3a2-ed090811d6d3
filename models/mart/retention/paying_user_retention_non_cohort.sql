with fan_first_month as (
    select fan_id, min(month) as first_month
    from {{ref("fact_monthly_fan_engagement")}}
    where total_revenue_spent > 0
    group by 1
),
monthly_new_fan as (
    select first_month, count(distinct fan_id) as new_paying_user
    from fan_first_month 
    group by 1
),
monthly_engagement_expanded as (
    select * ,
            lag(total_revenue_spent) over (partition by fan_id order by month) as last_month_spent
    from {{ref("fact_monthly_fan_engagement")}}
),
monthly_summary as (
    select month, 
            count(distinct case when total_revenue_spent > 0 then fan_id else null end) as paying_user_count,
            count(distinct case when total_revenue_spent > 0  and last_month_spent >0 then fan_id 
                    else null 
                end) as recurring_paying_user_count,
            count(distinct case when total_revenue_spent = 0  and last_month_spent >0 then fan_id 
                else null 
            end) as churned_paying_user_count
    from monthly_engagement_expanded
    group by 1
)
select month, paying_user_count, recurring_paying_user_count, new_paying_user, churned_paying_user_count,
        recurring_paying_user_count/ (lag(paying_user_count) over (order by month)) as paying_user_retention_rate_mom
from monthly_summary ms
left join monthly_new_fan mns on mns.first_month = ms.month
order by month asc
