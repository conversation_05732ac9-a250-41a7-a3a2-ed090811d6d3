WITH promo_creator_pool AS (
SELECT DISTINCT fanfix_id
FROM {{ ref('dim_promo_code_creator_adoption') }}
),

first_sub_date as (
    SELECT customer_id, min(subscription_created_at) as first_subscription_at, 
    FROM {{ ref('fact_subscriptions') }}
    WHERE creator_fanfix_id in (SELECT fanfix_id FROM promo_creator_pool)
    GROUP BY customer_id
),

sub_first_and_last_day AS (
    SELECT s.customer_id, 
        s.subscription_created_at AS first_day, date_trunc('month', first_day) AS first_month,
        CASE WHEN s.subscription_canceled_at is null THEN dateadd('year', 12, current_date()) ELSE subscription_canceled_at END AS last_day, date_trunc('month', last_day) AS last_month,
        datediff('day', first_day, last_day) AS sub_interval,
        CASE WHEN s.applied_promotion_id is null THEN false ELSE true END AS promo_code_flag,
        CASE WHEN s.subscription_created_at = sd.first_subscription_at THEN 'new subscriber' ELSE 'returning subscriber' END AS new_or_returning
    FROM {{ ref('fact_subscriptions') }} s JOIN first_sub_date sd ON s.customer_id = sd.customer_id
    WHERE 1=1
    AND s.creator_fanfix_id in (SELECT fanfix_id FROM promo_creator_pool)
    AND s.subscription_created_at >= '2025-10-01'
),

max_interval_per_customer AS (
    SELECT customer_id, promo_code_flag, first_month, new_or_returning, max(sub_interval) AS interval
    FROM sub_first_and_last_day
    GROUP BY customer_id, promo_code_flag, first_month, new_or_returning
)

SELECT first_month AS cohort, promo_code_flag, new_or_returning,
COUNT(DISTINCT customer_id) AS subscribers,
    SUM(CASE WHEN interval <= 0 THEN 1 ELSE 0 END) AS d0_churn_count,
        1- d0_churn_count/subscribers AS d0_retention_rate,
        CASE
            WHEN datediff('day', first_month, getdate()) > 30 THEN SUM(CASE WHEN interval <= 30 THEN 1 ELSE 0 END)
            ELSE null
        END AS d30_churn_count,
        1- d30_churn_count/subscribers AS d30_retention_rate,
        CASE
            WHEN datediff('day', first_month, getdate()) > 60 THEN SUM(CASE WHEN interval <= 60 THEN 1 ELSE 0 END)
            ELSE null
        END AS d60_churn_count,
        1 - d60_churn_count/subscribers AS d60_retention_rate,
        CASE
            WHEN datediff('day', first_month, getdate()) > 90 THEN SUM(CASE WHEN interval <= 90 THEN 1 ELSE 0 END)
            ELSE null
        END AS d90_churn_count,
        1 - d90_churn_count/subscribers AS d90_retention_rate,
        CASE
            WHEN datediff('day', first_month, getdate()) > 180 THEN SUM(CASE WHEN interval <= 180 THEN 1 ELSE 0 END)
            ELSE null
        END AS d180_churn_count,
        1 - d180_churn_count/subscribers AS d180_retention_rate,
        CASE
            WHEN datediff('day', first_month, getdate()) > 365 THEN SUM(CASE WHEN interval <= 365 THEN 1 ELSE 0 END)
            ELSE null
        END AS d365_churn_count,
        1 - d365_churn_count/subscribers AS d365_retention_rate
FROM max_interval_per_customer
WHERE 1=1
--AND cohort >= '2025-10-01'
GROUP BY first_month, promo_code_flag, new_or_returning
HAVING subscribers > 0
ORDER BY 1 DESC, 2 DESC, 3
