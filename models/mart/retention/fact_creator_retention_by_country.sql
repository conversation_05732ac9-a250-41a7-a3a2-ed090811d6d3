WITH creator_mom_activities AS (
    SELECT  a.*, e.total_revenue,  
        CASE WHEN p.country IN ('United States', 'Brazil', 'MENA') THEN p.country ELSE 'Other' END AS country_group,
        lag(a.month, 1) OVER (PARTITION BY a.creator_id, a.username, CASE WHEN p.country IN ('United States', 'Brazil', 'MENA') THEN p.country ELSE 'Other' END ORDER BY a.month) AS last_month,
        lag(total_revenue, 1) over (partition by a.creator_id, a.username, CASE WHEN p.country IN ('United States', 'Brazil', 'MENA') THEN p.country ELSE 'Other' END ORDER BY a.month) AS last_month_revenue
    FROM {{ref ('fact_monthly_creator_activity')}} a
    LEFT JOIN {{ ref ('fact_monthly_creator_earnings_bi')}} e ON e.creator_id = a.creator_id AND e.month = a.month
    LEFT JOIN {{ ref ('dim_creator_profile')}} p ON a.creator_id = p.fanfix_id
    WHERE post_count > 0 OR blast_count > 0
),
active_creator_by_month AS (
    SELECT country_group as country, month,
        COUNT(DISTINCT creator_id) AS total_active_creator,
        SUM(CASE WHEN last_month is NOT null AND datediff('month', last_month, month) = 1 THEN 1 ELSE 0 END) AS roll_over_active_creator,
        SUM(CASE WHEN last_month is NOT null AND datediff('month', last_month, month) != 1 THEN 1 ELSE 0 END) AS reactivated_creator,
        SUM(CASE WHEN last_month is null THEN 1 ELSE 0 END) AS new_active_creator,

        SUM(CASE WHEN total_revenue >= 7000 THEN 1 ELSE 0 END) AS total_active_super_creator,
        SUM(CASE WHEN last_month is NOT null AND datediff('month', last_month, month) = 1
                    AND total_revenue >= 7000 AND last_month_revenue >= 7000 THEN 1 ELSE 0 END) AS roll_over_active_super_creator,
        SUM(CASE WHEN last_month is NOT null AND datediff('month', last_month, month) != 1
                    AND total_revenue >= 7000 AND last_month_revenue >= 7000 THEN 1 ELSE 0 END) AS reactivated_super_creator,
        SUM(CASE WHEN (last_month is null AND total_revenue >= 7000 )
                    OR (last_month is NOT null AND total_revenue >= 7000 AND last_month_revenue < 7000)
                    THEN 1 ELSE 0 END) AS new_super_creator
    FROM CREATOR_MOM_ACTIVITIES
    where month >= '2023-01-01'
    GROUP BY country, month ORDER BY month desc, country
)
SELECT country, month,
        total_active_creator, roll_over_active_creator, reactivated_creator, new_active_creator,
        lag(total_active_creator, 1) over (ORDER BY month) AS last_month_total_active_creator,
        roll_over_active_creator/nullif(last_month_total_active_creator,0) AS creator_retention_rate,

         total_active_super_creator, roll_over_active_super_creator, reactivated_super_creator, new_super_creator,
         lag(total_active_super_creator, 1) over (ORDER BY month) AS last_month_total_active_super_creator,
         roll_over_active_super_creator/nullif(last_month_total_active_super_creator,0) AS super_creator_retention_rate
FROM ACTIVE_CREATOR_BY_MONTH
WHERE month > '2023-01-01'
ORDER BY month desc