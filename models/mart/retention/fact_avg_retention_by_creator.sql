select  creator_id,--  cp.fanfix_username,
        count(*) as month_since_first_sub,
        sum(new_subscriber) as total_new_subscriber,
        sum(total_churns) as lifetime_churns,
        sum(new_subscriber*d0_retention_rate)/sum(case when d0_retention_rate is null then 0 else new_subscriber end) as avg_d0_retention_rate,
        sum(new_subscriber*d30_retention_rate)/sum(case when d30_retention_rate is null then 0 else new_subscriber end) as avg_d30_retention_rate,
        sum(new_subscriber*d60_retention_rate)/sum(case when d60_retention_rate is null then 0 else new_subscriber end) as avg_d60_retention_rate,
        sum(new_subscriber*d90_retention_rate)/sum(case when d90_retention_rate is null then 0 else new_subscriber end) as avg_d90_retention_rate,
        sum(new_subscriber*d180_retention_rate)/sum(case when d180_retention_rate is null then 0 else new_subscriber end) as avg_d180_retention_rate,
        sum(new_subscriber*d365_retention_rate)/sum(case when d365_retention_rate is null then 0 else new_subscriber end) as avg_d365_retention_rate
from {{ ref ('fact_retention_by_cohort_by_creator')}} r
-- left join fanfix_datamart_prod.analytics.dim_creator_profile cp on cp.fanfix_id = r.creator_id
group by 1 order by total_new_subscriber desc