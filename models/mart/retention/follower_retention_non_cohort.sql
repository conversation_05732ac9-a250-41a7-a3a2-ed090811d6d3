with fan_first_month as (
    select fan_id, min(month) as first_month
    from {{ref("fact_monthly_fan_engagement")}}
    where num_creators_followed > 0
    group by 1
),
monthly_new_follower as (
    select first_month, count(distinct fan_id) as new_follower
    from fan_first_month 
    group by 1
),
monthly_engagement_expanded as (
    select * ,
            lag(num_creators_followed) over (partition by fan_id order by month) as last_month_follow
    from {{ref("fact_monthly_fan_engagement")}}
),
monthly_summary as (
    select month, 
            count(distinct case when num_creators_followed > 0 then fan_id else null end) as follower_count,
            count(distinct case when num_creators_followed  > 0  and last_month_follow >0 then fan_id 
                    else null 
                end) as recurring_follower_count
    from monthly_engagement_expanded
    group by 1
)
select month, follower_count, recurring_follower_count, new_follower,
        recurring_follower_count/ (lag(follower_count) over (order by month)) as follower_retention_rate_mom
from monthly_summary ms
left join monthly_new_follower mns on mns.first_month = ms.month
where month > '2024-02-01'
order by month asc
