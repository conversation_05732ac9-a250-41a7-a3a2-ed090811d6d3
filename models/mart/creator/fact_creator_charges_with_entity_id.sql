WITH related_entity as (
    select
        distinct
        c.charge_id as charge_id,
        p.related_entity_id
    from {{ref("stg_stripe_charges")}} c
    left join {{ref("stg_fanfix_payment_payments")}} p
    on c.payment_intent = p.payment_processor_transaction_id
    where p.payment_processor = 'Stripe'
)
SELECT fcc.*,re.related_entity_id
FROM {{ref("fact_creator_charges")}} fcc
left join related_entity re on re.charge_id = fcc.charge_id