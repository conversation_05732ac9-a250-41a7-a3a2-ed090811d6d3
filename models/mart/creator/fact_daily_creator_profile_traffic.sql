SELECT 
    daily, 
    cp.fanfix_id as creator_id,
    creator_username, 
    standardized_referring_domain, 
    total_pageview, 
    unique_visitors,
    row_number() over (partition by daily, creator_username ORDER BY unique_visitors desc) AS referring_domain_ranking_desc
FROM {{ref("stg_custom_daily_creator_profile_traffic")}} pv
LEFT JOIN {{ref("dim_creator_profile")}} cp on cp.fanfix_username = pv.creator_username