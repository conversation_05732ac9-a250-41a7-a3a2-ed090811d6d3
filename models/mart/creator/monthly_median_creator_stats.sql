with month_spine as (
SELECT 
dateadd(month, seq4(), '2021-01-01'::DATE) as month
FROM table(generator(rowcount => 2500))  -- Covers ~7 years
WHERE dateadd(month, seq4(), '2021-01-01'::DATE) <= current_date()
),

monthly_posts as (
select creator_id, date_trunc('month', created_at)::date as month_start, 
count(distinct post_id) as monthly_posts
from {{ ref ('fact_post_stats') }}
where sunroom_asset_label != 'sunroom_legacy_assets'
group by 1, 2
),

monthly_blasts as (
select creator_id, date_trunc('month', created_at)::date as month_start, 
count(distinct message_blast_id) as monthly_blasts
from {{ ref ('fact_blast_stats') }}
where sunroom_asset_label != 'sunroom_legacy_assets'
group by 1, 2
),

monthly_messages as (
select creator_id, date_trunc('month', created_day)::date as month_start, 
sum(total_daily_outgoing_dm_count) as monthly_messages
from {{ ref ('fact_daily_creator_dm') }}
group by 1, 2
),

post_median as (select month_start as month, 
percentile_cont(0.5) within group (order by monthly_posts) as median_post_count
from monthly_posts
group by 1
),

blast_median as (select month_start as month, 
percentile_cont(0.5) within group (order by monthly_blasts) as median_blast_count
from monthly_blasts
group by 1
),

message_median as (select month_start as month, 
percentile_cont(0.5) within group (order by monthly_messages) as median_message_count
from monthly_messages
group by 1
)

select ms.month, median_post_count, median_blast_count, median_message_count 
from month_spine ms left join post_median p on ms.month = p.month
left join blast_median b on ms.month = b.month
left join message_median m on ms.month = m.month
order by 1 desc
