SELECT
        tne.creator_fanfix_id,
        tne.creator_username,

        tne.lifetime_net_revenue,
        tne.milestone_achieved AS lifetime_earning_milestone_achieved,
        tne.milestone_achieved_date AS lifetime_earning_milestone_achieved_date,

        fwe.first_week_net_revenue,
        fwe.milestone_achieved AS first_week_earning_milestone_achieved,
        fwe.milestone_achieved_date AS first_week_earning_milestone_achieved_date,

        le.lifetime_livestream_net_revenue,
        le.milestone_achieved AS livestream_earning_milestone_achieved,
        le.milestone_achieved_date AS livestream_earning_milestone_achieved_date,

        ts.lifetime_subscriber_count,
        ts.milestone_achieved AS subscriber_milestone_achieved,
        ts.milestone_achieved_date AS subscriber_milestone_achieved_date,

        am.active_month_count,
        am.milestone_achieved AS og_creator_milestone_achieved,
        am.milestone_achieved_date AS og_creator_milestone_achieved_date

FROM {{ref("fact_creator_milestone_total_net_earnings")}} tne
LEFT JOIN {{ref("fact_creator_milestone_first_week_earnings")}} fwe ON fwe.creator_fanfix_id = tne.creator_fanfix_id
LEFT JOIN {{ref("fact_creator_milestone_total_livestream_earnings")}} le ON le.creator_fanfix_id = tne.creator_fanfix_id
LEFT JOIN {{ref("fact_creator_milestone_total_subscriber")}} ts ON ts.creator_fanfix_id = tne.creator_fanfix_id
LEFT JOIN {{ref("fact_creator_milestone_active_month")}} am ON am.creator_id = tne.creator_fanfix_id