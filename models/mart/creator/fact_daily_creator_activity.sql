with activities_all AS (
    (SELECT * FROM {{ref("fact_daily_creator_posts_agg")}} GROUP BY 1,2,3,4)
    UNION
    (SELECT * FROM {{ref("fact_daily_creator_blasts_agg")}} GROUP BY 1,2,3,4)
    UNION
    (SELECT * FROM {{ref("fact_daily_creator_dm_agg")}} GROUP BY 1,2,3,4)
)
SELECT creator_id, u.username, created_day,
    coalesce(SUM(CASE WHEN type = 'posts' THEN COUNT ELSE 0 END), 0) AS post_count,
    coalesce(SUM(CASE WHEN type = 'blasts' THEN COUNT ELSE 0 END), 0) AS blast_count,
    -- coalesce(SUM(CASE WHEN type = 'incoming_dm' THEN COUNT ELSE 0 END), 0) AS incoming_dm_count,
    coalesce(SUM(CASE WHEN type = 'outgoing_dm' THEN COUNT ELSE 0 END), 0) AS outgoing_dm_count
FROM ACTIVITIES_ALL a
LEFT JOIN {{ref("stg_fanfix_user_profiles")}} u ON u.user_id = a.creator_id
JOIN {{ref("stg_fanfix_user_users")}} uu ON uu.user_id = a.creator_id
WHERE uu.roles not like '%admin%'-- AND uu.email NOT like '%fanfix.io%'
GROUP BY 1,2,3
-- HAVING (post_count > 0 OR blast_count > 0 OR outgoing_dm_count > 0)
ORDER BY created_day desc, creator_id