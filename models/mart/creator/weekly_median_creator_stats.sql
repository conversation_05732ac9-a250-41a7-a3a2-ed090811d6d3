with week_spine as (
SELECT 
dateadd(week, seq4(), '2021-01-04'::DATE) as week
FROM table(generator(rowcount => 2500))  -- Covers ~7 years
WHERE dateadd(week, seq4(), '2021-01-04'::DATE) <= current_date()
),

weekly_posts as (
select creator_id, date_trunc('week', created_at)::date as week_start, 
count(distinct post_id) as weekly_posts
from {{ ref ('fact_post_stats') }}
where sunroom_asset_label != 'sunroom_legacy_assets'
group by 1, 2
),

weekly_blasts as (
select creator_id, date_trunc('week', created_at)::date as week_start, 
count(distinct message_blast_id) as weekly_blasts
from {{ ref ('fact_blast_stats') }}
where sunroom_asset_label != 'sunroom_legacy_assets'
group by 1, 2
),

weekly_messages as (
select creator_id, date_trunc('week', created_day)::date as week_start, 
sum(total_daily_outgoing_dm_count) as weekly_messages
from {{ ref ('fact_daily_creator_dm') }}
group by 1, 2
),

post_median as (select week_start as week, 
percentile_cont(0.5) within group (order by weekly_posts) as median_post_count
from weekly_posts
group by 1
),

blast_median as (select week_start as week, 
percentile_cont(0.5) within group (order by weekly_blasts) as median_blast_count
from weekly_blasts
group by 1
),

message_median as (select week_start as week, 
percentile_cont(0.5) within group (order by weekly_messages) as median_message_count
from weekly_messages
group by 1
)

select ws.week, median_post_count, median_blast_count, median_message_count 
from week_spine ws left join post_median p on ws.week = p.week
left join blast_median b on ws.week = b.week
left join message_median m on ws.week = m.week
order by 1 desc


