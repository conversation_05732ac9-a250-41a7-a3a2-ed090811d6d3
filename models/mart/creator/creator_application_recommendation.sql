with one_time_run as (
    select creator_id as fanfix_id, 
            instagram as ig_handle, 
            follower_count, 
            engagememt_rate as engagement_rate,
            is_verified, 
            case when contnet_stats = 'no data' then null else contnet_stats end as content_stats,
            case when audience_type = 'no data' then null else audience_type end as audience_types,
            case when country = 'no data' then null else country end as country,
            case when city = 'no data' then null else city end as city,
            case when age = 'no data' then null else age end as age_group
    from {{ref("creator_application_vetted_01012026_to_01182026")}}
),
unioned_report as (
    (select * from one_time_run) 
    union all 
    (select * from {{ref("stg_modash_ig_influencer_report")}})
),
duplication_counts as (
    select * ,
            row_number() over (partition by fanfix_id order by follower_count desc) as number_of_creator_occurance
    from unioned_report
),
dedupped as (
    select * from duplication_counts where number_of_creator_occurance = 1
)
select dedupped.*, 
        u.username,
        u.email,
        vi.applied_at,
        vi.accepted_at,
        vi.rejected_at,
        tiktok as tiktok_handle,
        links.name as source
from {{ref("fact_creator_applications_with_valid_instagram")}} vi
join dedupped on dedupped.fanfix_id = vi.creator_id
left join {{ref("creator_onboard_trackable_links")}} links on links.code = vi.source
left join {{ref("stg_fanfix_user_users")}} u on u.user_id = vi.creator_id
