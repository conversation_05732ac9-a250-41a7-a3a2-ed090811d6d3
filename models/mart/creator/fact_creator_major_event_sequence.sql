with launches as (
    select launch_date as event_date, fanfix_id, 'launch' as creator_event_type, launch_revenue as revenue_impact
    from {{ref("dim_creator_profile")}} cp
    where launch_date is not null
    group by 1,2,3,4
),
first_30_day as (
    select first_earning_day as event_date, fanfix_id, 'first earning' as creator_event_type, first_30day_total_rev as revenue_impact
    from {{ref("dim_creator_profile")}} cp
    group by 1,2,3,4
),
churn as (
    select dateadd('month', 1, last_activity_date) as event_date, fanfix_id, 'churn' as creator_event_type, churn_revenue as revenue_impact
    from {{ref("dim_creator_profile")}} cp
    where last_activity_date is not null
    group by 1,2,3,4
),
last_activity as (
    select last_activity_date as event_date, fanfix_id, 'last activity' as creator_event_type, null as revenue_impact
    from {{ref("dim_creator_profile")}} cp
    where last_activity_date is not null
    group by 1,2,3,4
)
select * from ((select * from launches) union (select * from churn) union (select * from first_30_day) union (select * from last_activity))
where event_date < current_date()