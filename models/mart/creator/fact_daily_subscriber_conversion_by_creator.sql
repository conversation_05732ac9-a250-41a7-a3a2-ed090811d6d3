select  cp.fanfix_id, 
            cp.launch_date,
            sfc.* ,
            subscribe_button_clicked_users/nullif(pageview_users, 0) as subscribe_intent_conversion,
            payment_intent_users/nullif(subscribe_button_clicked_users, 0) as membership_selection_rate,
            payment_initiated_users/nullif(payment_intent_users,0) as checkout_completion_rate
from {{ref("stg_posthog_custom_daily_creator_subscription_flow_conversion")}} sfc
left join {{ref("dim_creator_profile")}} cp on sfc.creator_username = cp.fanfix_username