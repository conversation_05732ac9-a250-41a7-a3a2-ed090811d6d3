with referral_map as (
    select code, name, source_category from {{ref("creator_onboard_trackable_links")}} order by name
),
tier_sub_counts as (
    select creator_id, count(distinct subscription_plan_id) as tier_sub_count
    from  {{ref("stg_fanfix_subscription_subscription_plans")}}
    where is_active = True
    group by 1
),
creator_profile_expanded as (
    select contacts.id as record_id,
        fd.creator_id as fanfix_id, 
        fd.username as fanfix_username, 
        up.first_name as firstname, up.last_name as lastname, 
        case when up.last_name is null then up.first_name 
            else up.first_name|| ' ' || up.last_name  
        end AS creator_name,
        u.email, contacts.phone, contacts.create_date,  
        case when fd.launch_date is not null then fd.launch_date else fd.first_earning_day end as launch_date,
        fd.launch_date as hubspot_launch_date,  
        fd.accepted_date, fd.rejected_date, fd.first_earning_day, fd.first_post_day,

        case when fe.launch_date is not null then  fe.launch_revenue else fe.first_30day_total_rev  end as launch_revenue,
        fe.launch_revenue_without_fee_f180d,
        fe.first_30day_total_rev, 
        fe.launch_revenue as hubspot_launch_revenue,
        fe.first7day_revenue,
        fe.first_30day_subscribers,
        deal_creator_id,
        deal_creator.contact_owner  AS deal_creator,
        contacts.contact_owner, 
        contacts.contact_owner_email, fcc.country,

        -- agency
        contacts.agency_managed_date,
        case when contacts.account_management_agency = '<nil>' then null
            when trim(contacts.account_management_agency) = '' then null
            else contacts.account_management_agency end as account_management_agency,

        -- contracts
        contacts.contract_term_length::integer as contract_term_length,
        contacts.hubspot_link,
        contacts.minimum_guarantee_monthly::integer as minimum_guarantee_monthly,
        contacts.minimum_guarantee_start_date::date as minimum_guarantee_start_date,
        contacts.minimum_guarantee_end_date::date as minimum_guarantee_end_date,
        contacts.minimum_guarantee_cancellation_date::date as minimum_guarantee_cancellation_date,
        contacts.active_mg,
        contacts.relaunch,
        -- source
        case 
            when profile.source <> '<nil>' then source 
            else null 
        end as source_code,
        case 
            when profile.source <> '<nil>' then rm.name 
            else null
        end as source_name,
        case 
            when profile.source <> '<nil>' then rm.source_category 
            else null
        end as source_category,
        contacts.lead_source,
        case when profile.source = 'sunroom' then 'Sunroom'
            when (fcc.country = 'MENA' or contacts.fanfix_username = 'kingkailey') then 'MENA' 
            when (fcc.country = 'Brazil') then 'Brazil'
            when (((fcc.country != 'MENA'  and fcc.country != 'Brazil') or fcc.country is null) and lead_source = 'CPart') then 'US CPart'
            when lead_source in ('Inbound', 'Modash', 'Relaunch', 'Sam Betesh') then 'Other'
            when lead_source = 'Fanhouse' then 'Company Referral'
            when lead_source = 'Event' then 'Other'
            when lead_source = 'Company Referral' and referrer_name = 'Cameron Dallas' then 'Cameron Dallas'
            else lead_source
        end as lead_source_main_category,
        cs_managed, vertical, contacts.prospected_by,

        -- bio and social
        profile.persona_status as persona_completion,
        case when (social.instagram != '<nil>' and trim(social.instagram) != '') then social.instagram else null end as instagram,
        case when (social.tiktok != '<nil>' and trim(social.tiktok) != '') then social.tiktok else null end as tiktok,
        case when (up.bio != '<nil>' and trim(up.bio) != '') then bio else null end as bio,
        
        case when up.avatar_url is not null and up.avatar_url != '<nil>' then up.avatar_url else null end as profile_picture,
        case when up.banner_url is not null and up.banner_url != '<nil>' then up.banner_url else null end as cover_photo,
        case 
            when ic.creator_id is not null then 'inactive'
            when rejected_at is not null then 'rejected'
            else 'active'
        end as inactive_creator,
        stripe_accounts.stripe_account_id,

        contacts.fanfix_posts::integer as mg_post_deliverable,
        contacts.message_blasts::integer as mg_blast_deliverable,
        cr.last_activity_date,
        cr.churn_revenue,
        contacts.CHURN_REASON,
        contacts.CHURN_REASON_COMPETITIVE_PLATFORM,
        contacts.relaunch_date,
        contacts.relaunch_manager as relaunch_manager_id,
        deal_creator2.contact_owner  AS relaunch_manager,
        fe.relaunch_revenue,
        ifnull(tsc.tier_sub_count, 0) as number_of_subscription_tiers
    from {{ref("stg_fanfix_creator_profiles")}} profile 
    left join {{ref("dim_creator_first_days")}} fd on profile.creator_id = fd.creator_id
    left join {{ref("dim_creator_first30day_earnings")}} fe on profile.creator_id = fe.creator_id 
    left join {{ref("dim_hubspot_contacts")}} contacts on profile.creator_id = contacts.fanfix_id
    LEFT JOIN {{ref("stg_hubspot_owners")}} deal_creator on contacts.deal_creator_id = deal_creator.contact_owner_id
    LEFT JOIN {{ref("stg_hubspot_owners")}} deal_creator2 on contacts.relaunch_manager = deal_creator2.contact_owner_id
    left join {{ref("stg_fanfix_social_accounts")}} social on social.creator_id = profile.creator_id
    left join {{ref("stg_fanfix_user_profiles")}} up on up.user_id = profile.creator_id
    inner join {{ref("stg_fanfix_user_users")}} u on u.user_id = profile.creator_id
    left join {{ref("fact_inactive_creators")}} ic on ic.creator_id = profile.creator_id
    left join {{ref("creator_stripe_accounts")}} stripe_accounts on stripe_accounts.creator_id = profile.creator_id
    left join referral_map rm on rm.code = profile.source
    left join {{ref("fact_creator_churn_revenue")}} cr on cr.creator_id = profile.creator_id
    left join tier_sub_counts tsc on tsc.creator_id = profile.creator_id
    left join {{ref("fact_creator_country")}} fcc on fcc.fanfix_id = profile.creator_id
    left join {{ref("sunroom_creator_activation")}} sca on sca.creator_fanfix_id = profile.creator_id
    where (profile.accepted_at is not null or fd.first_earning_day is not null)  
            and (u.roles not like '%admin%')
            and (sca.account_activation_flag is null or sca.account_activation_flag = 'Account Activated')
    --and (contacts.PROPERTIES_HS_MERGED_OBJECT_IDS = '<nil>' OR contacts.PROPERTIES_HS_MERGED_OBJECT_IDS IS NULL)
    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,
    34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63
    order by fd.accepted_date desc
)
select * ,
    {{get_creator_launch_revenue_cohort('launch_revenue')}} as launch_revenue_cohort
from creator_profile_expanded
