with gmv_by_day_by_creator as 
(
    select date_trunc('day', time) as day, creator_fanfix_id, creator_username, sum(GROSS_REVENUE) as total_gmv,
            sum(case when type like '%subscription%' then GROSS_REVENUE else 0 end) as subs_gmv,
            sum(case when type not like '%subscription%' then gross_revenue else 0 end) as tips_gmv,
            count(distinct case when type like '%subscription creation%' then fan_fanfix_id else null end) as new_subscriber
    from {{ref("fact_creator_charges")}} AS f
    --where time < current_date() 
    group by 1,2,3
)
select *, 
        lag(total_gmv, 1) over (partition by creator_fanfix_id order by day asc) as prev_day_total_gmv,
        lag(subs_gmv, 1) over (partition by creator_fanfix_id order by day asc) as prev_day_subs_gmv,
        lag(tips_gmv, 1) over (partition by creator_fanfix_id order by day asc) as prev_day_tips_gmv,
        lag(new_subscriber, 1) over (partition by creator_fanfix_id order by day asc) as prev_day_new_subscriber,

        case when prev_day_total_gmv is null then 1  
            when prev_day_total_gmv = 0 then null
            else (total_gmv - prev_day_total_gmv)/prev_day_total_gmv end as daily_total_gmv_delta,
        case when prev_day_subs_gmv is null then 1 
            when prev_day_subs_gmv = 0 then null
            else (subs_gmv - prev_day_subs_gmv)/prev_day_subs_gmv end as daily_subs_gmv_delta,
        case when prev_day_tips_gmv is null then 1 
            when prev_day_tips_gmv = 0 then null
            else (tips_gmv - prev_day_tips_gmv)/prev_day_tips_gmv end as daily_tips_gmv_delta,
        case when prev_day_new_subscriber is null  then 1 
            when prev_day_new_subscriber = 0 then null
            else (new_subscriber - prev_day_new_subscriber)/prev_day_new_subscriber end as daily_new_subscriber_delta
from gmv_by_day_by_creator
where day > dateadd('day', -30, current_date())
order by day desc, total_gmv desc