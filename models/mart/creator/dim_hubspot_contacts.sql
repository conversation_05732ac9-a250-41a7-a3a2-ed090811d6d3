with
    removed as (
        select distinct replace(hs_merged_object_ids, ';', ',') as id
        from {{ ref("stg_hubspot_contacts") }}
        where hs_merged_object_ids <> '<nil>' and hs_merged_object_ids is not null
    ),
    removed_lst as (select listagg(id, ',') as ids from removed),
    result as (
        select
            creator.id,
            creator.fanfix_username as fanfix_username,
            creator.firstname as firstname,
            creator.lastname as lastname,
            creator.fanfix_id as fanfix_id,
            creator.email as email,
            creator.phone as phone,
            creator.create_date,
            creator.close_date,
            owner.contact_owner as contact_owner,
            owner.contact_owner_email,
            -- case
            --     when creator.creator_success_cohort = '<nil>'
            --     then ''
            --     else creator.creator_success_cohort
            -- end as creator_success_cohort,
            -- , CASE
            -- WHEN creator.n3rd_party_management = '<nil>' THEN ''
            -- ELSE creator.properties_n3rd_party_management
            -- END AS "3RD_PARTY_MANAGEMENT"
            -- , CASE
            -- WHEN creator.properties_n3rd_party_management_date = '<nil>' THEN ''
            -- ELSE creator.properties_n3rd_party_management_date
            -- END AS "3RD_PARTY_MANAGEMENT_DATE"
            creator.account_management_agency,
            creator.agency_managed_date,
            -- , creator.properties_agency_management AS agency_management
            creator.churn_reason,
            creator.churn_reason_competitive_platform,
            creator.amount as amount,
            creator.contract_term_length,
            creator.contract_term_length_referral,
            creator.referrer_name,
            creator.referral_start_date,
            creator.referral_end_date,
            creator.referral_fee,
            creator.minimum_guarantee,
            creator.minimum_guarantee_monthly,
            creator.minimum_guarantee_start_date,
            creator.minimum_guarantee_end_date,
            creator.minimum_guarantee_cancellation_date,
            creator.active_mg,
            creator.relaunch,
            creator.relaunch_date,
            creator.relaunch_manager,
            creator.last_cs_check_in,
            creator.stripe_link,
            creator.trackable_link_owner,
            creator.hubspot_link,
            creator.instagram_handle,
            creator.tiktok_handle,

            lead_source,
            -- , CASE
            -- WHEN creator.inactive_ = '<nil>' THEN ''
            -- ELSE creator.inactive_
            -- END AS inactive  
            cs_managed,
            vertical,
            deal_creator_id,
            prospected_by.contact_owner as prospected_by,
            case
                when
                    (creator.country is null or creator.country = '<nil>')
                    and (
                        owner.contact_owner like '%Ally Salama%'
                        or prospected_by.contact_owner like '%Mo Ali%'
                    )
                then 'MENA'

                when
                    (creator.country is null or creator.country = '<nil>')
                    and (
                        owner.contact_owner like '%Diego Antunes%'
                        or owner.contact_owner like '%Izabor Ribeiro%'
                    )
                then 'Brazil'

                when
                    creator.country = '<nil>'
                then null
                else creator.country
            end as country,
            creator.fanfix_posts,
            creator.message_blasts,
            creator.age_group_modash,
            creator.audience_gender_female_modash,
            creator.audience_gender_male_modash,
            creator.audience_location_one_modash,
            creator.audience_location_two_modash,
            creator.audience_location_three_modash,
            creator.audience_location_four_modash,
            creator.audience_location_percentage_one_modash,
            creator.audience_location_percentage_two_modash,
            creator.audience_location_percentage_three_modash,
            creator.audience_location_percentage_four_modash,
            creator.audience_type_influencers_modash,
            creator.audience_type_mass_followers_modash,
            creator.audience_type_real_modash,
            creator.audience_type_suspicious_modash,
            creator.average_likes_per_post_modash,
            creator.country_modash,
            creator.gender_modash,
            creator.instagram_followers_modash,
            creator.instagram_engagement_rate_modash,
            csr_comment,
            note_timestamp as most_recent_csr_note_timestamp
        from {{ ref("stg_hubspot_contacts") }} creator
        left join
            {{ ref("stg_hubspot_owners") }} owner
            on creator.hubspot_owner_id = owner.contact_owner_id
        left join
            {{ ref("stg_hubspot_owners") }} prospected_by
            on creator.prospected_by = prospected_by.contact_owner_id
        left join
            {{ ref("fact_hubspot_creator_csr_notes") }} csrn
            on csrn.fanfix_id = creator.fanfix_id
        where
            creator.fanfix_id is not null
            and creator.fanfix_id <> '<nil>'
            and creator.id not in (
                select trim(value) as ids
                from removed_lst, lateral split_to_table(ids, ',')
            )
    )
select *
from result
