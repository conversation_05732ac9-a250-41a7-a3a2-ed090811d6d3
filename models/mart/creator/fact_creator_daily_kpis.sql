WITH creator_first_activity AS (
    SELECT 
        creator_id,
        CASE 
            WHEN first_post_day IS NOT NULL OR first_earning_day IS NOT NULL THEN
                LEAST(
                    COALESCE(first_post_day::DATE, '2099-12-31'::DATE),
                    COALESCE(first_earning_day, '2099-12-31'::DATE)
                )
            ELSE accepted_date::DATE
        END as first_activity_day
    FROM {{ref("dim_creator_first_days")}}
    WHERE (first_post_day IS NOT NULL OR first_earning_day IS NOT NULL OR accepted_date IS NOT NULL)
),
date_series AS (
    SELECT 
        dateadd(day, seq4(), '2021-01-01'::DATE) as day
    FROM table(generator(rowcount => 2500))  -- Covers ~7 years
    WHERE dateadd(day, seq4(), '2021-01-01'::DATE) <= current_date()
),
all_creator_days AS (
    SELECT 
        cfa.creator_id,
        ds.day
    FROM creator_first_activity cfa
    CROSS JOIN date_series ds
    WHERE ds.day >= cfa.first_activity_day
),
subscribers as (
    select creator_fanfix_id as creator_id, date::date as date, sum(subscriber_count) as subscriber_count from {{ref("fact_daily_active_subscriber_by_creator")}}
    group by 1, 2
), 
new_subscribers as (
    select creator_id, created as date, count(distinct fan_id) as new_subscriber_count from {{ref("fact_subscription_cohort")}}
    group by 1, 2
),
daily_incoming_dm AS (
    SELECT 
        m.created_at::date AS day, 
        recipient_user_id AS creator_id, 
        COUNT(DISTINCT m.message_id) AS incoming_dm_count
    FROM {{ref("stg_fanfix_messaging_messages")}} m
    LEFT JOIN {{ref("stg_fanfix_user_users")}}  cp ON cp.user_id = m.recipient_user_id
    LEFT JOIN {{ref("stg_fanfix_greeting_messages")}} gm ON gm.creator_id = m.recipient_user_id AND gm.text = m.content
    WHERE cp.roles = '["creator"]' 
        AND recipient_user_id is NOT null 
        AND m.created_at >= '2023-11-17' 
        AND gm.id is null
        AND (message_blast_id = '<nil>' OR message_blast_id is null)
    GROUP BY 1, 2
)

SELECT 
    acd.creator_id,
    acd.day,
    
    coalesce(e.total_revenue, 0) as total_revenue,
    coalesce(e.net_total_revenue, 0) as net_total_revenue,
    coalesce(e.total_sales_tax, 0) as total_sales_tax,
    coalesce(e.total_platform_fee, 0) as total_platform_fee,
    coalesce(e.paying_user, 0) as paying_user,
    coalesce(e.total_payment_count, 0) as total_payment_count,
    coalesce(e.subscription_payment_count, 0) as subscription_payment_count,
    coalesce(e.tip_payment_count, 0) as tip_payment_count,
    
    coalesce(e.subscriptions_revenue, 0) as subscriptions_revenue,
    coalesce(e.tips_revenue, 0) as tips_revenue,
    coalesce(e.subscriptions_creation_revenue, 0) as subscriptions_creation_revenue,
    coalesce(e.subscriptions_update_revenue, 0) as subscriptions_update_revenue,
    coalesce(e.media_unlock_revenue, 0) as media_unlock_revenue,
    coalesce(e.message_tip_revenue, 0) as message_tip_revenue,
    coalesce(e.livestream_unlock_revenue, 0) as livestream_unlock_revenue,
    coalesce(e.livestream_tip_revenue, 0) as livestream_tip_revenue,
    coalesce(e.livestream_revenue, 0) as livestream_revenue,
    coalesce(e.post_unlock_revenue, 0) as post_unlock_revenue,
    coalesce(e.post_tip_revenue, 0) as post_tip_revenue,
    coalesce(e.publication_revenue, 0) as publication_revenue,
    coalesce(e.creator_tip_revenue, 0) as creator_tip_revenue,
    coalesce(e.invoice_revenue, 0) as invoice_revenue,
    
    coalesce(e.net_subscriptions_revenue, 0) as net_subscriptions_revenue,
    coalesce(e.net_tips_revenue, 0) as net_tips_revenue,
    coalesce(e.net_subscriptions_creation_revenue, 0) as net_subscriptions_creation_revenue,
    coalesce(e.net_subscriptions_update_revenue, 0) as net_subscriptions_update_revenue,
    coalesce(e.net_media_unlock_revenue, 0) as net_media_unlock_revenue,
    coalesce(e.net_message_tip_revenue, 0) as net_message_tip_revenue,
    coalesce(e.net_livestream_unlock_revenue, 0) as net_livestream_unlock_revenue,
    coalesce(e.net_livestream_tip_revenue, 0) as net_livestream_tip_revenue,
    coalesce(e.net_livestream_revenue, 0) as net_livestream_revenue,
    coalesce(e.net_post_unlock_revenue, 0) as net_post_unlock_revenue,
    coalesce(e.net_post_tip_revenue, 0) as net_post_tip_revenue,
    coalesce(e.net_publication_revenue, 0) as net_publication_revenue,
    coalesce(e.net_creator_tip_revenue, 0) as net_creator_tip_revenue,
    coalesce(e.net_invoice_revenue, 0) as net_invoice_revenue,
    
    coalesce(e.chargeback_amount, 0) as chargeback_amount,
    coalesce(e.chargeback_dispute, 0) as chargeback_dispute,
    coalesce(e.chargeback_refund, 0) as chargeback_refund,

    coalesce(s.subscriber_count, 0) as total_subscribers,
    coalesce(ns.new_subscriber_count, 0) as new_subscribers,
    
    coalesce(f.follows, 0) as total_followers,
    coalesce(f.net_follows, 0) as new_followers,
    
    coalesce(a.post_count, 0) as post_count,
    coalesce(a.blast_count, 0) as blast_count,
    coalesce(a.outgoing_dm_count, 0) as outgoing_dm_count,
    coalesce(idm.incoming_dm_count, 0) as incoming_dm_count

FROM all_creator_days acd
LEFT JOIN {{ref("fact_daily_creator_earnings_bi")}} e
    ON acd.creator_id = e.creator_id AND acd.day = e.day_created::DATE
LEFT JOIN subscribers s
    ON acd.creator_id = s.creator_id AND acd.day = s.date
LEFT JOIN new_subscribers ns
    ON acd.creator_id = ns.creator_id AND acd.day = ns.date
LEFT JOIN {{ref("fact_daily_creator_follows")}} f  
    ON acd.creator_id = f.creator_id AND acd.day = f.day::DATE
LEFT JOIN {{ref("fact_daily_creator_activity")}} a
    ON acd.creator_id = a.creator_id AND acd.day = a.created_day::DATE
LEFT JOIN daily_incoming_dm idm
    ON acd.creator_id = idm.creator_id AND acd.day = idm.day
ORDER BY acd.creator_id, acd.day