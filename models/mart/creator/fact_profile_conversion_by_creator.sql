select fanfix_id, creator_username, launch_date,
    sum(case when datediff('day', launch_date, daily) >= 0 and datediff('day', launch_date, daily) <= 30 then pageview_users end) as launch_period_pageview_users,
    sum(case when datediff('day', launch_date, daily) >= 0 and datediff('day', launch_date, daily) <= 30 then subscribe_button_clicked_users end) as launch_period_subscribe_intent_users,
    sum(case when datediff('day', launch_date, daily) >= 0 and datediff('day', launch_date, daily) <= 30 then payment_intent_users end) as launch_period_membership_selected_users,
    sum(case when datediff('day', launch_date, daily) >= 0 and datediff('day', launch_date, daily) <= 30 then payment_initiated_users end) as launch_period_membership_checkout_users,
    
    launch_period_subscribe_intent_users/nullif(launch_period_pageview_users,0) as launch_period_subscription_intent_rate,
    launch_period_membership_selected_users/nullif(launch_period_subscribe_intent_users,0) as launch_period_membership_selection_rate,
    launch_period_membership_checkout_users/nullif(launch_period_membership_selected_users,0) as launch_period_membership_checkout_rate,

    sum(pageview_users) as lifetime_pageview_users,
    sum(subscribe_button_clicked_users) as lifetime_subscribe_intent_users,
    sum(payment_intent_users) as lifetime_membership_selected_users,
    sum(payment_initiated_users) as lifetime_membership_checkout_users,

    lifetime_subscribe_intent_users/nullif(lifetime_pageview_users,0) as lifetime_subscribe_intent_rate,
    lifetime_membership_selected_users /nullif(lifetime_subscribe_intent_users,0) as lifetime_membership_selection_rate,
    lifetime_membership_checkout_users/nullif(lifetime_membership_selected_users,0) as lifetime_membership_checkout_rate

from {{ref("fact_daily_subscriber_conversion_by_creator")}}
group by 1,2,3