SELECT 
    date_trunc('day', created_time) AS daily,
    creator_fanfix_id,
    SUM(
        CASE 
            WHEN payment_status = 'succeeded' THEN gross_revenue 
            ELSE 0 
        END
    ) AS succeeded_gmv,
    SUM(gross_revenue) AS total_attempted_gmv,
    COUNT(
        DISTINCT 
        CASE 
            WHEN payment_status = 'succeeded' THEN charge_id 
            ELSE null 
        END
    ) AS succeeded_payments,
    COUNT(DISTINCT charge_id) AS total_payments
FROM {{ ref('fact_payment_status_name_resolved') }}
GROUP BY 1, 2 
ORDER BY 1 DESC, 3 DESC