SELECT
    p.promotion_id
    , p.code AS promotion_code
    , p.type AS promotion_type
    , p.creator_id AS fanfix_id
    , cp.fanfix_username
    , p.restricted_to AS target_audiences
    , p.subscription_plan_id
    , p.discount_percentage
    , p.max_uses_count 
    , p.uses_count
    , discount_duration_in_months
    , discount_duration_in_months * 30 AS discount_duration_in_days
    , valid_until
    , trial_duration_in_days
    , expires_at
    , p.created_at 
FROM {{ ref('stg_fanfix_subscription_promotions') }} p
LEFT JOIN {{ ref('dim_creator_profile') }} cp ON p.creator_id = cp.fanfix_id