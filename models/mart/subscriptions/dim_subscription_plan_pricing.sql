SELECT
    sp.subscription_price_id AS price_id, 
    sp.subscription_plan_id, 
    duration_in_months, 
    sp.created_at AS price_created_at, 
    sp.updated_at AS price_updated_at,
    price_in_cents/100 AS sub_price , 
    sp.is_active AS price_is_active,
    dsp.has_perks,
    free_message_quantity, 
    post_unlock_discount, 
    display_name AS perk_name
FROM {{ ref('stg_fanfix_subscription_subscription_prices') }} sp
LEFT JOIN {{ ref('fact_dim_subscription_plans') }} dsp ON sp.subscription_plan_id = dsp.subscription_plan_id