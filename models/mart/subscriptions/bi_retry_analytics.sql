WITH dim_creator_and_fan_invoice AS (
	SELECT
	    i.subscription_id,
	    i.invoice_id AS subscription_invoice_id,
	    s.creator_id AS creator_fanfix_id,
	    u1.username AS creator_username,
	    s.fan_id AS fan_fanfix_id,
	    u2.username AS fan_username
	FROM {{ ref('stg_fanfix_subscription_invoices') }} i
	LEFT JOIN {{ ref('stg_fanfix_subscription_subscriptions') }} s ON s.subscription_id = i.subscription_id
	LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} u1 ON u1.user_id = s.creator_id
    LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} u2 ON u2.user_id = s.fan_id 
)
SELECT
    i.invoice_id,
    i.created_at AS invoiced_at,
    i.is_initial,
    total_amount_in_cents/100 AS total_amount,
    di.subscription_id,
    di.creator_fanfix_id,
    di.creator_username,
    di.fan_fanfix_id,
    di.fan_username,
    i.status AS invoice_status,
    ip.invoice_payment_id,
    attempt_number,
    ip.created_at AS invoice_payments_time,
    ip.status AS invoice_payment_status,
    ip.failure_reason,
    ROW_NUMBER() OVER (PARTITION BY invoice_id ORDER BY attempt_number DESC) AS attempt_number_desc, MAX(attempt_number) OVER (PARTITION BY invoice_id ) AS total_attemp_count
FROM {{ ref('stg_fanfix_subscription_invoices') }} i
LEFT JOIN DIM_CREATOR_AND_FAN_INVOICE di ON di.subscription_invoice_id = i.invoice_id
LEFT JOIN {{ ref('stg_fanfix_subscription_invoice_payments') }} ip ON i.invoice_id = ip.invoice_id
WHERE invoice_payment_id IS NOT NULL