SELECT 
    dvd.date,
    si.creator_fanfix_id, 
    si.creator_username, 
    si.recurring_interval_count, 
    si.subscription_membership_name,
    COUNT (
        DISTINCT 
        CASE 
            WHEN si.subscription_created_date <= dvd.date AND (si.subscription_ended_at > dvd.date OR si.subscription_ended_at  IS NULL)
            THEN si.fan_fanfix_id 
            ELSE null 
        END) AS subscriber_count
FROM {{ ref('dim_subscription_info') }} si
INNER JOIN {{ ref('dim_valid_days') }} dvd
GROUP BY 1, 2, 3, 4, 5 
ORDER BY 1 DESC, 4 DESC