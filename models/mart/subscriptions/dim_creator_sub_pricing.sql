WITH creator_sub_prices AS (
    SELECT
        creator_id
        , price
    FROM (
        SELECT 
            plan.creator_id
            , price.created_at
            , price.price_in_cents/100  AS price
            -- , sp.tier
            , row_number() OVER (PARTITION BY plan.creator_id ORDER BY price.created_at DESC) AS price_recency
    FROM {{ ref('fact_dim_subscription_plans') }} plan
    LEFT JOIN {{ ref('stg_fanfix_subscription_subscription_prices') }} price ON plan.plan_id = price.subscription_plan_id
    WHERE 1 = 1
        AND price.is_active = True 
        AND price.duration_in_months = 1 
        AND plan.is_active = True
    ) a
    WHERE price_recency = 1
)
SELECT 
    c.user_id AS creator_id
    , u.username AS creator_name
    , minimum_message_tip/100 AS minimum_message_tip
    , sp.price AS one_month_price
FROM {{ ref('stg_fanfix_creator_profiles') }} c
LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} u ON u.user_id = c.user_id
LEFT JOIN CREATOR_SUB_PRICES sp ON c.user_id = sp.creator_id
WHERE accepted_at IS NOT NULL