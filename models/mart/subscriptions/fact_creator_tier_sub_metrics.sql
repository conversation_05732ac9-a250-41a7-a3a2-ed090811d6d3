WITH membership_rank AS (
    SELECT 
        creator_id
        , created_at
        , row_number() over (partition by creator_id ORDER BY created_at asc) AS creation_rank
    FROM {{ref("stg_fanfix_subscription_subscription_plans")}}
),
perks AS (
    SELECT 
        sp.creator_id
        , SUM(CASE WHEN is_active = True THEN 1 ELSE 0 END) AS number_of_active_membership
        , SUM(CASE WHEN is_active = True AND has_perks = True THEN 1 ELSE 0 END) AS number_of_active_membership_with_perks
        , SUM(CASE WHEN is_active = True AND free_message_quantity = -1 THEN 1 ELSE 0 END) AS number_of_active_membership_with_free_message_perks
    FROM {{ref("dim_subscription_plans")}} sp
    GROUP BY 1
)
SELECT 
    perks.creator_id
    , mr.created_at AS  tier_sub_started_at
    , number_of_active_membership
    , number_of_active_membership_with_perks
    , number_of_active_membership_with_free_message_perks
FROM PERKS
LEFT JOIN ( 
    SELECT * 
    FROM MEMBERSHIP_RANK 
    WHERE creation_rank = 2
) mr ON perks.creator_id = mr.creator_id
