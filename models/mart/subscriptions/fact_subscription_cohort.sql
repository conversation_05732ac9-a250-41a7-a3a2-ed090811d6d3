SELECT 
    sc.subscription_created_date AS created, 
    subscription_id, 
    price_id,
    creator_fanfix_id AS creator_id, 
    creator_username AS creator_name,
    customer_id, fan_fanfix_id AS fan_id, 
    up.username AS fan_username,
    recurring_interval_count AS plan_interval_count,
    subscription_period_start AS current_period_start, 
    subscription_period_end AS current_period_end,
    subscription_canceled_at AS canceled_at,
    -- CASE WHEN (subscription_created_date >= '2024-11-05' AND subscription_created_date <= '2024-12-04' AND subscription_canceled_at IS NULL) THEN subscription_ended_at
    --         ELSE subscription_canceled_at
    --     END AS canceled_at,
    subscription_status AS status,
    CASE 
        WHEN (subscription_ended_at IS NULL OR subscription_ended_at > current_Date()) THEN 'subscription_not_ended' 
        ELSE status 
    END AS status_ver2,
    subscription_ended_at,
    subtotal AS price_unit_amount,
    date_trunc('month', subscription_created_date) AS cohort_type,
    CASE 
        WHEN subscription_canceled_at IS NULL AND subscription_ended_at IS NULL THEN 'NOT churn' 
        ELSE 'churn' 
    END AS churn_type,
    DATEDIFF('day', sc.subscription_created_date, sc.subscription_canceled_at) AS creation_to_cancel
FROM {{ ref('fact_subscriptions_with_valid_charges')}} sc
LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} up ON up.user_id = sc.fan_fanfix_id
WHERE cycle_count_desc = 1