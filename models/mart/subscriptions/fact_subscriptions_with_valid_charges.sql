WITH all_subscriptions AS (
    (SELECT * FROM {{ ref('fact_subscriptions') }}) 
    UNION
    (SELECT * FROM {{ ref('fact_subscription_in_qa') }})
),
paid_subscriptions AS (
    SELECT 
        s.*
        , c.time AS charge_date
    -- FROM all_subscriptions s
    FROM {{ ref('fact_subscriptions') }} s
    LEFT JOIN {{ ref('fact_creator_charges') }} c on c.charge_id = s.charge_id
    WHERE 1 = 1
        AND s.charge_id IS NOT NULL 
        AND charge_date IS NOT NULL 
        -- and s.creator_fanfix_id is not null
),
paid_subscriptions_cycle_ranked AS (
    SELECT 
        subscription_id
        , price_id
        , charge_id
        , creator_fanfix_id
        , creator_username
        , customer_id
        , fan_fanfix_id
        , recurring_interval_count
        , subtotal
        , charged_total_usd
        , charge_date
        , subscription_created_date
        , subscription_period_start
        , subscription_period_end
        , subscription_ended_at
        , subscription_status
        , SUBSCRIPTION_CANCELED_AT
        , row_number() over (partition by subscription_id order by subscription_period_start desc, subtotal desc, charge_id desc) AS cycle_count_desc
        , row_number() over (partition by subscription_id order by subscription_period_start asc, subtotal asc, charge_id asc) AS cycle_count_asc
    FROM paid_subscriptions
),
subscription_dates AS (
    SELECT 
        subscription_id
        , price_id
        , recurring_interval_count
        , subscription_status
        , SUBSCRIPTION_CANCELED_AT
        , subscription_ended_at
        , min(subscription_period_start) AS subscription_created_date
        --  case 
        --      when subscription_status != 'active' and subscription_status != 'pending'  then  dateadd('month', recurring_interval_count, max(subscription_period_start)) --  
        --      else null 
        -- end AS subscription_ended_at
    FROM paid_subscriptions_cycle_ranked  
    GROUP BY 1, 2, 3, 4, 5, 6
),
sub_inv_attempts AS (
    SELECT DISTINCT     
        -- only accurate for 2025 onward subscriptions
        i.subscription_id
        , i.invoice_id
        , i.created_at::DATE AS invoice_date
        , ip.created_at AS last_charge_attempt_date
        , ip.status
        , i.is_initial
        , ip.attempt_number
        , row_number() OVER (PARTITION BY i.invoice_id order by ip.attempt_number DESC) AS rnk
    FROM {{ ref('stg_fanfix_subscription_invoice_payments') }} ip
    LEFT JOIN {{ ref('stg_fanfix_subscription_invoices') }} i ON ip.invoice_id = i.invoice_id 
    WHERE 1 = 1 
    -- and ip.attempt_number = 4
    -- and ip.status = 'Failed'
    -- and i.is_initial = FALSE
),
sub_ended_due_to_failed_charges AS (
    SELECT DISTINCT     
        -- only accurate for 2025 onward subscriptions
        subscription_id
    FROM sub_inv_attempts
    WHERE 1 = 1 
        AND status = 'Failed'
        AND is_initial = FALSE
        AND rnk = 1
)
SELECT 
    sd.subscription_id
    , sd.price_id
    , sd.subscription_created_date
    , sd.subscription_ended_at
    , sd.subscription_canceled_at
    , sd.recurring_interval_count
    , sd.subscription_status
    , CASE
        WHEN fc.subscription_id IS NOT NULL THEN 'Ended due to failed charges'
        WHEN (sd.subscription_status = 'canceled' OR sd.subscription_status = 'expired') 
            AND sd.subscription_ended_at >= '2025-01-01' THEN 'Ended by fan'
        ELSE NULL
    END AS subscription_ended_reason
    , charge_id
    , creator_fanfix_id
    , creator_username
    , customer_id
    , fan_fanfix_id
    , subtotal
    , charged_total_usd
    , charge_date
    , subscription_period_start
    , subscription_period_end
    , cycle_count_asc
    , cycle_count_desc
FROM subscription_dates sd
LEFT JOIN paid_subscriptions_cycle_ranked lpc ON lpc.subscription_id = sd.subscription_id
LEFT JOIN sub_ended_due_to_failed_charges fc ON fc.subscription_id = sd.subscription_id
