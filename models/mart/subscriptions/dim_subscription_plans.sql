WITH perk_detail_by_plan AS (
    SELECT  
        subscription_plan_id,
        SUM(CASE WHEN perk_id = '3c115e39-271f-40e2-98cc-5f1562f0fe5a' THEN quantity ELSE 0 END) AS free_message_quantity,
        SUM(CASE WHEN perk_id = 'd2632bd4-35b7-4e22-b5dc-e7941330e934' THEN discount/100 ELSE 0 END) AS post_unlock_discount
    FROM {{ ref('stg_fanfix_subscription_subscription_plan_perks')}} spp
    GROUP BY 1
),
one_month_pricing_by_plan AS (
   SELECT 
        subscription_plan_id, 
        subscription_price_id, 
        price_in_cents/100 AS one_month_price
   FROM {{ ref('stg_fanfix_subscription_subscription_prices')}}
   WHERE 1 = 1
    AND duration_in_months = 1 
    AND is_active = True
)
SELECT 
    sp.subscription_plan_id, 
    sp.creator_id, 
    sp.display_name, 
    sp.description, 
    sp.created_at, 
    sp.is_active, 
    one_month_price,
    pd.free_message_quantity, 
    pd.post_unlock_discount,
    CASE 
        WHEN (pd.free_message_quantity IS NOT NULL AND free_message_quantity != 0) OR
        (pd.post_unlock_discount IS NOT NULL AND post_unlock_discount != 0) THEN True 
        ELSE False 
    END AS has_perks,
    row_number() OVER (PARTITION BY sp.creator_id, sp.is_active ORDER BY one_month_price ASC) AS tier_rank_asc,
    row_number() OVER (PARTITION BY  sp.creator_id, sp.is_active ORDER BY one_month_price DESC) AS tier_rank_desc,
FROM {{ ref('stg_fanfix_subscription_subscription_plans')}} sp
LEFT JOIN PERK_DETAIL_BY_PLAN pd ON sp.subscription_plan_id = pd.subscription_plan_id
LEFT JOIN ONE_MONTH_PRICING_BY_PLAN pp ON pp.subscription_plan_id = sp.subscription_plan_id