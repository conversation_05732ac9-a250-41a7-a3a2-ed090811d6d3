SELECT 
    s.subscription_id
    , s.creator_id
    , cp.fanfix_username AS creator_username
    , s.fan_id
    , fp.username AS fan_username
    , price.price AS subscription_price
    , ROUND(price.price * (100 - discount_percentage) / 100, 2) as subscription_discounted_price
    , p.promotion_id AS promotion_id
    , p.code AS promotion_code
    , p.discount_percentage
    , s.created_at::date AS promotion_used_date
    , s.promotion_ends_at::date AS promotion_end_date
    , s.promotion_end_notification_sent_at
FROM {{ ref('stg_fanfix_subscription_subscriptions') }} s
LEFT JOIN {{ ref('stg_fanfix_subscription_invoices') }} i ON i.subscription_id = s.subscription_id
INNER JOIN {{ ref('stg_fanfix_subscription_promotions') }} p ON p.promotion_id = s.applied_promotion_id
LEFT JOIN {{ ref('stg_fanfix_subscription_subscription_prices') }} price ON s.subscription_price_id = price.subscription_price_id
LEFT JOIN {{ ref('dim_creator_profile') }} cp ON s.creator_id = cp.fanfix_id
LEFT JOIN {{ ref('dim_fan_profile') }} fp ON s.fan_id = fp.user_id
WHERE i.status = 'succeeded' AND i.is_initial = true