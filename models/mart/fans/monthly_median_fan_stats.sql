with month_spine as (
SELECT 
dateadd(month, seq4(), '2021-01-01'::DATE) as month
FROM table(generator(rowcount => 2500))  -- Covers ~7 years
WHERE dateadd(month, seq4(), '2021-01-01'::DATE) <= current_date()
),

monthly_comments as (
select fan_id, date_trunc('month', created_at)::date as month_start, 
count(distinct comment_id) as monthly_comments
from {{ ref ('fact_post_fan_comments') }}
group by 1, 2
),

monthly_messages as (
select fan_user_id, date_trunc('month', hour)::date as month_start, 
sum(fan_msg) as monthly_messages
from {{ ref ('fact_hourly_messaging_heatmap') }}
group by 1, 2
having sum(fan_msg) > 0
),

monthly_message_tips as (
select fan_fanfix_id, date_trunc('month', time)::date as month_start, 
count(distinct charge_id) as monthly_message_tips
from {{ ref ('fact_creator_charges') }}
where revenue_category = 'message'
group by 1, 2
),

monthly_media_unlocks as (
select fan_fanfix_id, date_trunc('month', time)::date as month_start, 
count(distinct charge_id) as monthly_media_unlocks
from {{ ref ('fact_creator_charges') }}
where revenue_category = 'media unlock'
group by 1, 2
),

monthly_post_unlocks as (
select fan_fanfix_id, date_trunc('month', time)::date as month_start,
count(distinct charge_id) as monthly_post_unlocks
from {{ ref ('fact_creator_charges') }}
where revenue_category = 'post unlock'
group by 1, 2
),

monthly_livestream_unlocks as (
select fan_fanfix_id, date_trunc('month', time)::date as month_start,
count(distinct charge_id) as monthly_livestream_unlocks
from {{ ref ('fact_creator_charges') }}
where revenue_category = 'livestream unlock'
group by 1, 2
),

monthly_livestream_tips as (
select fan_fanfix_id, date_trunc('month', time)::date as month_start,
count(distinct charge_id) as monthly_livestream_tips
from {{ ref ('fact_creator_charges') }}
where revenue_category = 'livestream tip'
group by 1,2
),

message_median as (select month_start as month, 
percentile_cont(0.5) within group (order by monthly_messages) as median_message_count
from monthly_messages
group by 1
),

message_tip_median as (select month_start as month, 
percentile_cont(0.5) within group (order by monthly_message_tips) as median_message_tips
from monthly_message_tips
group by 1
),

comments_median as (select month_start as month, 
percentile_cont(0.5) within group (order by monthly_comments) as median_comment_count
from monthly_comments
group by 1
),

media_unlock_median as (select month_start as month, 
percentile_cont(0.5) within group (order by monthly_media_unlocks) as median_media_unlocks
from monthly_media_unlocks
group by 1
),

post_unlock_median as (select month_start as month,
percentile_cont(0.5) within group (order by monthly_post_unlocks) as median_post_unlocks
from monthly_post_unlocks
group by 1
),

livestream_unlock_median as (select month_start as month,
percentile_cont(0.5) within group (order by monthly_livestream_unlocks) as median_livestream_unlocks
from monthly_livestream_unlocks
group by 1
),

livestream_tip_median as (select month_start as month,
percentile_cont(0.5) within group (order by monthly_livestream_tips) as median_livestream_tips
from monthly_livestream_tips
group by 1
)

select ms.month, median_message_count, median_message_tips, median_comment_count, median_media_unlocks, median_post_unlocks,
median_livestream_unlocks, median_livestream_tips
from month_spine ms left join message_median m on ms.month = m.month
left join message_tip_median mt on ms.month = mt.month
left join comments_median c on ms.month = c.month
left join media_unlock_median media on ms.month = media.month
left join post_unlock_median p on ms.month = p.month
left join livestream_unlock_median lu on ms.month = lu.month
left join livestream_tip_median lt on ms.month = lt.month
order by 1 desc
