WITH dm_data AS (
    SELECT recipient_user_id,
            sender_user_id,
            COUNT(DISTINCT message_id) AS message_count
    FROM {{ref("stg_fanfix_messaging_messages")}} m
    WHERE (message_blast_id = '<nil>' OR message_blast_id is null)
    GROUP BY 1,2 ORDER BY 1 desc
),
subscription_data AS (
    SELECT creator_id, fan_id,
            SUM(CASE WHEN current_period_end > current_date() THEN datediff('month', created, current_date())+1
                    ELSE datediff('month', created, current_period_end)
                END) AS total_subscription_month
    FROM {{ref("fact_subscription_cohort")}}
    GROUP BY 1,2
),
current_active_subscription_all AS (
    SELECT creator_id, fan_id, LISTAGG(status, ' , ') WITHIN GROUP (ORDER BY fan_id) AS all_subscription_status
    FROM {{ref("fact_subscription_cohort")}}
    GROUP BY 1,2
),
current_active_subscription AS (
    SELECT creator_id, fan_id, all_subscription_status,
            CASE WHEN all_subscription_status like '%active%' THEN 'active'
                WHEN (all_subscription_status NOT like '%active%' AND  all_subscription_status like '%past_due%') THEN 'past_due'
                ELSE 'canceled'
            END AS status
    FROM CURRENT_ACTIVE_SUBSCRIPTION_ALL
    GROUP BY 1,2,3,4
),
charges_data AS (
    SELECT creator_fanfix_id, creator_username,
            fan_fanfix_id, fan_username,
            SUM(gross_revenue) AS total_spending_gmv,
            SUM(net_revenue) AS total_spending_nmv,
            COUNT(*) AS number_of_payments,
            MIN(time) AS first_payment_date,
            MAX(time) AS latest_payment_date,
            datediff('month', MIN(time), current_date()) AS months_on_fanfix,
            COUNT(DISTINCT CASE WHEN type like '%subscription%' THEN creator_fanfix_id ELSE null END) AS creators_subbed,
            SUM(CASE WHEN type like '%message%' THEN 1 ELSE 0 END) AS total_message_sent,
            COUNT(DISTINCT CASE WHEN type like '%message%' THEN creator_fanfix_id ELSE null END) AS creators_messaged,
            SUM(CASE WHEN dispute_id is NOT null THEN gross_revenue ELSE 0 END)/SUM(gross_revenue) AS dispute_rate,
            COUNT(DISTINCT CASE WHEN type like '%post unlock%' THEN charge_id ELSE null END) AS number_of_post_unlocks,
            COUNT(DISTINCT CASE WHEN type like '%media unlock%' THEN charge_id ELSE null END) AS number_of_media_unlocks
    FROM {{ref("fact_creator_charges")}}
    GROUP BY 1,2,3,4
)
SELECT cd.* ,
    dd.message_count AS f2c_message_count,
    dd2.message_count AS c2f_message_count,
    total_subscription_month,
    ff.first_name AS fan_first_name,
    ff.card_country AS fan_country,
    ff.card_state,
    CASE WHEN cas.status is null THEN 'NOT subscribed' ELSE cas.status END subscription_status
FROM CHARGES_DATA cd
LEFT JOIN DM_DATA dd ON dd.recipient_user_id = cd.creator_fanfix_id AND dd.sender_user_id = cd.fan_fanfix_id
LEFT JOIN DM_DATA dd2 ON dd2.recipient_user_id = cd.fan_fanfix_id AND dd2.sender_user_id = cd.creator_fanfix_id
LEFT JOIN SUBSCRIPTION_DATA  sd ON sd.creator_id = cd.creator_fanfix_id AND sd.fan_id = cd.fan_fanfix_id
LEFT JOIN {{ref("dim_fan_profile")}} ff ON ff.user_id = cd.fan_fanfix_id
LEFT JOIN CURRENT_ACTIVE_SUBSCRIPTION cas ON cas.creator_id = cd.creator_fanfix_id AND cas.fan_id = cd.fan_fanfix_id