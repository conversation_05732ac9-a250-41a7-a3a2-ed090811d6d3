with week_spine as (
SELECT 
dateadd(week, seq4(), '2021-01-04'::DATE) as week
FROM table(generator(rowcount => 2500))  -- Covers ~7 years
WHERE dateadd(week, seq4(), '2021-01-04'::DATE) <= current_date()
),

weekly_comments as (
select fan_id, date_trunc('week', created_at)::date as week_start, 
count(distinct comment_id) as weekly_comments
from {{ ref ('fact_post_fan_comments') }}
group by 1, 2
),

weekly_messages as (
select fan_user_id, date_trunc('week', hour)::date as week_start, 
sum(fan_msg) as weekly_messages
from {{ ref ('fact_hourly_messaging_heatmap') }}
group by 1, 2
having sum(fan_msg) > 0
),

weekly_message_tips as (
select fan_fanfix_id, date_trunc('week', time)::date as week_start, 
count(distinct charge_id) as weekly_message_tips
from {{ ref ('fact_creator_charges') }}
where revenue_category = 'message'
group by 1, 2
),

weekly_media_unlocks as (
select fan_fanfix_id, date_trunc('week', time)::date as week_start, 
count(distinct charge_id) as weekly_media_unlocks
from {{ ref ('fact_creator_charges') }}
where revenue_category = 'media unlock'
group by 1, 2
),

weekly_post_unlocks as (
select fan_fanfix_id, date_trunc('week', time)::date as week_start,
count(distinct charge_id) as weekly_post_unlocks
from {{ ref ('fact_creator_charges') }}
where revenue_category = 'post unlock'
group by 1, 2
),

weekly_livestream_unlocks as (
select fan_fanfix_id, date_trunc('week', time)::date as week_start,
count(distinct charge_id) as weekly_livestream_unlocks
from {{ ref ('fact_creator_charges') }}
where revenue_category = 'livestream unlock'
group by 1, 2
),

weekly_livestream_tips as (
select fan_fanfix_id, date_trunc('week', time)::date as week_start,
count(distinct charge_id) as weekly_livestream_tips
from {{ ref ('fact_creator_charges') }}
where revenue_category = 'livestream tip'
group by 1,2
),

message_median as (select week_start as week, 
percentile_cont(0.5) within group (order by weekly_messages) as median_message_count
from weekly_messages
group by 1
),

message_tip_median as (select week_start as week, 
percentile_cont(0.5) within group (order by weekly_message_tips) as median_message_tips
from weekly_message_tips
group by 1
),

comments_median as (select week_start as week, 
percentile_cont(0.5) within group (order by weekly_comments) as median_comment_count
from weekly_comments
group by 1
),

media_unlock_median as (select week_start as week, 
percentile_cont(0.5) within group (order by weekly_media_unlocks) as median_media_unlocks
from weekly_media_unlocks
group by 1
),

post_unlock_median as (select week_start as week,
percentile_cont(0.5) within group (order by weekly_post_unlocks) as median_post_unlocks
from weekly_post_unlocks
group by 1
),

livestream_unlock_median as (select week_start as week,
percentile_cont(0.5) within group (order by weekly_livestream_unlocks) as median_livestream_unlocks
from weekly_livestream_unlocks
group by 1
),

livestream_tip_median as (select week_start as week,
percentile_cont(0.5) within group (order by weekly_livestream_tips) as median_livestream_tips
from weekly_livestream_tips
group by 1
)

select ws.week, median_message_count, median_message_tips, median_comment_count, median_media_unlocks, median_post_unlocks,
median_livestream_unlocks, median_livestream_tips
from week_spine ws left join message_median m on ws.week = m.week
left join message_tip_median mt on ws.week = mt.week
left join comments_median c on ws.week = c.week
left join media_unlock_median media on ws.week = media.week
left join post_unlock_median p on ws.week = p.week
left join livestream_unlock_median lu on ws.week = lu.week
left join livestream_tip_median lt on ws.week = lt.week
order by 1 desc
