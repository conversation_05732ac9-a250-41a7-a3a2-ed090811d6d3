SELECT DISTINCT 
    person_id as ph_person_id
    , properties:"$initial_referrer"::string as referring_domain
    , properties:email as email_address
    , properties:"$initial_current_url"::string as initial_landing_url
    , CASE 
        WHEN referring_domain = '$direct' THEN 'direct landing or incognito'
        WHEN referring_domain LIKE '%instagram%' THEN 'instagram'
        WHEN referring_domain LIKE '%superlink%' THEN 'superlink'
        WHEN referring_domain LIKE '%fanfix%' THEN 'fanfix'
        WHEN referring_domain LIKE '%google%' THEN 'google'
        WHEN referring_domain LIKE '%tiktok%' THEN 'tiktok'
        WHEN referring_domain LIKE '%youtube%' THEN 'youtube'
        WHEN referring_domain LIKE '%facebook%' THEN 'facebook'
        WHEN referring_domain LIKE '%reddit%' THEN 'reddit'
        WHEN referring_domain LIKE '%yahoo%' THEN 'yahoo'
        WHEN referring_domain LIKE '%patreon%' THEN 'patreon'
        WHEN referring_domain LIKE '%telegram%' THEN 'telegram'
        WHEN referring_domain LIKE '%twitch%' THEN 'twitch'
        WHEN referring_domain LIKE '%twitter%' OR referring_domain LIKE '%t.co%' THEN 'twitter'
        WHEN referring_domain LIKE '%pinterest%' THEN 'pinterest'
        WHEN referring_domain LIKE '$%yandex%$' THEN 'yandex'
        WHEN referring_domain LIKE '%x.com%' THEN 'twitter'
        WHEN referring_domain LIKE '%linktr%' 
                OR referring_domain LIKE '%liinks%' 
                OR referring_domain LIKE '%lnk.bio%'
                OR referring_domain LIKE '%link.me%'
            THEN 'Linktree or equivalent'
        ELSE referring_domain 
    END AS standardized_referring_domain
    , properties
FROM {{ ref('stg_posthog_persons') }}
WHERE email_address IS NOT NULL
