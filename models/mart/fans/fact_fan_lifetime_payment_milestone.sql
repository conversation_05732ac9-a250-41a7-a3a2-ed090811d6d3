WITH CUMULATIVE_SPENDING AS (
    SELECT
        FAN_FANFIX_ID,
        FAN_USERNAME,
        DATE_TRUNC(DAY, TIME) AS DATE,
        SUM(GROSS_REVENUE) OVER (PARTITION BY FAN_FANFIX_ID, <PERSON><PERSON>_<PERSON>ERNAME ORDER BY DATE ASC) AS C<PERSON><PERSON>LATIVE_PAYMENT
    FROM {{ ref("fact_creator_charges") }}
)
SELECT
    FAN_FANFIX_ID,
    FAN_USERNAME,
    MAX(CUMULATIVE_PAYMENT) AS lifetime_payment,
    CASE
        WHEN MAX(CUMULATIVE_PAYMENT) >= 25000 THEN TRUE
        ELSE FALSE
    END AS LIFETIME_PAYMENT_MILESTONE_ACHIEVED,
    MIN(
        CASE
            WHEN CUMULATIVE_PAYMENT >= 25000 THEN DATE
            ELSE NULL
        END
    )AS LIFETIME_PAYMENT_MILESTONE_ACHIEVED_DATE,
FROM CUMULATIVE_SPENDING
GROUP BY 1, 2
ORDER BY 3 DESC