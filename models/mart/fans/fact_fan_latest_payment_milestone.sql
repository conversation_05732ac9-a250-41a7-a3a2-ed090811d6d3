SELECT
    USER_ID AS FAN_FANFIX_ID,
    USERNAME AS FAN_FANFIX_USERNAME,
    LATEST_PAYMENT_DATE,
    CASE
        WHEN DATEDIFF(DAY, LATEST_PAYMENT_DATE, CURRENT_DATE) <= 6 THEN TRUE
        ELSE FALSE
    END AS LATEST_PAYMENT_MILESTONE_ACHIEVED,
    CASE
        WHEN DATEDIFF(DAY, LATEST_PAYMENT_DATE, CURRENT_DATE) <= 6 THEN LATEST_PAYMENT_DATE
        ELSE NULL
    END AS LATEST_PAYMENT_MILESTONE_ACHIEVED_DATE
FROM {{ ref("dim_fan_profile") }}