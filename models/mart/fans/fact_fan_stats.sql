--TODO: update update sources
WITH fans AS (
    SELECT
        u.user_id AS fan_fanfix_id
        , p.username AS fan_fanfix_username
    FROM {{ ref('stg_fanfix_user_users') }} u
    LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} p ON u.user_id = p.user_id
    WHERE u.roles like '%fan%'
)
, charges AS (
    SELECT
        fans.fan_fanfix_id
        , fans.fan_fanfix_username
        , SUM(c.gross_revenue) AS total_spending_gmv
        , SUM(c.net_revenue) AS total_spending_nmv
        , COUNT(DISTINCT charge_id) AS number_of_payments
        , MIN(time) AS first_payment_date
        , MAX(time) AS latest_payment_date
        , datediff('month', MIN(time), current_date()) AS months_on_fanfix
        , COUNT(DISTINCT 
            CASE 
                WHEN type like '%subscription%' THEN creator_fanfix_id 
                ELSE null 
            END
        ) AS creators_subbed
        , SUM(
            CASE 
                WHEN dispute_id is NOT null THEN gross_revenue 
                ELSE 0
            END
        )/ SUM(gross_revenue) AS dispute_rate
    FROM FANS
    LEFT JOIN {{ ref('fact_creator_charges') }} c ON fans.fan_fanfix_id = c.fan_fanfix_id
    GROUP BY 1, 2
), messages AS (
    SELECT
        fans.fan_fanfix_id
        , fans.fan_fanfix_username
        , COUNT(DISTINCT m.ID) AS total_message_sent
        , COUNT(DISTINCT m.RECIPIENT_USER_ID) AS creators_messaged
    FROM FANS
    LEFT JOIN FANFIX_RAW.fanfix_messaging.MESSAGES m
    ON fans.fan_fanfix_id = m.SENDER_USER_ID
    GROUP BY 1, 2
), follows AS (
    SELECT
        fans.fan_fanfix_id
        , fans.fan_fanfix_username
        -- number of unique creators each fan follows
        -- calculated FROM THE creator_follows table WHERE each row represents a follow relationship
        -- (WHEN someone unfollows a creator, their row is removed FROM THIS table)
        , COUNT(DISTINCT fo.followed_creator_id) AS creators_followed
    FROM FANS
    LEFT JOIN FANFIX_RAW.FANFIX.CREATOR_FOLLOWS fo ON fans.fan_fanfix_id = fo.following_user_id
    GROUP BY 1, 2
), suspicious_fans AS (
    SELECT
        fans.fan_fanfix_id
        , fans.fan_fanfix_username
        , MAX(
            CASE
                WHEN f.fan_fanfix_id IS NOT NULL THEN True
                ELSE False
            END
        )AS suspicious_fans
    FROM FANS
    LEFT JOIN {{ ref('bi_suspicious_fans') }} f
    ON fans.fan_fanfix_id = f.FAN_FANFIX_ID
    GROUP BY 1, 2
)
SELECT
    fans.fan_fanfix_id
    , fans.fan_fanfix_username
    , c.total_spending_gmv
    , c.total_spending_nmv
    , c.number_of_payments
    , c.first_payment_date
    , c.latest_payment_date
    , c.months_on_fanfix
    , c.creators_subbed
    , c.dispute_rate
    , m.total_message_sent
    , m.creators_messaged
    , fo.creators_followed
    , f.suspicious_fans
FROM FANS
LEFT JOIN CHARGES c ON fans.fan_fanfix_id = c.fan_fanfix_id
LEFT JOIN MESSAGES mON fans.fan_fanfix_id = m.fan_fanfix_id
LEFT JOIN FOLLOWS fo ON fans.fan_fanfix_id = fo.fan_fanfix_id
LEFT JOIN SUSPICIOUS_FANS f ON fans.fan_fanfix_id = f.fan_fanfix_id