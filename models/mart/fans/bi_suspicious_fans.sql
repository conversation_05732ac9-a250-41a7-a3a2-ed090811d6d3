WITH high_dispute_fans AS (
    SELECT 
        day
        , fan_fanfix_id
        , fan_username
        , 'high_daily_dispute_amount' AS flag_category
        , daily_disputed_amount AS dollars_impacted
    FROM {{ ref('fact_fan_daily_disputes') }}
    WHERE daily_disputed_amount >= 500 
        OR daily_dispute_count >= 5
    ORDER BY 1 desc
),
high_card_swap_fans AS (
    SELECT 
        date_trunc('day', hour) AS day
        , fan_fanfix_id
        , fan_username
        , 'high_hourly_card_swaps' AS flag_category
        , SUM(hourly_amount) AS dollars_impacted
    FROM {{ ref('fact_fan_hourly_card_swaps') }}
    WHERE hourly_cards >= 4
    GROUP BY 1,2,3,4
    ORDER BY 1 desc
),
high_repeating_payment_fans AS (
    SELECT 
        date_trunc('day', hour) AS day
        , fan_fanfix_id
        , fan_username
        , 'high_repeating_payments' AS flag_category
        , SUM(total_potential_fraud_amount) AS dollars_impacted
    FROM {{ ref('fact_fan_with_high_repeating_charges_hourly') }}
    GROUP BY 1,2,3,4 
    ORDER BY 1 desc
)
SELECT * 
FROM HIGH_DISPUTE_FANS 
UNION 
SELECT * 
FROM HIGH_CARD_SWAP_FANS 
UNION 
SELECT * 
FROM HIGH_REPEATING_PAYMENT_FANS
ORDER BY day DESC