WITH CHARGES AS (
    SELECT
        FAN_FANFIX_ID,
        FAN_USERNAME,
        DATE_TRUNC(DAY, TIME) AS DATE,
        COUNT(DISTINCT CHARGE_ID) AS CHARGES_COUNT
    FROM {{ ref("fact_creator_charges") }}
    GROUP BY 1, 2, 3
), CUM<PERSON>LATIVE_TRANSACTIONS AS (
    SELECT
        FAN_FANFIX_ID,
        FAN_USERNAME,
        DATE,
        SUM(charges_count) OVER (PARTITION BY FAN_FANFIX_ID, FAN_USERNAME ORDER BY date ASC) AS cumulative_transactions
    FROM CHARGES
)
SELECT
    FAN_FANFIX_ID,
    FAN_USERNAME,
    MAX(cumulative_transactions) AS lifetime_transactions,
    CASE
        WHEN MAX(cumulative_transactions) >= 100 THEN TRUE
        ELSE FALSE
    END AS LIFETIME_TRANSACTIONS_MILESTONE_ACHIEVED,
    MIN(
        CASE
            WHEN cumulative_transactions >= 100 THEN DATE
            ELSE NULL
        END
    )AS LIFETIME_TRANSACTIONS_MILESTONE_ACHIEVED_DATE,
FROM CUMULATIVE_TRANSACTIONS
GROUP BY 1, 2
ORDER BY 3 DESC