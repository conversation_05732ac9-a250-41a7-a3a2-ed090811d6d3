SELECT 
    fan_fanfix_id
    , fan_username
    , creator_fanfix_id
    , creator_username
    , SUM(
        CASE
            WHEN (type like '%subscription%' or type like '%post%') THEN 1
            WHEN type like '%media unlock%' THEN 1.1
            WHEN type like '%livestream%' THEN 1.1
            WHEN type like '%post%' THEN 1.1
            WHEN type like '%message%' THEN 1.2 
            ELSE 0
    END) AS score
FROM {{ref("fact_creator_charges")}}
GROUP BY 
    fan_fanfix_id
    , fan_username
    , creator_fanfix_id
    , creator_username