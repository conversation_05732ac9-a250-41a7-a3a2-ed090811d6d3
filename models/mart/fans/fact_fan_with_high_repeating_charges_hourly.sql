SELECT 
    date_trunc('hour', time) AS hour 
    --, creator_fanfix_id
    -- , creator_username
    , fan_fanfix_id
    , fan_username
    , gross_revenue AS payment_amount_per_charge 
    , type 
    -- , card_id
    , COUNT(*) AS potential_fraud_payment_count
    , SUM(GROSS_REVENUE) AS total_potential_fraud_amount
FROM {{ ref('fact_creator_charges') }}
WHERE payment_amount_per_charge>= 20 AND type != 'message' 
    -- AND type != 'media unlock'
GROUP BY 1,2,3,4,5 --,6,7
HAVING potential_fraud_payment_count >= 5
ORDER BY hour desc