WITH date_spine AS (
    SELECT DISTINCT
        DATE_TRUNC('month', created) AS month
    FROM {{ref("fact_subscription_cohort")}}
    UNION
    SELECT DATE_TRUNC('month', CURRENT_DATE()) AS month
),
fan_creator_pairs AS (
    SELECT DISTINCT
        fan_id,
        creator_id,
        MIN(DATE_TRUNC('month', created)) AS first_interaction_month
    FROM {{ref("fact_subscription_cohort")}}
    GROUP BY 1, 2
    
    UNION
    
    SELECT DISTINCT
        following_user_id AS fan_id,
        followed_creator_id AS creator_id,
        MIN(DATE_TRUNC('month', created_at)) AS first_interaction_month
    FROM {{ref("stg_fanfix_creator_follows")}}
    GROUP BY 1, 2

    UNION
    
    SELECT DISTINCT
        fan_fanfix_id AS fan_id,
        creator_fanfix_id AS creator_id,
        MIN(DATE_TRUNC('month', time)) AS first_interaction_month
    FROM {{ref("fact_creator_charges")}}
    GROUP BY 1, 2
    
),
fan_creator_pairs_agg AS (
    SELECT
        fan_id,
        creator_id,
        MIN(first_interaction_month) AS first_interaction_month
    FROM fan_creator_pairs
    GROUP BY 1, 2
),
pairs_all AS (
    SELECT 
        ds.month,
        fcpa.fan_id,
        fcpa.creator_id
    FROM fan_creator_pairs_agg fcpa
    CROSS JOIN date_spine ds
    WHERE ds.month >= fcpa.first_interaction_month
),
active_subscriptions AS (
    SELECT 
        ds.month,
        sc.fan_id,
        sc.creator_id
    FROM {{ref("fact_subscription_cohort")}} sc
    CROSS JOIN date_spine ds
    WHERE ds.month >= DATE_TRUNC('month', sc.created)
        AND (ds.month <= DATE_TRUNC('month', sc.subscription_ended_at) or sc.subscription_ended_at IS NULL)
    GROUP BY 1, 2, 3
),
active_follows AS (
    SELECT 
        ds.month,
        cf.following_user_id AS fan_id,
        cf.followed_creator_id AS creator_id
    FROM FANFIX_RAW.FANFIX.CREATOR_FOLLOWS cf
    CROSS JOIN date_spine ds
    WHERE ds.month >= DATE_TRUNC('month', cf.created_at)
        AND (ds.month <= DATE_TRUNC('month', cf.deleted_at) or cf.deleted_at IS NULL)
    GROUP BY 1, 2, 3
),
monthly_revenue AS (
    SELECT 
        DATE_TRUNC('month', time) AS month,
        fan_fanfix_id AS fan_id,
        creator_fanfix_id AS creator_id,
        SUM(gross_revenue) AS revenue
    FROM {{ref("fact_creator_charges")}}
    GROUP BY 1, 2, 3
),
base AS (
    SELECT 
        pa.month,
        pa.fan_id,
        pa.creator_id,
        CASE WHEN asub.fan_id IS NOT NULL THEN 1 ELSE 0 END AS subscription_flag,
        CASE WHEN af.fan_id IS NOT NULL THEN 1 ELSE 0 END AS follow_flag,
        COALESCE(mr.revenue, 0) AS revenue
    FROM pairs_all pa
    LEFT JOIN active_subscriptions asub
        ON pa.month = asub.month
        AND pa.fan_id = asub.fan_id
        AND pa.creator_id = asub.creator_id
    LEFT JOIN active_follows af
        ON pa.month = af.month
        AND pa.fan_id = af.fan_id
        AND pa.creator_id = af.creator_id
    LEFT JOIN monthly_revenue mr
        ON pa.month = mr.month
        AND pa.fan_id = mr.fan_id
        AND pa.creator_id = mr.creator_id
)
SELECT 
    month,
    fan_id,
    creator_id,
    subscription_flag,
    follow_flag,
    revenue,
    CASE 
        WHEN follow_flag = 1 AND (revenue > 0 OR LAG(revenue) OVER (PARTITION BY fan_id, creator_id ORDER BY month) > 0) THEN 1 
        ELSE 0 
    END AS active_follow_flag
FROM base
inner join {{ref("valid_user")}} on valid_user.user_id = fan_id
ORDER BY 1, 2, 3