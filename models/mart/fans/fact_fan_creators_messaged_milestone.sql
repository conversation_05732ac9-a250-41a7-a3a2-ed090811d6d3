--TODO: update fanfix_messaging.messages table source
WITH first_message AS (
    SELECT
        date_trunc('day', m.created_at) AS date
        , m.sender_user_id
        , m.recipient_user_id
        , MIN(date_trunc('day', m.created_at)) OVER (PARTITION BY m.SENDER_USER_ID, m.RECIPIENT_USER_ID) AS first_message_day
    FROM {{ ref('stg_fanfix_user_users') }} u
    LEFT JOIN {{ ref("stg_fanfix_messaging_messages") }} m ON u.user_id = m.sender_user_id
    WHERE u.roles LIKE '%fan%'
)
, first_message_count AS (
    SELECT
        sender_user_id AS fan_fanfix_id
        , date
        , COUNT(DISTINCT recipient_user_id) AS message_count
    FROM FIRST_MESSAGE
    WHERE date = first_message_day
    GROUP BY 1,2
)
, cumulative_message AS (
    SELECT
        FAN_FANFIX_ID
        , date
        , message_count
        , SUM(message_count) OVER (PARTITION BY fan_fanfix_id ORDER BY date ASC) AS cumulative_message_count
    FROM FIRST_MESSAGE_COUNT
)
SELECT
    FAN_FANFIX_ID
    , MAX(cumulative_message_count) AS lifetime_creators_messaged_count
    , CASE
        WHEN MAX(cumulative_message_count) > 3 THEN True
        ELSE False
    END AS creators_messaged_milestone_achieved
    , MIN(
        CASE
            WHEN cumulative_message_count > 3 THEN date
            ELSE null
        END
    )AS creators_messaged_milestone_achieved_date
FROM CUMULATIVE_MESSAGE
GROUP BY 1
ORDER BY 2 DESC