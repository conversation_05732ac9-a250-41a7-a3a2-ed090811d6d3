WITH fanfix_users AS (
    SELECT 
        created_at
        , user_id
        , email 
    FROM {{ ref('stg_fanfix_user_users') }}
    WHERE created_at > '2023-11-17' AND roles not like '%admin%'
    UNION
    SELECT 
        created_at
        , user_id
        , email 
    FROM {{ ref('stg_firebase_prod_users') }}
    WHER<PERSON> created_at < '2023-11-17'
),
fan_state_count AS (
    SELECT 
        fan_fanfix_id
        , card_country
        , UPPER(card_state) AS card_state
        , COUNT(*) AS payment_count
    FROM {{ ref('revenue_payout_transactions') }}
    WHERE card_state is NOT null
    GROUP BY 1,2,3
),
fan_state_ranking AS (
    SELECT 
        fan_fanfix_id
        , card_country
        , card_state
        , payment_count
        , row_number() over (partition by fan_fanfix_id ORDER BY payment_count desc) AS occurance_ranking
    FROM FAN_STATE_COUNT
),
fan_state AS(
    SELECT * 
    FROM FAN_STATE_RANKING
    WHERE occurance_ranking = 1
)
SELECT  
    u.user_id
    , case 
        when email like '%@privaterelay.appleid.com' then 'apple'
        when a.provider is null then 'regular' 
        else a.provider
    end as signon_method
    , a.provider as sso_provider
    , u.email
    , up.username
    , up.first_name
    , up.last_name
    , u.created_at AS profile_created_at
    , fs.card_country
    , fs.card_state
    , total_spending_gmv
    , total_spending_nmv
    , number_of_payments
    , first_payment_date
    , latest_payment_date
    , months_on_fanfix
    , creators_subbed
    , total_message_sent
    , creators_messaged
    , dispute_rate
    , fpp.standardized_referring_domain
    , sum(
        case 
            when datediff('day', profile_created_at, c.time) <= 7 and datediff('day', profile_created_at, c.time) >= 0 then gross_revenue 
            else 0
        end
    ) as first_7day_total_spending_since_signup
    , sum(
        case 
            when datediff('day', first_payment_date, c.time) <= 7 and datediff('day', first_payment_date, c.time) >= 0 then gross_revenue 
            else 0
        end
    ) as first_7day_total_spending_since_first_payment
FROM FANFIX_USERS u
LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} up ON up.user_id = u.user_id
LEFT JOIN FAN_STATE fs ON fs.fan_fanfix_id = u.user_id
LEFT JOIN {{ ref('fact_fan_stats') }} ffs ON ffs.fan_fanfix_id = u.user_id
LEFT JOIN {{ ref('stg_fanfix_user_accounts') }} a on a.user_id = u.user_id
LEFT JOIN {{ ref('fact_creator_charges') }} c on c.fan_fanfix_id = u.user_id
LEFT JOIN {{ ref('dim_fan_ph_profile') }} fpp on fpp.email_address = u.email
GROUP BY 
    1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20, 21
ORDER BY 
    u.created_at DESC