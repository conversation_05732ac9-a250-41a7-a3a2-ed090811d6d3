WITH cumulative_messages AS (
    SELECT
        m.message_id,
        u.user_id AS fan_fanfix_id,
        date_trunc('day', m.created_at) AS date,
        COUNT(m.message_id) over (partition by u.user_id ORDER BY date_trunc('day', m.created_at) DESC) AS cumulative_message_count
    FROM {{ ref('stg_fanfix_user_users') }} u
    LEFT JOIN {{ ref('stg_fanfix_messaging_messages') }} m ON u.user_id = m.sender_user_id
    WHERE u.roles LIKE '%fan%'
)
SELECT
    fan_fanfix_id,
    MAX(cumulative_message_count) AS lifetime_message_count,
    CASE
        WHEN lifetime_message_count > 100 THEN True
        ELSE False
    END AS cumulative_message_milestone_achieved,
    MIN(
        CASE
            WHEN cumulative_message_count > 100 THEN date
            ELSE null
        END
    ) AS cumulative_message_milestone_achieved_date
FROM CUMULATIVE_MESSAGES
GROUP BY 1
ORDER BY 2 DESC