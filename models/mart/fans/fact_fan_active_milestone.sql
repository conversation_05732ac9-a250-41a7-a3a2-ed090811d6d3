WITH fan_month AS (
    SELECT
        fan_fanfix_id,
        fan_username,
        date_trunc('month', time) AS month
    FROM {{ ref("fact_creator_charges") }}
    WHERE time >= date_trunc('month', current_date) - interval '11 months'
), active_month AS (
    SELECT
        fan_fanfix_id,
        fan_username,
        COUNT(DISTINCT month) AS active_months
    FROM FAN_MONTH
    GROUP BY 1, 2
)
SELECT
    fan_fanfix_id,
    fan_username AS FAN_FANFIX_USERNAME,
FROM ACTIVE_MONTH
WHERE active_months = 12