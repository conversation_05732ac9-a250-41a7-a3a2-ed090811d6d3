WITH overall_subscription AS (
    SELECT
        FAN_FANFIX_ID,
        FAN_USERNAME,
        creator_fanfix_id,
        creator_username,
        DATE_TRUNC(DAY, TIME) AS date,
        MIN(DATE_TRUNC(DAY, TIME)) over (partition by FAN_FANFIX_ID, FAN_USERNAME, creator_fanfix_id, creator_username) AS first_subscription_day
    FROM {{ ref("fact_creator_charges") }}
    WHERE type like '%subscription%'
), first_subscription AS (
    SELECT
        FAN_FANFIX_ID,
        FAN_USERNAME,
        date,
        COUNT(DISTINCT creator_fanfix_id) AS subscription_count
    FROM OVERALL_SUBSCRIPTION
    WHERE date = first_subscription_day
    GROUP BY 1,2,3
), cumulative_subscription AS (
    SELECT
        FAN_FANFIX_ID,
        FAN_USERNAME,
        date,
        SUM(subscription_count) over (partition by FAN_FANFIX_ID,FAN_USERNAME ORDER BY date ASC) AS cumulative_subscription_count
    FROM FIRST_SUBSCRIPTION
)
SELECT
    FAN_FANFIX_ID,
    FAN_USERNAME,
    MAX(cumulative_subscription_count) AS lifetime_subscription,
    CASE
        WHEN MAX(cumulative_subscription_count) >= 3 THEN True
        ELSE False
    END AS lifetime_subscription_milestone_achieved,
    MIN(
        CASE
            WHEN cumulative_subscription_count >= 3 THEN date
            ELSE null
        END
    )AS lifetime_subscription_milestone_achieved_date
FROM CUMULATIVE_SUBSCRIPTION
GROUP BY 1, 2
ORDER BY 3 DESC