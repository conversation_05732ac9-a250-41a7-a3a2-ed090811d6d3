with new_creator as (
    select date_trunc('month', first_earning_day) as first_month,
            {{creator_segmentation_by_monthly_gmv('cp.first_30day_total_rev')}} as creator_segment,
            count(distinct fanfix_id) as new_creator_count
    from {{ref("dim_creator_profile")}} cp
    group by 1,2
    order by 1 asc, 2 asc
),
creator_monthly_gmv as (
    select date_trunc('month', time) as month,
        creator_fanfix_id,
        sum(gross_revenue) as gross_revenue,
        sum(net_revenue) as net_revenue
    from {{ref("fact_creator_charges")}} 
    group by 1,2
),
earning_creator as (
    select month, 
        {{creator_segmentation_by_monthly_gmv('mg.gross_revenue')}} as creator_segment,
        count(distinct creator_fanfix_id) as earning_creator_count
    from creator_monthly_gmv mg
    group by 1,2
    order by 1 desc
)
select ec.*, nc.new_creator_count,
        sum(earning_creator_count) over (partition by month) as total_earning_creator,
        sum(new_creator_count) over (partition by month) as total_new_creator
from earning_creator ec
left join new_creator nc on nc.first_month = ec.month and nc.creator_segment = ec.creator_segment 
order by month, creator_segment asc


