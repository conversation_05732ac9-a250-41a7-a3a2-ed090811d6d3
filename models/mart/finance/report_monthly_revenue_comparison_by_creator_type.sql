with monthly_earning_by_creator_group as (
    select earning_month, 
            case when (creator_attribution like '%trending up%' or creator_attribution like '%trending down%') then '3. recurring creators' else creator_attribution end as creator_group,
            sum(creator_count) as creator_count,
            sum(current_month_gmv) as current_month_gmv
    from {{ref("report_monthly_earning_review")}}
    group by 1,2 order by 1 desc, 2 asc
)
select * ,

    lag(creator_count, 1) over (partition by creator_group order by earning_month) as last_month_creator_count,
    lag(current_month_gmv, 1) over (partition by creator_group order by earning_month) as last_month_gmv,

    case when last_month_creator_count = 0 then null else (creator_count - last_month_creator_count)/last_month_creator_count end as creator_count_pct_change,
    case when last_month_gmv = 0 then null else (current_month_gmv - last_month_gmv)/last_month_gmv end as gmv_pct_change
from monthly_earning_by_creator_group
order by 1 desc, 2 asc
