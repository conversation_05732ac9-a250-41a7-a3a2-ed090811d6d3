with cohorted_average_revenue_per_creator_monthly as (
    select first_month, 
            initial_creator_count,
             sum(total_gmv) as cohorted_total_revenue,
            sum(total_gmv)/sum(creator_count) as avg_monthly_gmv_per_creator,
            sum(total_fanfix_earnings)/sum(creator_count) as avg_monthly_earnings_per_creator,
            sum(creator_retention_rate) as completed_lifetime
    from {{ref("earning_creator_cohort")}}
    where  first_month < date_trunc('month', current_date()) and month >= first_month and month < date_trunc('month', current_date())
    group by 1,2 order by 1 desc
),
avg_lifetime_view as (
    select sum(avg_creator_retention_rate) as avg_lifetime
    from {{ref("creator_survival_curve")}}
)
select first_month, initial_creator_count, 
        completed_lifetime,
        avg_lifetime as adjusted_avg_lifetime,
        avg_monthly_gmv_per_creator*avg_lifetime as gross_ltv,
        avg_monthly_earnings_per_creator*avg_lifetime as earning_ltv
from cohorted_average_revenue_per_creator_monthly arpuml, avg_lifetime_view alt
order by 1 desc