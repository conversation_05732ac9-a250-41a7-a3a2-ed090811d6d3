with creator_first_days as (
    select creator_fanfix_id, creator_username as username, 
            min(date_trunc('day',time)) as first_earning_day, min(date_trunc('month',time)) as first_month
    from {{ ref("fact_creator_charges") }}
    group by 1,2
),
number_of_creator_per_cohort as (
    select first_month, count(distinct creator_fanfix_id) as number_of_creator
    from creator_first_days
    group by 1 
),
subscription_payments as (
    select creator_fanfix_id, fan_fanfix_id, time, charge_id, type 
    from  {{ ref("fact_creator_charges") }} c
    where type like '%subscription%'
),
creator_fan_ranks as (
    select creator_fanfix_id, fan_fanfix_id, time,
            rank() over (partition by creator_fanfix_id, fan_fanfix_id order by time) as rownumber
    from subscription_payments
),
creator_fan_first_days as (
    select *
    from creator_fan_ranks where rownumber = 1
),
first_pass as (
    select first_month,
            date_trunc('month', time) as payment_month,
            datediff('month', first_month, payment_month) as month_diff,
            count(distinct fan_fanfix_id) as new_subscriber_count
    from creator_fan_first_days ffd
    left join creator_first_days fd on ffd.creator_fanfix_id  = fd.creator_fanfix_id
    where first_earning_day >= '2022-06-01' -- and payment_month >= '2022-06-01'
    group by 1, 2 order by 1 asc, 2 asc
),
main_report as (
    select first_month, 
            sum(Case when month_diff = 0 then new_subscriber_count else 0 end) as "Month0",
            sum(Case when month_diff = 1 then new_subscriber_count else 0 end) as "Month1",
            sum(Case when month_diff = 2 then new_subscriber_count else 0 end) as "Month2",
            sum(Case when month_diff = 3 then new_subscriber_count else 0 end) as "Month3",
            sum(Case when month_diff = 4 then new_subscriber_count else 0 end) as "Month4",
            sum(Case when month_diff = 5 then new_subscriber_count else 0 end) as "Month5",
            sum(Case when month_diff = 6 then new_subscriber_count else 0 end) as "Month6",
            sum(Case when month_diff = 7 then new_subscriber_count else 0 end) as "Month7",
            sum(Case when month_diff = 8 then new_subscriber_count else 0 end) as "Month8",
            sum(Case when month_diff = 9 then new_subscriber_count else 0 end) as "Month9",
            sum(Case when month_diff = 10 then new_subscriber_count else 0 end) as "Month10",
            sum(Case when month_diff = 11 then new_subscriber_count else 0 end) as "Month11",
            sum(Case when month_diff = 12 then new_subscriber_count else 0 end) as "Month12",
            sum(Case when month_diff = 13 then new_subscriber_count else 0 end) as "Month13",
            sum(Case when month_diff = 14 then new_subscriber_count else 0 end) as "Month14",
            sum(Case when month_diff = 15 then new_subscriber_count else 0 end) as "Month15",
            sum(Case when month_diff = 16 then new_subscriber_count else 0 end) as "Month16",
            sum(Case when month_diff = 17 then new_subscriber_count else 0 end) as "Month17",
            sum(Case when month_diff = 18 then new_subscriber_count else 0 end) as "Month18",
            sum(Case when month_diff = 19 then new_subscriber_count else 0 end) as "Month19",
            sum(Case when month_diff = 20 then new_subscriber_count else 0 end) as "Month20",
            sum(Case when month_diff = 21 then new_subscriber_count else 0 end) as "Month21",
            sum(Case when month_diff = 22 then new_subscriber_count else 0 end) as "Month22",
            sum(Case when month_diff = 23 then new_subscriber_count else 0 end) as "Month23",
            sum(Case when month_diff = 24 then new_subscriber_count else 0 end) as "Month24",
    from first_pass 
    group by 1 order by 1 asc 
)
select mr.*, number_of_creator
from main_report mr
left join number_of_creator_per_cohort cpc on cpc.first_month = mr.first_month