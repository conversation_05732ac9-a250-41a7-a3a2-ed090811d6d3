WITH churn_count_by_creator_monthly AS (
    SELECT date_trunc('month', canceled_at) AS cancel_month, creator_id,  COUNT(DISTINCT fan_id) AS churned_subscriber_count
    FROM {{ref("fact_subscription_cohort")}}
    GROUP BY 1,2 ORDER BY 1 desc, 3 desc
),
chargeback_by_creator_monthly AS (
    SELECT account_id, creator_fanfix_id, date_trunc('month', reverse_payment_time) AS reverse_payment_month, creator_username,
            SUM(gross_revenue) AS total_gross_chargeback,
            SUM(net_revenue) AS total_net_chargeback
    FROM {{ref("fact_creator_chargeback")}}
    GROUP BY 1,2,3,4
),
first_sub AS (
    SELECT account_id, creator_fanfix_id, fan_fanfix_id, MIN(time) AS first_sub
    FROM {{ref("fact_creator_charges")}}
    GROUP BY 1,2,3
),
revenue AS (
    SELECT
    c.account_id
    , c.creator_fanfix_id AS fanfix_id
    , date_trunc('month', time) AS month
    , creator_username AS username
    , SUM(gross_revenue) AS total_gmv
    , SUM(net_revenue) AS total_nmv
    , SUM(processing_fee) AS total_processing_fee
    , COUNT(*) AS total_payment_count
    , SUM(CASE WHEN type like '%subscription%' THEN gross_revenue ELSE 0 END) AS subscriptions_gmv
    , SUM(CASE WHEN type like '%subscription%' AND time = first_sub THEN gross_revenue ELSE 0 END) AS new_sub_subscription_gmv

    , SUM(CASE WHEN type like '%subscription%' THEN net_revenue ELSE 0 END) AS subscriptions_nmv
    , SUM(CASE WHEN type like '%subscription%' AND time = first_sub THEN net_revenue ELSE 0 END) AS new_sub_subscription_nmv

    , SUM(CASE WHEN type like '%subscription%' THEN processing_fee ELSE 0 END) AS subscriptions_processing_fee
    , SUM(CASE WHEN type like '%subscription%' AND time = first_sub THEN processing_fee ELSE 0 END) AS new_sub_processing_fee

    , SUM(CASE WHEN type like '%subscription%' THEN 1 ELSE 0 END) AS subscriptions_payment_count
    , SUM(CASE WHEN type like '%subscription%' AND time = first_sub THEN 1 ELSE 0 END) AS new_sub_payment_count

    , SUM(CASE WHEN type NOT like '%subscription%'  THEN gross_revenue ELSE 0 END) AS tips_gmv
    , SUM(CASE WHEN type NOT like '%subscription%'  THEN net_revenue ELSE 0 END) AS tips_nmv
    , SUM(CASE WHEN type NOT like '%subscription%'  THEN processing_fee ELSE 0 END) AS tips_processing_fee
    , SUM(CASE WHEN type NOT like '%subscription%'  THEN  1 ELSE 0 END) AS tips_payment_count
    FROM {{ref("fact_creator_charges")}} c
    LEFT JOIN FIRST_SUB fs ON fs.account_id = c.account_id AND fs.creator_fanfix_id = c.creator_fanfix_id AND fs.fan_fanfix_id = c.fan_fanfix_id
    GROUP BY 1,2,3,4
)
SELECT r.*,  cb.total_gross_chargeback, cb.total_net_chargeback,
    churn.churned_subscriber_count
FROM REVENUE r
LEFT JOIN CHURN_COUNT_BY_CREATOR_MONTHLY churn ON churn.creator_id = r.fanfix_id AND churn.cancel_month = r.month
LEFT JOIN CHARGEBACK_BY_CREATOR_MONTHLY cb ON cb.account_id = r.account_id AND cb.reverse_payment_month = r.month
WHERE month >= '2022-06-01'
ORDER BY r.month desc, churned_subscriber_count desc