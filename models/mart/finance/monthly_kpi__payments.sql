select date_trunc('month', time) as month,
    sum(gross_revenue) as gross_revenue,
    count(distinct charge_id) as payment_volume,

    sum(case when revenue_category like '%subscription%' then gross_revenue else 0 end) as subs_gmv,
    count(distinct case when revenue_category like '%subscription%' then charge_id else null end) as subs_payment_volume,

    sum(case when revenue_category not like '%subscription%' then gross_revenue else 0 end) as tips_gmv,
    count(distinct case when revenue_category not like '%subscription%' then charge_id else null end) as tips_payment_volume,

    sum(gross_revenue)/count(distinct fan_fanfix_id) as average_revenue_per_paying_user,
    avg(gross_revenue) as average_order_value,
    avg(sub_total) as average_order_value_subtotal,

    avg(case when revenue_category not like '%subscription%' then gross_revenue else null end) as tips_aov,
    avg(case when revenue_category like '%subscription%' then gross_revenue else null end) as subscription_aov,
from {{ref("fact_creator_charges")}}
group by 1 order by 1