select lifetime_in_month,
        sum(fan_count)/sum(initial_fan_count) as avg_user_retention_rate,
        sum(total_gmv)/sum(fan_count) as arpu_gmv,
        sum(total_spend)/sum(fan_count) as arpu_spend,
        sum(total_earnings)/sum(fan_count) as arpu_earnings
from {{ref("active_paying_user_cohort")}}
where first_month < date_trunc('month', current_date()) and month >= first_month --first_month >= dateadd('month', -24, current_date()) and 
group by 1 order by 1 asc