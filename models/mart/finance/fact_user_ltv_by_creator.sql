WITH month_over_month_summary AS (
    SELECT creator_fanfix_id,
            lifetime_in_month,
            SUM(fan_count)/SUM(initial_fan_count) AS avg_user_retention_rate,
            SUM(total_gmv)/SUM(fan_count) AS arpu_gmv,
            SUM(total_spend)/SUM(fan_count) AS arpu_spend,
            SUM(total_earnings)/SUM(fan_count) AS arpu_earnings
    FROM {{ref("active_paying_user_cohort_by_creator")}}
    WHERE  first_month < date_trunc('month', current_date()) AND first_month >= dateadd('month', -18, current_date())
    GROUP BY 1,2
)
SELECT creator_fanfix_id, fanfix_username,
        SUM(arpu_gmv*avg_user_retention_rate) AS ltv_gmv,
        SUM(arpu_spend*avg_user_retention_rate) AS ltv_spend,
        SUM(arpu_earnings*avg_user_retention_rate) AS ltv_earnings
FROM MONTH_OVER_MONTH_SUMMARY ms
LEFT JOIN {{ref("dim_creator_profile")}} cp
ON cp.fanfix_id = ms.creator_fanfix_id
GROUP BY 1,2