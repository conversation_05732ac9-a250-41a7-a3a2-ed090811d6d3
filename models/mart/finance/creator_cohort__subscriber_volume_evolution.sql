with creator_first_days as (
    select creator_fanfix_id , creator_username as username, 
            min(date_trunc('day',time)) as first_earning_day, min(date_trunc('month',time)) as first_month
    from {{ ref("fact_creator_charges") }}
    group by 1,2
),
first_pass as (
    select first_month, date_trunc('month', time) as payment_month, datediff('month', first_month, payment_month) as month_diff,
            count(distinct case when type like '%subscription%' then fan_fanfix_id else null end) as active_subscriber_count
    from {{ ref("fact_creator_charges") }} c
    left join creator_first_days fd on fd.creator_fanfix_id  = c.creator_fanfix_id
    where first_month >= '2022-06-01' 
    group by 1, 2,3 order by 1 asc, 2 asc
)
select first_month, 
        sum(Case when month_diff = 0 then active_subscriber_count else 0 end) as "Number of New Subscribers",
        sum(Case when month_diff = 1 then active_subscriber_count else 0 end) as "Month1",
        sum(Case when month_diff = 2 then active_subscriber_count else 0 end) as "Month2",
        sum(Case when month_diff = 3 then active_subscriber_count else 0 end) as "Month3",
        sum(Case when month_diff = 4 then active_subscriber_count else 0 end) as "Month4",
        sum(Case when month_diff = 5 then active_subscriber_count else 0 end) as "Month5",
        sum(Case when month_diff = 6 then active_subscriber_count else 0 end) as "Month6",
        sum(Case when month_diff = 7 then active_subscriber_count else 0 end) as "Month7",
        sum(Case when month_diff = 8 then active_subscriber_count else 0 end) as "Month8",
        sum(Case when month_diff = 9 then active_subscriber_count else 0 end) as "Month9",
        sum(Case when month_diff = 10 then active_subscriber_count else 0 end) as "Month10",
        sum(Case when month_diff = 11 then active_subscriber_count else 0 end) as "Month11",
        sum(Case when month_diff = 12 then active_subscriber_count else 0 end) as "Month12",
        sum(Case when month_diff = 13 then active_subscriber_count else 0 end) as "Month13",
        sum(Case when month_diff = 14 then active_subscriber_count else 0 end) as "Month14",
        sum(Case when month_diff = 15 then active_subscriber_count else 0 end) as "Month15",
        sum(Case when month_diff = 16 then active_subscriber_count else 0 end) as "Month16",
        sum(Case when month_diff = 17 then active_subscriber_count else 0 end) as "Month17",
        sum(Case when month_diff = 18 then active_subscriber_count else 0 end) as "Month18",
        sum(Case when month_diff = 19 then active_subscriber_count else 0 end) as "Month19",
        sum(Case when month_diff = 20 then active_subscriber_count else 0 end) as "Month20",
        sum(Case when month_diff = 21 then active_subscriber_count else 0 end) as "Month21",
        sum(Case when month_diff = 22 then active_subscriber_count else 0 end) as "Month22",
        sum(Case when month_diff = 23 then active_subscriber_count else 0 end) as "Month23",
        sum(Case when month_diff = 24 then active_subscriber_count else 0 end) as "Month24",
from first_pass 
group by 1 order by 1 asc 