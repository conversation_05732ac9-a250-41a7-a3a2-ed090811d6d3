with sub_first_and_last_day as ( 
    select customer_id, 
        min(created) as first_day, date_trunc('month', first_day) as first_month,
        max(created) as last_sub_creation_day, 
        max(case when canceled_at is null then dateadd('year', 12, current_date()) else canceled_at end) as last_day, date_trunc('month', last_day) as last_month,
        datediff('day', first_day, last_day) as interval,
        datediff('month', date_trunc('month', first_day), date_trunc('month', last_day)) as interval2
    from {{ref("fact_subscription_cohort")}} s
    group by 1
)
select first_month as cohort, count(distinct customer_id) as new_subscriber, 
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 1 then sum(case when interval2 >= 1 then 1 else 0 end) 
            else null 
        end as m1_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 2 then sum(case when interval2 >= 2 then 1 else 0 end) 
            else null 
        end as m2_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 3 then sum(case when interval2 >= 3 then 1 else 0 end) 
            else null 
        end as m3_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 4 then sum(case when interval2 >= 4 then 1 else 0 end) 
            else null 
        end as m4_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 5 then sum(case when interval2 >= 5 then 1 else 0 end) 
            else null 
        end as m5_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 6 then sum(case when interval2 >= 6 then 1 else 0 end) 
            else null 
        end as m6_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 7 then sum(case when interval2 >= 7 then 1 else 0 end) 
            else null 
        end as m7_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 8 then sum(case when interval2 >= 8 then 1 else 0 end) 
            else null 
        end as m8_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 9 then sum(case when interval2 >= 9 then 1 else 0 end) 
            else null 
        end as m9_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 10 then sum(case when interval2 >= 10 then 1 else 0 end) 
            else null 
        end as m10_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 11 then sum(case when interval2 >= 11 then 1 else 0 end) 
            else null 
        end as m11_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 12 then sum(case when interval2 >= 12 then 1 else 0 end) 
            else null 
        end as m12_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 13 then sum(case when interval2 >= 13 then 1 else 0 end) 
            else null 
        end as m13_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 14 then sum(case when interval2 >= 14 then 1 else 0 end) 
            else null 
        end as m14_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 15 then sum(case when interval2 >= 15 then 1 else 0 end) 
            else null 
        end as m15_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 16 then sum(case when interval2 >= 16 then 1 else 0 end) 
            else null 
        end as m16_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 17 then sum(case when interval2 >= 17 then 1 else 0 end) 
            else null 
        end as m17_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 18 then sum(case when interval2 >= 18 then 1 else 0 end) 
            else null 
        end as m18_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 19 then sum(case when interval2 >= 19 then 1 else 0 end) 
            else null 
        end as m19_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 20 then sum(case when interval2 >= 20 then 1 else 0 end) 
            else null 
        end as m20_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 21 then sum(case when interval2 >= 21 then 1 else 0 end) 
            else null 
        end as m21_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 22 then sum(case when interval2 >= 22 then 1 else 0 end) 
            else null 
        end as m22_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 23 then sum(case when interval2 >= 23 then 1 else 0 end) 
            else null 
        end as m23_retention_count,
        case 
            when datediff('month', first_month, date_trunc('month', getdate())) >= 24 then sum(case when interval2 >= 24 then 1 else 0 end) 
            else null 
        end as m24_retention_count,
from sub_first_and_last_day 
where cohort >= '2022-06-01'
group by first_month 
having new_subscriber > 0
order by 1