WITH months AS (
    SELECT
        DATE_TRUNC('month', DATEADD(month, seq4(), '2021-06-01')) AS month
    FROM TABLE(GENERATOR(ROWCOUNT => 10*12)) -- generates 120 months (10 years)
    where month <= date_trunc('month', current_date())
),
creator_months AS (
    SELECT
        cp.fanfix_id, cp.fanfix_username,
        m.month
    FROM {{ref("dim_creator_profile")}} cp
    CROSS JOIN months m
),
earnings AS (
    select  creator_fanfix_id, 
            creator_username,
            date_trunc('month', time) as payment_month,
            sum(gross_revenue) as total_gmv
    from {{ref("fact_creator_charges")}} c
    group by 1,2,3
),
full_earnings as (
    SELECT
        cm.fanfix_id as creator_fanfix_id, cm.fanfix_username as creator_username,
        cm.month as earning_month,
        COALESCE(total_gmv, 0) AS total_gmv
    FROM creator_months cm
    LEFT JOIN earnings e ON cm.fanfix_id = e.creator_fanfix_id AND cm.month = e.payment_month
),
earning_with_attribution as (
    select fe.* , cp.country, 
        case when country = 'MENA' then 'MENA' else 'not MENA' end as country_group,
        cp.last_activity_date, cp.first_earning_day,
        lag(total_gmv, 1) over (partition by creator_fanfix_id, creator_username order by earning_month) as prev_month_gmv,
        case when date_trunc('month', cp.first_earning_day) = earning_month then '1. new earner' 
            when prev_month_gmv = 0 and total_gmv > 0 then '2. reactivated earner'
            when prev_month_gmv <= total_gmv and total_gmv != 0 then '3. creators trending up'
            when prev_month_gmv > total_gmv then '4. creators trending down'
            when prev_month_gmv = 0 and total_gmv = 0 then '5. 0 earners in both month'
            else '6. other'
        end as creator_attribution,
        case when rejected_date is not null and date_trunc('month', rejected_date) = earning_month then 'rejected_this_month'
             when rejected_date is not null and date_trunc('month', rejected_date) < earning_month then 'rejected_in_previous_months'
             when last_activity_date < earning_month then 'inactive this month'
             when last_activity_date < dateadd('day', 15, earning_month) then 'inactive half of the month'
             when prev_month_gmv > 0 and ((prev_month_gmv - total_gmv)/prev_month_gmv >= 0.3)  then 'significant_revenue_slide - down 30%'
             when prev_month_gmv > 0 and (prev_month_gmv - total_gmv > 30000) then 'significant_revenue_slide - down $30K'
             else 'other'
        end as underperforming_reason
    from full_earnings fe
    left join {{ref("dim_creator_profile")}} cp on cp.fanfix_id = fe.creator_fanfix_id
    where fe.earning_month >= date_trunc('month', cp.first_earning_day)
)
select * from earning_with_attribution
