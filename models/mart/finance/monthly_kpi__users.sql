with daily_paying_user as (
        select date_trunc('day', time) as day,
                count(distinct fan_fanfix_id) as daily_paying_user
            from {{ref("fact_creator_charges")}}
            group by 1 order by 1
),
monthly_paying_user as (
    select date_trunc('month', time) as month,
        count(distinct fan_fanfix_id) as monthly_paying_user,
        sum(gross_revenue)/count(distinct fan_fanfix_id) as arpu
    from {{ref("fact_creator_charges")}}
    group by 1 order by 1
),
avg_daily_paying_user as (
    select date_trunc('month', day) as month,
            round(avg(daily_paying_user), 0) as daily_paying_user
    from daily_paying_user dpu
    group by 1 order by 1 
),
new_user_signup as (
    (select date_trunc('month', created_at)::date as month, count(distinct user_id) as signup_user_count from {{ref("stg_firebase_prod_users")}} where created_at::date < '2023-12-01' group by 1 order by 1 asc)
    union all
    (select date_trunc('month', created_at)::date as month, count(distinct user_id) as signup_user_count from {{ref("stg_fanfix_user_users")}}  where created_at::date >= '2023-12-01' group by 1 order by 1 asc)
),
web_traffic_aggregated as (
    (select month, cast(dau as int) as dau, cast(mau as int) as mau from {{ref("monthly_web_metrics")}} )
    union all
    (select date_trunc('month', month::date) as month, cast(dau as int) as dau, cast(mau as int) as mau from {{ref("google_analytics_web_traffic")}}) -- prior to 2024/08, web traffic data comes from google analytics, which we no longer use
)
select adpu.* , 
        mpu.monthly_paying_user, mpu.arpu, 
        wta.dau, wta.mau,
        nus.signup_user_count
from avg_daily_paying_user adpu
left join monthly_paying_user mpu on mpu.month = adpu.month
left join new_user_signup nus on nus.month = adpu.month
left join web_traffic_aggregated wta on wta.month = adpu.month
order by month asc
