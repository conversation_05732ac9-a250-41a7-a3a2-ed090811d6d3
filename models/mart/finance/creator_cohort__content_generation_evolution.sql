with creator_first_days as (
    select creator_id, username, first_earning_day, date_trunc('month', first_earning_day) as first_month
    from {{ref("dim_creator_first_days")}}
    where first_earning_day is not null
),
number_of_creator_per_cohort as (
    select first_month, count(distinct creator_id) as number_of_creator
    from creator_first_days
    group by 1 
),
activity as (
    select creator_id, username, created_day, post_count + blast_count + livestream_count as total_content
    from {{ref("fact_daily_creator_activity")}}
),
first_pass as (
    select first_month, date_trunc('month', created_day) as activity_month, 
            datediff('month', first_month, activity_month) as month_diff,
            sum(total_content) as total_content
    from activity a 
    left join creator_first_days on a.creator_id = creator_first_days.creator_id 
    where first_earning_day >= '2022-06-01' and activity_month >= first_month  -- and  activity_month >= '2024-08-01'
    group by 1,2 order by 1 asc, 2 asc
),
main_report as (
    select first_month, 
            sum(Case when month_diff = 0 then total_content else 0 end) as "Month0",
            sum(Case when month_diff = 1 then total_content else 0 end) as "Month1",
            sum(Case when month_diff = 2 then total_content else 0 end) as "Month2",
            sum(Case when month_diff = 3 then total_content else 0 end) as "Month3",
            sum(Case when month_diff = 4 then total_content else 0 end) as "Month4",
            sum(Case when month_diff = 5 then total_content else 0 end) as "Month5",
            sum(Case when month_diff = 6 then total_content else 0 end) as "Month6",
            sum(Case when month_diff = 7 then total_content else 0 end) as "Month7",
            sum(Case when month_diff = 8 then total_content else 0 end) as "Month8",
            sum(Case when month_diff = 9 then total_content else 0 end) as "Month9",
            sum(Case when month_diff = 10 then total_content else 0 end) as "Month10",
            sum(Case when month_diff = 11 then total_content else 0 end) as "Month11",
            sum(Case when month_diff = 12 then total_content else 0 end) as "Month12",
            sum(Case when month_diff = 13 then total_content else 0 end) as "Month13",
            sum(Case when month_diff = 14 then total_content else 0 end) as "Month14",
            sum(Case when month_diff = 15 then total_content else 0 end) as "Month15",
            sum(Case when month_diff = 16 then total_content else 0 end) as "Month16",
            sum(Case when month_diff = 17 then total_content else 0 end) as "Month17",
            sum(Case when month_diff = 18 then total_content else 0 end) as "Month18",
            sum(Case when month_diff = 19 then total_content else 0 end) as "Month19",
            sum(Case when month_diff = 20 then total_content else 0 end) as "Month20",
            sum(Case when month_diff = 21 then total_content else 0 end) as "Month21",
            sum(Case when month_diff = 22 then total_content else 0 end) as "Month22",
            sum(Case when month_diff = 23 then total_content else 0 end) as "Month23",
            sum(Case when month_diff = 24 then total_content else 0 end) as "Month24",
    from first_pass 
    group by 1 order by 1 asc 
)
select mr.*, number_of_creator
from main_report mr
left join number_of_creator_per_cohort cpc on cpc.first_month = mr.first_month
