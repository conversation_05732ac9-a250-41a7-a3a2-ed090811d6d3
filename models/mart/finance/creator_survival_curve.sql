
select lifetime_in_month, count(distinct first_month) as number_of_cohorts,
        sum(creator_count)/sum(initial_creator_count) as avg_creator_retention_rate,
        sum(total_gmv)/sum(creator_count) as arpu_gmv,
        sum(total_fanfix_earnings)/sum(creator_count) as arpu_earnings
from {{ref("earning_creator_cohort")}}
where  first_month < date_trunc('month', current_date()) and month >= first_month and month < date_trunc('month', current_date())--first_month >= dateadd('month', -18, current_date()) 
group by 1 order by 1 asc