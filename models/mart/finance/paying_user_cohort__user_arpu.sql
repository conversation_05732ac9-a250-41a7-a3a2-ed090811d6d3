with p24m as (
    select * 
    from {{ ref("active_paying_user_cohort") }}
    where first_month >= '2022-06-01' and first_month < date_trunc('month', current_date()) and month >= first_month
)
select first_month,
        initial_fan_count,
        sum(case when lifetime_in_month = 0 then current_month_average_gmv_per_user else 0 end) as month0_value,
        sum(case when lifetime_in_month = 1 then current_month_average_gmv_per_user else 0 end) as month1_value,
        sum(case when lifetime_in_month = 2 then current_month_average_gmv_per_user else 0 end) as month2_value,
        sum(case when lifetime_in_month = 3 then current_month_average_gmv_per_user else 0 end) as month3_value,
        sum(case when lifetime_in_month = 4 then current_month_average_gmv_per_user else 0 end) as month4_value,
        sum(case when lifetime_in_month = 5 then current_month_average_gmv_per_user else 0 end) as month5_value,
        sum(case when lifetime_in_month = 6 then current_month_average_gmv_per_user else 0 end) as month6_value,
        sum(case when lifetime_in_month = 7 then current_month_average_gmv_per_user else 0 end) as month7_value,
        sum(case when lifetime_in_month = 8 then current_month_average_gmv_per_user else 0 end) as month8_value,
        sum(case when lifetime_in_month = 9 then current_month_average_gmv_per_user else 0 end) as month9_value,
        sum(case when lifetime_in_month = 10 then current_month_average_gmv_per_user else 0 end) as month10_value,
        sum(case when lifetime_in_month = 11 then current_month_average_gmv_per_user else 0 end) as month11_value,
        sum(case when lifetime_in_month = 12 then current_month_average_gmv_per_user else 0 end) as month12_value,
        sum(case when lifetime_in_month = 13 then current_month_average_gmv_per_user else 0 end) as month13_value,
        sum(case when lifetime_in_month = 14 then current_month_average_gmv_per_user else 0 end) as month14_value,
        sum(case when lifetime_in_month = 15 then current_month_average_gmv_per_user else 0 end) as month15_value,
        sum(case when lifetime_in_month = 16 then current_month_average_gmv_per_user else 0 end) as month16_value,
        sum(case when lifetime_in_month = 17 then current_month_average_gmv_per_user else 0 end) as month17_value,
        sum(case when lifetime_in_month = 18 then current_month_average_gmv_per_user else 0 end) as month18_value,
        sum(case when lifetime_in_month = 19 then current_month_average_gmv_per_user else 0 end) as month19_value,
        sum(case when lifetime_in_month = 20 then current_month_average_gmv_per_user else 0 end) as month20_value,
        sum(case when lifetime_in_month = 21 then current_month_average_gmv_per_user else 0 end) as month21_value,
        sum(case when lifetime_in_month = 22 then current_month_average_gmv_per_user else 0 end) as month22_value,
        sum(case when lifetime_in_month = 23 then current_month_average_gmv_per_user else 0 end) as month23_value,
        sum(case when lifetime_in_month = 24 then current_month_average_gmv_per_user else 0 end) as month24_value
-- first_month, month, total_gmv, total_spend, fan_count
from p24m
group by 1,2 order by 1 asc
