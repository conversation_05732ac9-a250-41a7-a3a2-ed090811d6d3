WITH fan_spend AS (
    SELECT
        creator_fanfix_id,
        creator_username,
        fan_fanfix_id,
        fan_username,
        SUM(gross_revenue) AS total_spending_gmv,
        SUM(net_revenue)   AS total_spending_nmv,
        COUNT(*)           AS number_of_payments,
        MIN(time)          AS first_payment_date,
        MAX(time)          AS latest_payment_date,
        dateDiff('month', MIN(time), current_date()) AS months_on_fanfix,
        COUNT(DISTINCT creator_fanfix_id, like(type, '%subscription%')) AS creators_subbed,
        SUM(CASE WHEN like(type, '%message%') THEN 1 ELSE 0 END) AS total_message_sent,
        COUNT(DISTINCT creator_fanfix_id, like(type, '%message%')) AS creators_messaged,
        SUM(CASE WHEN dispute_id IS NOT NULL THEN gross_revenue ELSE 0 END) / NULLIF(SUM(gross_revenue), 0) AS dispute_rate,
        COUNT(DISTINCT charge_id, like(type, '%post unlock%'))  AS number_of_post_unlocks,
        COUNT(DISTINCT charge_id, like(type, '%media unlock%')) AS number_of_media_unlocks
    FROM  {{ ref('fact_creator_charges') }} c  GROUP BY 1,2,3,4
),
ranked AS (
SELECT
    *,
    RANK() OVER (PARTITION BY creator_fanfix_id ORDER BY total_spending_gmv DESC)  AS gmv_rank_alltime,
    FROM fan_spend
)
SELECT *
FROM ranked
WHERE gmv_rank_alltime <= 50 -- top 50 fans per creator 
ORDER BY creator_fanfix_id, gmv_rank_alltime DESC
