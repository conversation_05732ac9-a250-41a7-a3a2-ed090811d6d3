-- this model is for engineering use only

SELECT
    creator_id
    , username
    , MAX(last_transaction_timestamp) AS last_earning_timestamp
    , SUM(gross_total_revenue) AS lifetime_gross_total_revenue
    , SUM(gross_subscriptions_revenue) AS lifetime_gross_subscriptions_revenue
    , SUM(gross_tips_revenue) AS lifetime_gross_tips_revenue
    , SUM(gross_subscriptions_creation_revenue) AS lifetime_gross_subscriptions_creation_revenue
    , SUM(gross_subscriptions_update_revenue) AS lifetime_gross_subscriptions_update_revenue
    , SUM(gross_media_unlock_revenue) AS lifetime_gross_media_unlock_revenue
    , SUM(gross_message_tip_revenue) AS lifetime_gross_message_tip_revenue
    , SUM(gross_livestream_tip_revenue) AS lifetime_gross_livestream_tip_revenue
    , SUM(gross_livestream_unlock_revenue) AS lifetime_gross_livestream_unlock_revenue
    , SUM(gross_livestream_revenue) AS lifetime_gross_livestream_revenue

    , SUM(gross_post_tip_revenue) AS lifetime_gross_post_tip_revenue
    , SUM(gross_POST_UNLOCK_REVENUE) AS lifetime_gross_post_unlock_revenue
    , SUM(gross_publication_revenue) AS lifetime_gross_publication_revenue
    , SUM(gross_creator_tip_revenue) AS lifetime_gross_creator_tip_revenue
    , SUM(gross_invoice_revenue) AS lifetime_gross_invoice_revenue
    , SUM(net_total_revenue) AS lifetime_net_total_revenue
    , SUM(net_subscriptions_revenue) AS lifetime_net_subscriptions_revenue
    , SUM(net_tips_revenue) AS lifetime_net_tips_revenue
    , SUM(net_subscriptions_creation_revenue) AS lifetime_net_subscriptions_creation_revenue
    , SUM(net_subscriptions_update_revenue) AS lifetime_net_subscriptions_update_revenue
    , SUM(net_media_unlock_revenue) AS lifetime_net_media_unlock_revenue
    , SUM(net_message_tip_revenue) AS lifetime_net_message_tip_revenue
    , SUM(net_livestream_tip_revenue) AS lifetime_net_livestream_tip_revenue
    , SUM(net_livestream_unlock_revenue) AS lifetime_net_livestream_unlock_revenue
    , SUM(net_livestream_revenue) AS lifetime_net_livestream_revenue

    , SUM(net_post_tip_revenue) AS lifetime_net_post_tip_revenue
    , SUM(NET_POST_UNLOCK_REVENUE) AS lifetime_net_post_unlock_revenue
    , SUM(net_publication_revenue) AS lifetime_net_publication_revenue
    , SUM(net_creator_tip_revenue) AS lifetime_net_creator_tip_revenue
    , SUM(net_invoice_revenue) AS lifetime_net_invoice_revenue

    , SUM(chargeback_amount) AS chargeback_amount
    , SUM(chargeback_dispute) AS chargeback_dispute
    , SUM(chargeback_refund) AS chargeback_refund
FROM {{ ref('fact_daily_creator_earnings') }}
WHERE creator_id IS NOT NULL
GROUP BY 1, 2
