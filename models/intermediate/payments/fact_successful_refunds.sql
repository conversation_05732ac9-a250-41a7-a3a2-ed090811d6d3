SELECT 
    r.refund_id
    , r.created_at AS refund_created_time
    , r.amount/100 AS refund_amount
    , c.charge_id
    , c.amount/100 AS charge_amount
    , c.created_at AS charge_created_time
FROM {{ ref('stg_stripe_refunds') }} r
LEFT JOIN {{ ref('stg_stripe_charges') }} c ON r.charge_id = c.charge_id
WHERE  paid = true 
    AND c.amount > 0 
    AND c.status = 'succeeded'  
    AND r.status = 'succeeded'