with charges_with_entity_rank as (
        select * ,
            row_number() over (partition by creator_fanfix_id, fan_fanfix_id, entity_id order by time asc) as entity_payment_rank
        from {{ref("fact_creator_charges")}} c
)
select c.* , 
        case when cb.charge_id is not null then 'chargeback' else 'not_chargeback' end as charge_back_flag,
        case when ds.dr_label = 'has_duplicated_records' then 'duplicated_subscription' else 'not_duplicated_subscription' end as duplicated_subscription_flag,
        case when c.entity_payment_rank > 1 and entity_id is not null and c.type not like '%subscription%' and c.type != 'livestream' and c.type != 'creator tip' and c.type != 'post' then 'duplicated_unlock' else 'not_duplicated_unlock' end as duplicated_unlock_flag,
        case when c.entity_payment_rank > 1 and entity_id is not null and (c.type = 'creator tip' or c.type = 'post') then 'duplicated_tip' else 'not_duplicated_tip' end as duplicated_tip_flag
from charges_with_entity_rank c
left join {{ref("fact_creator_chargeback")}} cb on cb.charge_id = c.charge_id
left join {{ref("duplicated_subscription")}} ds on ds.charge_id = c.charge_id