WITH creator_revenue_by_day AS (
    SELECT DISTINCT
        charges.payment_intent
        , CASE 
            WHEN (charges.paid = True AND charges.status = 'succeeded') THEN 'succeeded' 
            ELSE null 
        END AS payment_status
        , charges.outcome_reason
        , charges.outcome_seller_message
        , charges.outcome_risk_level
        , charges.charge_id
        , charges.captured_at AS captured_time
        , charges.created_at AS created_time
        , charges.card_country
        , charges.card_funding
        , tax.card_type AS payment_method
        , charges.card_id
        , {{ charge_type('charges.description') }} AS type
        , am.account_id
        , am.value AS creator_fanfix_id
        , cm.value AS fan_fanfix_id
        , CAST((charges.amount / 100) AS DECIMAL(10, 2)) AS gross_revenue
    FROM {{ ref('stg_stripe_charges') }} charges
    LEFT JOIN (SELECT * FROM {{ ref('stg_stripe_connected_accounts_metadata') }} WHERE key = 'id') am ON am.account_id = charges.destination_id
    LEFT JOIN (SELECT * FROM {{ ref('stg_stripe_customers_metadata') }} WHERE key = 'appUserId') cm ON charges.customer_id = cm.customer_id
    LEFT JOIN {{ ref('stg_stripe_application_fees') }} application_fees ON application_fees.application_fee_id = charges.application_fee_id
    LEFT JOIN {{ ref('fact_payment_tax_collection') }} tax ON charges.payment_intent = tax.payment_processor_transaction_id
    WHERE 1 = 1
        --AND paid = true
        AND charges.amount > 0
        --AND charges.status = 'succeeded'
        AND charges.merchant_id = 'acct_1Ir538D2AfqkY9Hk'

),
name_resolved_revenue AS (
    SELECT
        creator_revenue_by_day.charge_id
        , creator_revenue_by_day.captured_time
        , creator_revenue_by_day.created_time
        , card_country
        , card_funding
        , payment_method
        , card_id
        , CASE
            WHEN creator_revenue_by_day.type = 'publication' THEN 'tip jar'
            ELSE creator_revenue_by_day.type
        END AS type
        , CASE WHEN type like '%subscription%' THEN 'subscription' ELSE type END AS revenue_category
        , account_id
        , creator_fanfix_id
        , creator.username AS creator_username
        , fan_fanfix_id
        , fan.username AS fan_username
        , creator_revenue_by_day.gross_revenue
        , payment_status
        , outcome_reason AS FAIL_REASON
        , outcome_seller_message AS STRIPE_MESSAGE
        , outcome_risk_level AS RISK_LEVEL
    FROM CREATOR_REVENUE_BY_DAY
    LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} creator ON creator.user_id = creator_fanfix_id
    LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} fan ON fan.user_id = fan_fanfix_id
    )
SELECT * 
FROM NAME_RESOLVED_REVENUE