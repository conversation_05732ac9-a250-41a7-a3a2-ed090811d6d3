SELECT
    tr.transfer_id
    , tr.transfer_reversal_id
    , tr.created_at AS transfer_reversal_created_time
    , tr.amount/100 AS transfer_reversal_amount
    , c.charge_id
    , c.amount/100 AS charge_amount
    , c.created_at AS charge_created_time
    , d.amount/100 AS dispute_amount
    , d.status AS dispute_status
    , d.created_at AS disputed_time
FROM {{ ref('stg_stripe_transfer_reversals') }} tr
LEFT JOIN {{ ref('stg_stripe_charges') }} c ON tr.transfer_id = c.transfer_id
LEFT JOIN {{ ref('stg_stripe_disputes') }} d ON c.charge_id = d.charge_id
WHERE paid = true
    AND c.amount > 0
    AND c.status = 'succeeded'
    AND d.dispute_id IS NOT NULL
ORDER BY 3 DESC