SELECT
    f.fraud_decision_id
    , f.payment_id
    , c.charge_id
    , p.payment_processor_transaction_id
    , p.creator_id AS creator_fanfix_id
    , creator.username AS creator_username
    , p.fan_id AS fan_fanfix_id
    , fan.username AS fan_username
    , p.tip_type AS payment_type
    , p.payment_status
    , f.decision_status AS fraud_decision
    , f.decision_description AS fraud_decision_description
    , p.created_at AS payment_time
FROM {{ ref('stg_fanfix_payment_fraud_decisions') }} f
LEFT JOIN {{ ref('stg_fanfix_payment_payments') }} p ON p.payment_id = f.payment_id
LEFT JOIN {{ ref('stg_stripe_charges') }} c ON c.payment_intent = p.payment_processor_transaction_id
LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} creator ON creator.user_id = p.creator_id
LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} fan ON fan.user_id = p.fan_id
