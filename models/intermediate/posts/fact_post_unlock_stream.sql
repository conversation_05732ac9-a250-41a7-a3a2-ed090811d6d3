WITH post_unlock AS (
    SELECT  p.creator_id, p.created_at AS post_created_at, 
            case when p.scheduled_for is not null then scheduled_for else p.created_at end as posted_at,
            p.title AS post_content, p.subscriber_price, non_subscriber_price,
            pu.post_unlock_id, pu.fan_tip_amount_in_cents/100 AS unlock_amount, pu.created_at AS unlock_time, pu.fan_id
    FROM {{ref("stg_fanfix_premium_post_unlocks")}} pu
    LEFT JOIN {{ref("stg_fanfix_posts")}} p ON pu.post_id = p.post_id
    -- WHERE p.created_at > dateadd('day', -90, current_date())
)
SELECT 
    pu.*, 
    up.username AS fan_username
FROM POST_UNLOCK pu
LEFT JOIN {{ref("stg_fanfix_user_profiles")}} up ON pu.fan_id = up.user_id