WITH post_assets AS (
    SELECT pa.asset_id, pa.created_at, pa.post_asset_id AS post_asset_id, post_id, a.mime_type, a.name
    FROM {{ref("stg_fanfix_post_assets")}} pa
    LEFT JOIN {{ref("stg_fanfix_assets")}} a ON pa.asset_id = a.asset_id
),
unlock_stats AS (
    SELECT post_id, COUNT(DISTINCT fan_id) AS unlock_fans, SUM(amount_in_cents)/100 AS post_unlock_revenue
    FROM {{ref("stg_fanfix_premium_post_unlocks")}}
    GROUP BY 1
),
asset_stats AS (
    SELECT post_id,
            COUNT(*) AS asset_count,
            SUM(CASE WHEN mime_type like '%image%' THEN 1 ELSE 0 END) AS image_count,
            SUM(CASE WHEN mime_type like '%video%' THEN 1 ELSE 0 END) AS video_count
    FROM POST_ASSETS
    GROUP BY 1
)
SELECT 
        p.post_id AS post_id,
        creator_id,
        created_at,
        case when scheduled_for is not null then scheduled_for else created_at end as sent_at,
        is_exclusive,
        is_first_asset_public,
        is_pinned,
        like_count,
        non_subscriber_price,
        subscriber_price,
        title,
        view_count,
        scheduled_for,
        status,
        asset_count, image_count, video_count, unlock_fans, post_unlock_revenue,
        case when su.creator_fanfix_id is not null and created_at <= '2025-08-13' then 'sunroom_legacy_assets' else 'fanfix_assets' end as sunroom_asset_label
FROM {{ref("stg_fanfix_posts")}} p
LEFT JOIN ASSET_STATS assets ON p.post_id = assets.post_id 
LEFT JOIN UNLOCK_STATS us ON p.post_id = us.post_id
left join {{ref("fanfix_sunroom_bridge_user")}} su on su.creator_fanfix_id = p.creator_id