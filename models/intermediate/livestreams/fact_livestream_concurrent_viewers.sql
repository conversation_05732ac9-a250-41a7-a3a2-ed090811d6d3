WITH joined_left_events AS (
    SELECT 
        LIVESTREAM_ID,
        USER_ID,
        CREATED_AT,
        ACTIVITY_TYPE,
        ROW_NUMBER() OVER (PARTITION BY LIVESTREAM_ID, USER_ID ORDER BY CREATED_AT) as event_sequence
    FROM {{ ref('stg_fanfix_livestream_livestream_activities') }}
    WHERE ACTIVITY_TYPE IN ('joined', 'left')
        AND DELETED_AT IS NULL
),
user_sessions AS (
    SELECT 
        j.LIVESTREAM_ID,
        j.USER_ID,
        j.CREATED_AT as join_time,
        COALESCE(l.CREATED_AT, ls.ENDED_AT) as leave_time
    FROM joined_left_events j
    LEFT JOIN joined_left_events l 
        ON j.LIVESTREAM_ID = l.LIVESTREAM_ID 
        AND j.USER_ID = l.USER_ID
        AND j.ACTIVITY_TYPE = 'joined'
        AND l.ACTIVITY_TYPE = 'left'
        AND j.event_sequence + 1 = l.event_sequence
    LEFT JOIN {{ ref('stg_fanfix_livestream_livestreams') }} ls
        ON j.LIVESTREAM_ID = ls.LIVESTREAM_ID
),
minute_distribution AS (
    SELECT 
        ls.LIVESTREAM_ID as LIVESTREAM_ID,
        DATEADD('minute', seq.seq, DATE_TRUNC('minute', ls.CREATED_AT)) as minute_timestamp,
        ls.ENDED_AT
    FROM {{ ref('stg_fanfix_livestream_livestreams') }} ls
    CROSS JOIN (
        SELECT ROW_NUMBER() OVER (ORDER BY NULL) - 1 as seq
        FROM TABLE(GENERATOR(ROWCOUNT => 1440 * 5)) -- 5 days max
    ) seq
    WHERE DATEADD('minute', seq.seq, DATE_TRUNC('minute', ls.CREATED_AT)) <= 
          COALESCE(DATE_TRUNC('minute', ls.ENDED_AT), CURRENT_TIMESTAMP())
)
SELECT 
    d.LIVESTREAM_ID,
    d.minute_timestamp,
    COUNT(DISTINCT s.USER_ID) as concurrent_viewers
FROM minute_distribution d
LEFT JOIN user_sessions s
    ON d.LIVESTREAM_ID = s.LIVESTREAM_ID
    AND d.minute_timestamp >= DATE_TRUNC('minute', s.join_time)
    AND d.minute_timestamp < DATE_TRUNC('minute', s.leave_time)
GROUP BY 1, 2
ORDER BY 1, 2