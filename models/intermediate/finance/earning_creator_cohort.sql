with internal_users as (
    select * 
    from {{ref("stg_fanfix_user_users")}}
    where (roles like '%admin%')  -- or email like '%@fanfix.io%'
),
charges_excl_employees as (
    select c.* 
    from {{ref("fact_creator_charges")}} c
    where creator_fanfix_id not in (select user_id from internal_users)  
),
creator_first_month as (
    -- first payment month for each creator
    select creator_fanfix_id, min(date_trunc('month', time)) as first_month
    from charges_excl_employees e
    group by 1
),
creator_monthly_earning as (
    -- monthly earnings for each creator
    select creator_fanfix_id, date_trunc('month', time) as month, 
            sum(gross_revenue) as gmv,
            sum(sub_total) as sub_total,
            sum(gross_revenue)-sum(net_revenue)-sum(sales_tax) as fanfix_earnings
    from charges_excl_employees
    group by 1,2
),
creator_journey as (
    -- monthly earnings & indicator of month number in creator's journey
    select ums.creator_fanfix_id, month, gmv, sub_total, fanfix_earnings, 
            first_month, datediff('month', first_month, month) as lifetime_in_month
    from creator_monthly_earning ums 
    left join creator_first_month ufm on ufm.creator_fanfix_id = ums.creator_fanfix_id
    order by first_month asc, month asc
),
monthly_summary as (
    -- cohorted data & month in journey
    select first_month, month, lifetime_in_month, 
            count(distinct creator_fanfix_id) as creator_count,
            sum(gmv) as total_gmv,
            sum(sub_total) as total_earning_in_subtotal,
            sum(fanfix_earnings) as total_fanfix_earnings,
    from creator_journey
    group by 1,2,3
),
initial_benchmark as (
    select first_month, lifetime_in_month, creator_count as initial_creator_count, 
            total_fanfix_earnings as beginning_fanfix_earnings, total_gmv as beginning_gmv,
            beginning_gmv/creator_count as initial_average_gmv_per_creator,
            beginning_fanfix_earnings/creator_count as initial_average_earnings_per_creator
    from monthly_summary where lifetime_in_month = 0
),
cohorted_creator_retention_by_month as (
    select ms.*, 
            ib.initial_creator_count, ib.beginning_fanfix_earnings, ib.beginning_gmv,
            initial_average_earnings_per_creator,
            initial_average_gmv_per_creator,
            total_fanfix_earnings/creator_count as current_month_average_earnings_per_creator,
            total_gmv/creator_count as current_month_average_gmv_per_creator,
            creator_count/initial_creator_count as creator_retention_rate,
            total_fanfix_earnings/beginning_fanfix_earnings as earnings_retention_rate,
            total_gmv/beginning_gmv as gmv_retention_rate
    from monthly_summary ms 
    left join initial_benchmark ib on ib.first_month = ms.first_month 
    where ms.first_month >= '2022-06-01' and month >= '2022-06-01'
    order by first_month asc, month asc
)
select * from cohorted_creator_retention_by_month