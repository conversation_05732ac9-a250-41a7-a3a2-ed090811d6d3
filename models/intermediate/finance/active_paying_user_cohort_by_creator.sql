with internal_users as (
    select * 
    from {{ ref("stg_fanfix_user_users") }}
    where (roles like '%admin%' or email like '%@fanfix.io%') 
),
charges_excl_employees as (
    select * 
    from {{ ref("fact_creator_charges") }}
    where fan_fanfix_id not in (select user_id from internal_users)  
    -- and card_country not in ('SA','AE', 'QA', 'EG', 'LY', 'MZ', 'IQ')
),
user_first_month AS (
    SELECT  creator_fanfix_id, fan_fanfix_id, MIN(date_trunc('month', time)) AS first_month
    FROM CHARGES_EXCL_EMPLOYEES
    GROUP BY 1,2
),
user_monthly_spending AS (
    SELECT creator_fanfix_id, fan_fanfix_id, date_trunc('month', time) AS month,
            SUM(gross_revenue) AS gmv,
            SUM(sub_total) AS sub_total,
            SUM(gross_revenue)-SUM(net_revenue) AS fanfix_earnings
    FROM CHARGES_EXCL_EMPLOYEES
    GROUP BY 1,2,3
),
user_journey AS (
    SELECT ums.creator_fanfix_id, ums.fan_fanfix_id, month, gmv, sub_total, fanfix_earnings, first_month, datediff('month', first_month, month) AS lifetime_in_month
    FROM USER_MONTHLY_SPENDING ums
    LEFT JOIN USER_FIRST_MONTH ufm ON ufm.fan_fanfix_id = ums.fan_fanfix_id AND ufm.creator_fanfix_id = ums.creator_fanfix_id
    ORDER BY first_month asc, month asc
),
monthly_summary AS (
    SELECT creator_fanfix_id, first_month, month, lifetime_in_month,
            COUNT(DISTINCT fan_fanfix_id) AS fan_count,
            SUM(gmv) AS total_gmv,
            SUM(sub_total) AS total_spend,
            SUM(fanfix_earnings) AS total_earnings
    FROM USER_JOURNEY
    GROUP BY 1,2,3,4
    ORDER BY 2 asc, 3 asc
),
initial_benchmark AS (
    SELECT creator_fanfix_id, first_month, lifetime_in_month, fan_count AS initial_fan_count, total_earnings AS beginning_earnings, total_gmv AS beginning_gmv,
            beginning_gmv/initial_fan_count AS initial_average_gmv_per_user,
            beginning_earnings/initial_fan_count AS initial_average_earnings_per_user
    FROM MONTHLY_SUMMARY WHERE lifetime_in_month = 0
)
SELECT ms.*,
        ib.initial_fan_count, ib.beginning_earnings, ib.beginning_gmv,
        initial_average_earnings_per_user,
        initial_average_gmv_per_user,
        total_earnings/fan_count AS current_month_average_earnings_per_user,
        total_gmv/fan_count AS current_month_average_gmv_per_user,
        fan_count/initial_fan_count AS user_retention_rate,
        total_earnings/beginning_earnings AS earnings_retention_rate,
        total_gmv/beginning_gmv AS gmv_retention_rate
FROM MONTHLY_SUMMARY ms
LEFT JOIN INITIAL_BENCHMARK ib ON ib.first_month = ms.first_month AND ms.creator_fanfix_id = ib.creator_fanfix_id
WHERE ms.first_month >= '2022-06-01' AND month >= '2022-06-01'
ORDER BY first_month asc, month asc