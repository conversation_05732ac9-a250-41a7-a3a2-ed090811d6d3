with charges_excl_employees as (
    select * 
    from {{ ref("fact_creator_charges") }}
    inner join {{ref("valid_user")}} on valid_user.user_id = fan_fanfix_id
    -- and card_country not in ('SA','AE', 'QA', 'EG', 'LY', 'MZ', 'IQ')
),
user_first_month as (
    select fan_fanfix_id, min(date_trunc('month', time)) as first_month
    from charges_excl_employees
    group by 1
),
user_monthly_spending as (
    select fan_fanfix_id, date_trunc('month', time) as month, 
            sum(gross_revenue) as gmv,
            sum(sub_total) as sub_total,
            sum(gross_revenue)-sum(net_revenue) as fanfix_earnings,
            sum(case when revenue_category like '%subscription%' then gross_revenue else 0 end) as subscription_gmv,
            sum(case when revenue_category not like '%subscription%' then gross_revenue else 0 end) as tips_gmv 
    from charges_excl_employees
    group by 1,2
),
user_journey as (
    select ums.fan_fanfix_id, month, gmv, sub_total, fanfix_earnings, subscription_gmv, tips_gmv,
    first_month, datediff('month', first_month, month) as lifetime_in_month
    from user_monthly_spending ums 
    left join user_first_month ufm on ufm.fan_fanfix_id = ums.fan_fanfix_id
    order by first_month asc, month asc
),
monthly_summary as (
    select first_month, month, lifetime_in_month, 
            count(distinct fan_fanfix_id) as fan_count,
            sum(gmv) as total_gmv,
            sum(sub_total) as total_spend,
            sum(fanfix_earnings) as total_earnings,
            sum(subscription_gmv) as subscription_gmv,
            sum(tips_gmv) as tips_gmv,
            count(distinct case when uj.subscription_gmv > 0 then fan_fanfix_id else null end) as subscription_fan_count,
            count(distinct case when uj.tips_gmv > 0 then fan_fanfix_id else null end) as tips_fan_count
    from user_journey uj
    group by 1,2,3
    order by 1 asc, 2 asc
),
initial_benchmark as (
    select first_month, lifetime_in_month, fan_count as initial_fan_count, total_earnings as beginning_earnings, total_gmv as beginning_gmv,
            beginning_gmv/initial_fan_count as initial_average_gmv_per_user,
            beginning_earnings/initial_fan_count as initial_average_earnings_per_user
    from monthly_summary where lifetime_in_month = 0
)
select ms.*, 
        ib.initial_fan_count, ib.beginning_earnings, ib.beginning_gmv,
        initial_average_earnings_per_user,
        initial_average_gmv_per_user,
        total_earnings/fan_count as current_month_average_earnings_per_user,
        total_gmv/fan_count as current_month_average_gmv_per_user,
        fan_count/initial_fan_count as user_retention_rate,
        total_earnings/beginning_earnings as earnings_retention_rate,
        total_gmv/beginning_gmv as gmv_retention_rate,

        subscription_gmv / subscription_fan_count as current_month_avg_subscription_gmv_per_user,
        tips_gmv / tips_fan_count as current_month_avg_tips_gmv_per_user
from monthly_summary ms 
left join initial_benchmark ib on ib.first_month = ms.first_month
where ms.first_month >= '2022-06-01' and month >= '2022-06-01'
order by first_month asc, month asc