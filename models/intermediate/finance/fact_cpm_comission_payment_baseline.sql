select cp.fanfix_id, 
        cp.fanfix_username,
        
        cp.deal_creator,
        cp.prospected_by,
        cp.launch_date,
        cp.launch_revenue,
        cp.launch_revenue_without_fee_f180d,

        pd.payment_date_1,
        pd.payment_date_2,

        sum(case when c.time >= cp.launch_date and c.time < dateadd('day', 1, payment_date_1) then gross_revenue - processing_fee end) as payment_amount_1,
        sum(case 
                when payment_date_2 is not null and c.time >= dateadd('day', 1, payment_date_1) and c.time < dateadd('day', 181, cp.launch_date) then gross_revenue - processing_fee
                else null
            end) as payment_amount_2
from {{ref("dim_creator_profile")}} cp
left join {{ref("fact_cpm_payment_dates")}} pd on pd.fanfix_id = cp.fanfix_id
left join {{ref("fact_creator_charges")}} c on c.creator_fanfix_id = cp.fanfix_id
group by 1,2,3,4,5,6,7,8,9
