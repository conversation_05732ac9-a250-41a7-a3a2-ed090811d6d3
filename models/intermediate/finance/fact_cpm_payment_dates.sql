WITH payment_calculation AS (
  SELECT 
    FANFIX_ID,
    LAUNCH_DATE,
    
    -- Calculate revenue end date (launch + 180 days)
    DATEADD('day', 180, LAUNCH_DATE) AS revenue_end_date,
    
    -- Find the next payment date after launch (06/30 or 12/31)
    CASE 
      WHEN LAUNCH_DATE IS NULL THEN NULL
      WHEN MONTH(LAUNCH_DATE) <= 6
        THEN DATE(CONCAT(YEAR(LAUNCH_DATE), '-06-30'))
      ELSE DATE(CONCAT(YEAR(LAUNCH_DATE), '-12-31'))
    END AS next_payment_date,
    
    -- Find the second payment date (always 6 months after the first)
    CASE 
      WHEN LAUNCH_DATE IS NULL THEN NULL
      WHEN MONTH(LAUNCH_DATE) <= 6 
        THEN DATE(CONCAT(YEAR(LAUNCH_DATE), '-12-31'))
      ELSE DATE(CONCAT(YEAR(LAUNCH_DATE) + 1, '-06-30'))
    END AS second_payment_date
    
  FROM {{ref("dim_creator_profile")}}
)

SELECT 
  FANFIX_ID,
  <PERSON>UNCH_DATE,
  
  -- Payment Date 1: Always the next payment date after launch
  next_payment_date AS payment_date_1,
  
  -- Payment Date 2: Only if revenue period extends beyond first payment date
  CASE 
    WHEN LAUNCH_DATE IS NULL THEN NULL
    WHEN revenue_end_date > next_payment_date THEN second_payment_date
    ELSE NULL
  END AS payment_date_2
  
FROM payment_calculation
ORDER BY 1