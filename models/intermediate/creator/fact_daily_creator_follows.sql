with daily_follows as (
    select
        distinct 
        followed_creator_id as creator_id, 
        following_user_id as user_id, 
        date_trunc('day', created_at) as day,
        1 as follows
    from {{ref("stg_fanfix_creator_follows")}}
    union all 
    select
        distinct 
        followed_creator_id as creator_id, 
        following_user_id as user_id, 
        date_trunc('day', deleted_at) as day,
        -1 as follows
    from {{ref("stg_fanfix_creator_follows")}} 
    where deleted_at is not null
), daily_net_follows as (
    select
        creator_id, 
        day, 
        sum(follows) as follows
    from daily_follows
    group by 1, 2
), date_bounds as (
    select 
        min(day) as min_date,
        current_date() as max_date
    from daily_net_follows
), date_series as (
    select 
        dateadd(day, seq4(), (select min_date from date_bounds)) as day
    from table(generator(rowcount => 1000))
    where dateadd(day, seq4(), (select min_date from date_bounds)) <= (select max_date from date_bounds)
), date_range as (
    select 
        dnf.creator_id,
        ds.day
    from (select distinct creator_id from daily_net_follows) dnf
    cross join date_series ds
), complete_daily_net_follows as (
    select
        dr.creator_id, 
        dr.day,
        coalesce(dnf.follows, 0) as follows
    from date_range dr
    left join daily_net_follows dnf
    on dr.creator_id = dnf.creator_id and dr.day = dnf.day 
)
select
    creator_id, 
    day, 
    follows as net_follows, 
    sum(follows) over (partition by creator_id order by day ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) as follows
from complete_daily_net_follows