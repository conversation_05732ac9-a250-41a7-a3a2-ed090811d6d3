SELECT fd.creator_id, fd.username, fd.first_earning_day, fd.launch_date,
    SUM(CASE WHEN datediff('day', first_earning_day, created_day) <= 30 AND datediff('day', first_earning_day, created_day) >= 0
        THEN post_count ELSE 0 END) AS first_30day_total_post_since_earning,
    SUM(CASE WHEN datediff('day', launch_date, created_day) <= 30 AND datediff('day', launch_date, created_day) >= 0
        THEN post_count ELSE 0 END) AS first_30day_total_post_since_launch,
    SUM(CASE WHEN datediff('day', relaunch_date, created_day) <= 30 AND datediff('day', relaunch_date, created_day) >= 0
        THEN post_count ELSE 0 END) AS first_30day_total_post_since_relaunch,

    SUM(CASE WHEN datediff('day', first_earning_day, created_day) <= 30 AND datediff('day', first_earning_day, created_day) >= 0
        THEN blast_count ELSE 0 END) AS first_30day_total_blast_since_earning,
    SUM(CASE WHEN datediff('day', launch_date, created_day) <= 30 AND datediff('day', launch_date, created_day) >= 0
        THEN blast_count ELSE 0 END) AS first_30day_total_blast_since_launch,
    SUM(CASE WHEN datediff('day', relaunch_date, created_day) <= 30 AND datediff('day', relaunch_date, created_day) >= 0
        THEN blast_count ELSE 0 END) AS first_30day_total_blast_since_relaunch,

    SUM(CASE WHEN datediff('day', first_earning_day, created_day) <= 30 AND datediff('day', first_earning_day, created_day) >= 0
        THEN outgoing_dm_count ELSE 0 END) AS first_30day_total_dm_since_earning,
    SUM(CASE WHEN datediff('day', launch_date, created_day) <= 30 AND datediff('day', launch_date, created_day) >= 0
        THEN outgoing_dm_count ELSE 0 END) AS first_30day_total_dm_since_launch,
    SUM(CASE WHEN datediff('day', relaunch_date, created_day) <= 30 AND datediff('day', relaunch_date, created_day) >= 0
        THEN outgoing_dm_count ELSE 0 END) AS first_30day_total_dm_since_relaunch
FROM {{ref("dim_creator_first_days")}} fd
LEFT JOIN {{ref("fact_daily_creator_activity")}} a ON fd.creator_Id = a.creator_id
GROUP BY 1,2,3,4
ORDER BY 5 desc