WITH gm_data AS (
    SELECT gm.creator_id, gm.created_at, gm.text, gm.type, fa.ORIGINAL_URL AS image_url, unlock_price_in_cents/100 AS unlock_price
    FROM {{ ref ('stg_fanfix_greeting_messages') }} gm
    LEFT JOIN {{ ref ('stg_fanfix_greeting_message_assets') }} gma ON gma.greeting_message_id = gm.id
    LEFT JOIN {{ ref ('stg_fanfix_assets') }} fa ON fa.asset_id = gma.asset_id
    GROUP BY 1,2,3,4,5,6
),
subscriber_gm AS (
    SELECT creator_id, text, image_url,  unlock_price, row_number() over (partition by creator_id ORDER BY created_at desc) AS newest_ranking
    FROM GM_DATA
    WHERE type = 'subscriber' OR type is null
),
follower_gm AS (
    SELECT creator_id,  text, image_url,  unlock_price, row_number() over (partition by creator_id ORDER BY created_at desc) AS newest_ranking
    FROM GM_DATA
    WHERE type = 'follower'
)
SELECT CASE WHEN sg.creator_id is NOT null THEN sg.creator_id ELSE fg.creator_id END AS creator_id,
        sg.text AS subscriber_greeting_message,
        sg.image_url AS subscriber_greeting_message_picture,
        sg.unlock_price AS subscriber_greeting_message_unlock_price,
        fg.text AS follower_greeting_message,
        fg.image_url AS follower_greeting_message_picture,
        fg.unlock_price AS follower_greeting_message_unlock_price
FROM (SELECT * FROM SUBSCRIBER_GM WHERE newest_ranking = 1) sg
FULL OUTER JOIN (SELECT * FROM FOLLOWER_GM WHERE newest_ranking = 1)  fg ON fg.creator_id = sg.creator_id
ORDER BY creator_id desc

