SELECT fd.creator_id, 
        fd.username, 
        fd.first_earning_day, 
        fd.launch_date, 
        fd.relaunch_date,

        case when fd.launch_date is not null then launch_date else fd.first_earning_day end as final_launch_date,
        
    SUM(CASE WHEN datediff('day', first_earning_day, date_trunc('day', c.time) ) <= 30 AND datediff('day', first_earning_day,date_trunc('day', c.time) ) >= 0
        THEN gross_revenue ELSE 0 END) AS first_30day_total_rev,

    SUM(CASE WHEN datediff('day', launch_date, date_trunc('day', c.time) ) <= 30 AND datediff('day', launch_date, date_trunc('day', c.time) ) >= 0
        THEN gross_revenue ELSE 0 END) AS launch_revenue,

    SUM(CASE 
            WHEN datediff('day', final_launch_date, c.time ) <= 180 AND datediff('day', final_launch_date, c.time) >= 0
        THEN c.gross_revenue - c.processing_fee ELSE 0 END) AS launch_revenue_without_fee_f180d, -- this is related to CPM comission
    
        SUM(CASE 
            WHEN datediff('day', final_launch_date, c.time ) <= 180 AND datediff('day', final_launch_date, c.time) >= 0
        THEN c.gross_revenue ELSE 0 END) AS launch_revenue_with_fee_f180d, -- this is related to CPM comission

    sum(case when datediff('day', launch_date, date_trunc('day', c.time)) <= 7 and datediff('day', launch_date, date_trunc('day', c.time)) >= 0
        then gross_revenue else 0 end) as first7day_revenue,

    count(distinct CASE WHEN datediff('day', first_earning_day, date_trunc('day', c.time) ) <= 30 AND datediff('day', first_earning_day, date_trunc('day', c.time) ) >= 0 
        THEN fan_fanfix_id ELSE null END) AS first_30day_paying_users,

    count(distinct CASE WHEN datediff('day', launch_date, date_trunc('day', c.time) ) <= 30 AND datediff('day', launch_date, date_trunc('day', c.time) ) >= 0 
        THEN fan_fanfix_id ELSE null END) AS launch_period_paying_users,


    count(distinct CASE WHEN datediff('day', first_earning_day, date_trunc('day', c.time) ) <= 30 AND datediff('day', first_earning_day, date_trunc('day', c.time) ) >= 0
            and revenue_category like '%subscription%'
        THEN fan_fanfix_id ELSE null END) AS first_30day_subscribers,

    count(distinct CASE WHEN datediff('day', launch_date, date_trunc('day', c.time) ) <= 30 AND datediff('day', launch_date, date_trunc('day', c.time) ) >= 0 
            and revenue_category like '%subscription%'
        THEN fan_fanfix_id ELSE null END) AS launch_period_subscribers,

    SUM(CASE WHEN datediff('day', relaunch_date, date_trunc('day', c.time) ) <= 30 AND datediff('day', relaunch_date, date_trunc('day', c.time) ) >= 0
        THEN gross_revenue ELSE 0 END) AS relaunch_revenue
from {{ ref("dim_creator_first_days") }} fd
left join {{ ref("fact_creator_charges") }} c on c.creator_fanfix_id = fd.creator_id
GROUP BY 1,2,3,4,5,6