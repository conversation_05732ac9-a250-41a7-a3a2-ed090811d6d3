WITH monthly_revenue_by_creator_by_fan AS (
    SELECT date_trunc('month', time) AS month,
         creator_fanfix_id, creator_username,
         fan_fanfix_id, fan_username,
         SUM(gross_revenue) AS gross_revenue,
         SUM(net_revenue) AS net_revenue
    FROM {{ ref ('fact_creator_charges') }}
    GROUP BY 1,2,3,4,5
),
fan_ranking_monthly AS (
    SELECT *,
        row_number() over (partition by month, creator_fanfix_id, creator_username ORDER BY gross_revenue desc) AS fan_ranking
    FROM MONTHLY_REVENUE_BY_CREATOR_BY_FAN
)
SELECT *,
    CASE WHEN fan_ranking <= 3 THEN True ELSE False END AS top_3_fan_tag,
    CASE WHEN fan_ranking <= 5 THEN True ELSE False END AS top_5_fan_tag,
    CASE WHEN fan_ranking <= 10 THEN True ELSE False END AS top_10_fan_tag
FROM FAN_RANKING_MONTHLY