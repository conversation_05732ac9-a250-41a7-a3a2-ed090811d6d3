with daily_posts as (
    (
        SELECT created_at::date AS created_day, creator_id, COUNT(DISTINCT post_id) AS COUNT, 'posts' AS type
        FROM {{ref("stg_fanfix_posts")}}
        WHERE creator_id is NOT null AND created_at >= '2023-11-17'
        GROUP BY 1,2
    )
    UNION
    (
        SELECT createdat::date AS created_day, creatorid AS creator_id, COUNT(DISTINCT _firestore_document_id) AS COUNT, 'posts' AS type
        FROM {{ref("stg_firebase_prod_post")}}
        WHERE createdat < '2023-11-17' AND creatorid is NOT null
        GROUP BY 1,2
    )
)
select * from daily_posts