WITH creator_monthly_ranking AS (
    SELECT *,
            rank() over (partition by creator_id, username ORDER BY total_revenue desc) AS revenue_ranking
    FROM {{ref("fact_monthly_creator_earnings_bi")}}
),
creator_lifetime_apu AS (
    SELECT creator_fanfix_id, COUNT(DISTINCT fan_fanfix_id) AS lifetime_apu, SUM(gross_revenue) lifetime_earnings
    FROM {{ ref ("fact_creator_charges") }} c
    GROUP BY 1
)
SELECT creator_id, username, month, total_revenue, ca.lifetime_apu, ca.lifetime_earnings
FROM CREATOR_MONTHLY_RANKING cr
LEFT JOIN CREATOR_LIFETIME_APU ca ON ca.creator_fanfix_id = cr.creator_id
WHERE revenue_ranking = 1