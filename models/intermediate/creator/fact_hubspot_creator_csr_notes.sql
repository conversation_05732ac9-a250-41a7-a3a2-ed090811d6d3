with notes_stream as (
    SELECT 
        creator.fanfix_id AS fanfix_id
        , note.note_timestamp
        , note_body 
        , note.csr_comment as csr_comment
        , max(note_timestamp) over (partition by fanfix_id) as most_recent_update_timestamp
    FROM {{ref("stg_hubspot_engagements_notes")}} note
    LEFT JOIN {{ref("stg_hubspot_contacts")}} creator ON creator.id = ARRAY_TO_STRING(PARSE_JSON(note.contacts), ', ')
    where note_body like '%&lt;CSR&gt;%'
)
select * 
from notes_stream 
where most_recent_update_timestamp = note_timestamp