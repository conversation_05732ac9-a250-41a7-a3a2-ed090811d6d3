WITH creator_last_post AS (
    SELECT creator_id, MA<PERSON>(created_day) AS last_post
    FROM {{ref("fact_daily_creator_activity")}}
    WHERE post_count >0
    GROUP BY 1
),
creator_last_blast AS (
    SELECT creator_id, MAX(created_day) AS last_blast
    FROM {{ref("fact_daily_creator_activity")}}
    WHERE blast_count >0
    GROUP BY 1
),
creator_last_dm AS (
    SELECT creator_id, MAX(created_day) AS last_dm
    FROM {{ref("fact_daily_creator_activity")}}
    WHERE outgoing_dm_count >0
    GROUP BY 1
),
creator_last_day AS(
    SELECT creator_id, MIN(created_day) AS first_activity, MAX(created_day) AS last_activity
    FROM {{ref("fact_daily_creator_activity")}}
    WHERE post_count > 0 OR blast_count > 0 OR outgoing_dm_count > 0
    GROUP BY 1
)
SELECT la.creator_id, first_activity, last_activity,
        last_post, last_blast, last_dm,
        datediff('day', last_post, current_date()) AS day_since_last_post,
        datediff('day', last_blast, current_date()) AS day_since_last_blast,
        datediff('day', last_dm, current_date()) AS day_since_last_dm
FROM CREATOR_LAST_DAY la
LEFT JOIN CREATOR_LAST_POST p ON la.creator_id = p.creator_id
LEFT JOIN CREATOR_LAST_BLAST b ON b.creator_id = la.creator_id
LEFT JOIN CREATOR_LAST_DM m ON la.creator_id = m.creator_id
GROUP BY 1,2,3,4,5,6,7,8, 9