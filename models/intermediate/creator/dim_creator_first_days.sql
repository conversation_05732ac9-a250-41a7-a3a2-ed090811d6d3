WITH creator_first_earning_day AS (
    SELECT creator_fanfix_id as creator_id, 
            creator_username as username, 
            MI<PERSON>(time::date) AS first_earning_day
    FROM  {{ ref('fact_creator_charges') }} 
    GROUP BY 1,2
),
creator_first_post_day_version_resolved AS (
    SELECT creator_id, MIN(created_at)::TIMESTAMP_TZ(6) AS first_post_day
    FROM {{ref('stg_fanfix_posts')}}
    GROUP BY 1
),
result as (
    SELECT cp.creator_id, up.username,
            date_trunc('day', cp.accepted_at) AS accepted_date,
            date_trunc('day', cp.rejected_at) AS rejected_date,
            date_trunc('day', pd.first_post_day) AS first_post_day,
            date_trunc('day', ed.first_earning_day) AS first_earning_day,
            CASE WHEN (close_date is NOT null AND close_date != '2023-03-08') THEN close_date ELSE null END AS launch_date,
            relaunch_date
    FROM {{ref('stg_fanfix_creator_profiles')}} cp
    LEFT JOIN {{ref('stg_fanfix_user_profiles')}} up ON up.user_id = cp.creator_id
    LEFT JOIN CREATOR_FIRST_POST_DAY_VERSION_RESOLVED pd ON pd.creator_id = cp.creator_id
    LEFT JOIN CREATOR_FIRST_EARNING_DAY ed ON ed.creator_id = cp.creator_id
    LEFT JOIN {{ref('dim_hubspot_contacts')}} contacts ON cp.creator_id = contacts.fanfix_id
    WHERE accepted_at is NOT null OR first_earning_day is NOT null
    GROUP BY 1,2,3,4,5,6,7,8 
    ORDER BY 3 desc
)
select * from result