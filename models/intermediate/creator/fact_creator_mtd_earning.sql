WITH mtd_revenue_by_creator AS (
    SELECT creator_fanfix_id, creator_username,  date_trunc('month', time) AS month,
            SUM(gross_revenue-sales_tax) AS mtd_gross_revenue,
            SUM(net_revenue) AS mtd_net_revenue,

            SUM(CASE WHEN type like '%subscription%' THEN gross_revenue-sales_tax ELSE 0 END) AS mtd_subscription_revenue,
            SUM(CASE WHEN type like '%media unlock%' THEN gross_revenue-sales_tax ELSE 0 END) AS mtd_media_unlock_revenue,
            SUM(CASE WHEN type like '%message%' THEN gross_revenue-sales_tax ELSE 0 END) AS mtd_messaging_revenue,

            SUM(CASE WHEN type like '%subscription creation%' THEN net_revenue ELSE 0 END) AS mtd_subscription_creation_net_revenue,
            SUM(CASE WHEN type like '%subscription update%' THEN net_revenue ELSE 0 END) AS mtd_subscription_update_net_revenue,
            SUM(CASE WHEN type like '%media unlock%' THEN net_revenue ELSE 0 END) AS mtd_media_unlock_net_revenue,
            SUM(CASE WHEN type like '%message%' THEN net_revenue ELSE 0 END) AS mtd_messaging_net_revenue

    FROM {{ ref ('fact_creator_charges') }}
    WHERE extract(day FROM TIME) < extract(day FROM CURRENT_DATE) --AND datediff('month', time, current_date) <= 1
    GROUP BY 1,2,3
)
SELECT mtd.*,
        lag(mtd_gross_revenue, 1) over (partition by creator_fanfix_id, creator_username ORDER BY month) AS last_month_mtd_gross_revenue,
        lag(mtd_net_revenue, 1) over (partition by creator_fanfix_id, creator_username ORDER BY month) AS last_month_mtd_net_revenue,
        lag(mtd_subscription_revenue, 1) over (partition by creator_fanfix_id, creator_username ORDER BY month) AS last_month_mtd_subscription_revenue,
        lag(mtd_media_unlock_revenue, 1) over (partition by creator_fanfix_id, creator_username ORDER BY month) AS last_month_mtd_media_unlock_revenue,
        lag(mtd_messaging_revenue, 1) over (partition by creator_fanfix_id, creator_username ORDER BY month) AS last_month_mtd_messaging_revenue,

        lag(mtd_subscription_creation_net_revenue, 1) over (partition by creator_fanfix_id, creator_username ORDER BY month) AS last_month_mtd_subscription_creation_net_revenue,
        lag(mtd_subscription_update_net_revenue, 1) over (partition by creator_fanfix_id, creator_username ORDER BY month) AS last_month_mtd_subscription_update_net_revenue,
        lag(mtd_media_unlock_net_revenue, 1) over (partition by creator_fanfix_id, creator_username ORDER BY month) AS last_month_mtd_media_unlock_net_revenue,
        lag(mtd_messaging_net_revenue, 1) over (partition by creator_fanfix_id, creator_username ORDER BY month) AS last_month_mtd_messaging_net_revenue,

        mtd_gross_revenue - last_month_mtd_gross_revenue AS mtd_gross_revenue_delta_value,

        CASE WHEN last_month_mtd_gross_revenue = 0 THEN null
            ELSE (mtd_gross_revenue - last_month_mtd_gross_revenue)/last_month_mtd_gross_revenue END AS mtd_gross_revenue_delta ,
        CASE WHEN last_month_mtd_net_revenue = 0 THEN null
            ELSE (mtd_net_revenue - last_month_mtd_net_revenue)/last_month_mtd_net_revenue END AS mtd_net_revenue_delta,

        CASE WHEN last_month_mtd_subscription_revenue = 0 THEN null
            ELSE (mtd_subscription_revenue - last_month_mtd_subscription_revenue)/last_month_mtd_subscription_revenue END AS mtd_subscription_revenue_delta,
        CASE WHEN last_month_mtd_media_unlock_revenue = 0 THEN null
            ELSE (mtd_media_unlock_revenue - last_month_mtd_media_unlock_revenue)/last_month_mtd_media_unlock_revenue END AS mtd_media_unlock_revenue_delta,
        CASE WHEN last_month_mtd_messaging_revenue = 0 THEN null
            ELSE (mtd_messaging_revenue - last_month_mtd_messaging_revenue)/last_month_mtd_messaging_revenue END AS mtd_messaging_revenue_delta,

        CASE WHEN last_month_mtd_subscription_creation_net_revenue = 0 THEN null
            ELSE (mtd_subscription_creation_net_revenue - last_month_mtd_subscription_creation_net_revenue)/last_month_mtd_subscription_creation_net_revenue END AS mtd_subscription_creation_net_revenue_delta,
        CASE WHEN last_month_mtd_subscription_update_net_revenue = 0 THEN null
            ELSE (mtd_subscription_update_net_revenue - last_month_mtd_subscription_update_net_revenue)/last_month_mtd_subscription_update_net_revenue END AS mtd_subscription_update_net_revenue_delta,
        CASE WHEN last_month_mtd_media_unlock_net_revenue = 0 THEN null
            ELSE (mtd_media_unlock_net_revenue - last_month_mtd_media_unlock_net_revenue)/last_month_mtd_media_unlock_net_revenue END AS mtd_media_unlock_net_revenue_delta,
        CASE WHEN last_month_mtd_messaging_net_revenue = 0 THEN null
            ELSE (mtd_messaging_net_revenue - last_month_mtd_messaging_net_revenue)/last_month_mtd_messaging_net_revenue END AS mtd_messaging_net_revenue_delta
FROM MTD_REVENUE_BY_CREATOR mtd