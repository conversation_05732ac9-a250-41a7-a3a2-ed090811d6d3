WITH weekly_earnings_by_creator AS (
    SELECT date_trunc('month', time) AS month, account_management_agency,
            creator_fanfix_id AS creator_id,  creator_username AS username,
            SUM(GROSS_REVENUE) AS total_gmv, SUM(net_revenue) AS total_nmv,
            COUNT(DISTINCT fan_fanfix_id) AS number_of_paying_user
    FROM {{ ref ('fact_creator_charges') }} c
    LEFT JOIN {{ ref ('dim_creator_profile') }} cp ON cp.fanfix_id = c.creator_fanfix_id
    GROUP BY 1,2,3,4
),
weekly_activity_by_creator AS (
    SELECT date_trunc('month', created_day) AS month, account_management_agency,
            creator_id, username,
            SUM(post_count) AS post_count, SUM(Blast_count) AS blast_count
    FROM {{ ref ('fact_daily_creator_activity') }} a
    LEFT JOIN {{ ref ('dim_creator_profile') }} cp ON cp.fanfix_id = a.creator_id
    WHERE post_count > 0 OR blast_count > 0 OR outgoing_dm_count > 0
    GROUP BY 1,2,3,4
)
SELECT ea.month, ea.account_management_agency,
        ea.creator_id, ea.username,
        ifnull(total_gmv, 0) AS total_gmv,
        ifnull(total_nmv, 0) AS total_nmv,
        ifnull(number_of_paying_user, 0) AS number_of_paying_user,
        ifnull(post_count, 0) AS post_count,
        ifnull(blast_count, 0) AS blast_count,

        lag(total_gmv, 1) over (partition by ea.creator_id ORDER BY ea.month asc) AS last_month_total_gmv,
        lag(total_nmv, 1) over (partition by ea.creator_id ORDER BY ea.month asc) AS last_month_total_nmv,
        lag(number_of_paying_user, 1) over (partition by ea.creator_id ORDER BY ea.month asc) AS last_month_number_of_paying_user,
        lag(post_count, 1) over (partition by ea.creator_id ORDER BY aa.month asc) AS last_month_post_count,
        lag(blast_count, 1) over (partition by ea.creator_id ORDER BY aa.month asc) AS last_month_blast_count,

        CASE WHEN last_month_total_gmv = 0 THEN null
            ELSE (total_gmv - last_month_total_gmv)/last_month_total_gmv END AS mom_gmv_delta,
        CASE WHEN last_month_total_nmv = 0 THEN null
            ELSE (total_nmv - last_month_total_nmv)/last_month_total_nmv END AS mom_nmv_delta,
        CASE WHEN number_of_paying_user = 0 THEN null
            ELSE (number_of_paying_user - last_month_number_of_paying_user)/last_month_number_of_paying_user END AS paying_user_delta,
        CASE WHEN last_month_post_count = 0 THEN null
            ELSE (post_count - last_month_post_count)/last_month_post_count END AS post_count_delta,
        CASE WHEN last_month_blast_count = 0 THEN null
            ELSE (blast_count - last_month_blast_count)/last_month_blast_count END AS blast_count_delta,
FROM WEEKLY_EARNINGS_BY_CREATOR ea
LEFT JOIN WEEKLY_ACTIVITY_BY_CREATOR aa ON ea.month = aa.month AND ea.account_management_agency = aa.account_management_agency AND ea.creator_id = aa.creator_id
ORDER BY account_management_agency, month desc