WITH launches AS (
    SELECT launch_date AS launch_date,
            launch_revenue AS launch_revenue,
            account_management_agency,
            country,
            fanfix_id,
            record_id,
            fanfix_username,
            CASE WHEN deal_creator is NOT null THEN deal_creator ELSE source_name END AS launch_manager,
            hubspot_link,
            'First Time Launch' AS launch_type
    FROM {{ ref ('dim_creator_profile') }} cp
    WHERE launch_date is NOT null AND (relaunch is null OR relaunch != true)
),
relaunches AS (
    SELECT relaunch_date AS launch_date,
            relaunch_revenue AS launch_revenue,
            account_management_agency,
            country,
            fanfix_id,
            record_id,
            fanfix_username,
            relaunch_manager AS launch_manager,
            hubspot_link,
            'Relaunch' AS launch_type
    FROM {{ ref ('dim_creator_profile') }} cp
    WHERE relaunch_date is NOT null AND relaunch = true
)
(SELECT * FROM LAUNCHES) UNION (SELECT * FROM RELAUNCHES)