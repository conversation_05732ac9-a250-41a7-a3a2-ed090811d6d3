SELECT creator_id, creator_name,
        COUNT(DISTINCT CASE WHEN created >= dateadd('day', -30, current_date()) THEN fan_id ELSE null END ) AS current_R30D_subscriber_gain,
        COUNT(DISTINCT CASE WHEN canceled_at >= dateadd('day', -30, current_date()) THEN fan_id ELSE null END ) AS current_R30D_subscriber_lost,
        COUNT(DISTINCT CASE WHEN (created < dateadd('day', -30, current_date()) AND created >= dateadd('day', -60, current_date())) THEN fan_id ELSE null END ) AS last_R30D_subscriber_gain,
        COUNT(DISTINCT CASE WHEN (canceled_at < dateadd('day', -30, current_date()) AND canceled_at >= dateadd('day', -60, current_date())) THEN fan_id ELSE null END ) AS last_R30D_subscriber_lost
FROM {{ ref ('fact_subscription_cohort') }}
GROUP BY 1,2