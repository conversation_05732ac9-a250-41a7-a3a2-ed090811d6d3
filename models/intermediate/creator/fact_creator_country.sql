with accounts as (
    select
        creator_fanfix_id, 
        account_id,
        row_number() over (partition by creator_fanfix_id order by time desc) as rn
    from {{ ref("fact_creator_charges")}}
), latest_account_country as (
    select
        a.creator_fanfix_id,
        a.account_id, 
        ca.connected_account_country as country
    from accounts a
    left join {{ ref("stg_stripe_connected_accounts")}} ca
    on a.account_id = ca.connected_account_id
    where rn = 1
), country_name_resolved as (
    select
        c.creator_fanfix_id,
        c.account_id, 
        case
            when cc.name like '%United States of America%' then 'United States'
            when cc.name like '%United Kingdom of Great Britain and Northern Ireland%' then 'United Kingdom'
            else cc.name
        end as name
    from latest_account_country c
    left join {{ref("iso_country_code")}} cc
    on c.country = cc.alpha_2
),
creator_country as (
    select
    c.fanfix_id, 
    c.fanfix_username,
    -- c.country as old,
    -- r.name as new
    case
        when c.country is not null then c.country
        else r.name
    end as country
    from {{ref("dim_hubspot_contacts")}} c
    left join country_name_resolved r
    on c.fanfix_id = r.creator_fanfix_id
)
select * from creator_country