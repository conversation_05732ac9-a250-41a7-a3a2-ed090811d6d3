with clean_lead_status_hs as (
    SELECT
        c.fanfix_id AS fanfix_id
        , c.fanfix_username AS fanfix_username
        , c.id AS hubspot_id
        , h.value AS lead_status
        , TO_TIMESTAMP(left(h.timestamp,10)) AS lead_status_update_time
        , updated_by_user_id
        , o.contact_owner as deal_creator
        , row_number() over(partition by fanfix_id, lead_status order by lead_status_update_time) as rn
    FROM {{ref("stg_hubspot_contacts_property_history")}} h
    LEFT JOIN {{ref("stg_hubspot_contacts")}} c ON c.id = h.vid
    left join {{ref("stg_hubspot_owners")}} o on o.contact_owner_user_id = h.updated_by_user_id
    where property = 'hs_lead_status'  and lower(lead_status) in ('contacted', 'responded', 'call scheduled', 'pre-launch', 'launched')
)
select
    fanfix_id, 
    fanfix_username, 
    hubspot_id, 
    lead_status,
    lead_status_update_time, 
    updated_by_user_id,
    deal_creator
from clean_lead_status_hs
where rn = 1