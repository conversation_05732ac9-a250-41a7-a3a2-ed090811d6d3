WITH active_month_by_creator AS (
    SELECT creator_id, username, month,
        row_number() over (partition by creator_id, username ORDER BY month asc) AS cumulative_active_month
    FROM {{ref("fact_monthly_creator_activity")}}
)
SELECT creator_id,
        username,
        MAX(cumulative_active_month) AS active_month_count,
        MIN(CASE WHEN cumulative_active_month >= 12 THEN month ELSE null END) AS milestone_achieved_date,
        CASE WHEN MAX(cumulative_active_month) >= 12 THEN True ELSE False END AS milestone_achieved
FROM ACTIVE_MONTH_BY_CREATOR
GROUP BY 1,2