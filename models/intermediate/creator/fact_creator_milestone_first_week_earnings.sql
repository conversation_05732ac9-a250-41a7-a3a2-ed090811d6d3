WITH creator_first_day AS (
    SELECT fanfix_id,
        CASE WHEN (launch_date is NOT null AND launch_date > first_earning_day) THEN launch_date ELSE first_earning_day END AS first_earning_day_adjusted,
        dateadd('day', 7, first_earning_day_adjusted) AS end_of_first_week
    FROM {{ref("dim_creator_profile")}} cp
    GROUP BY 1,2,3
),
first_week_earnings AS (
    SELECT creator_fanfix_id,
            creator_username,
            first_earning_day_adjusted,
            end_of_first_week,
            SUM(CASE WHEN( time < end_of_first_week AND time >= first_earning_day_adjusted ) THEN net_revenue ELSE 0 END) AS first_week_net_revenue
    FROM {{ref("fact_creator_charges")}} c
    LEFT JOIN CREATOR_FIRST_DAY cfd ON cfd.fanfix_id = c.creator_fanfix_id
    GROUP BY 1,2,3,4
)
SELECT *,
    CASE WHEN first_week_net_revenue >= 1000 THEN True ELSE false END AS milestone_achieved,
    CASE WHEN first_week_net_revenue >= 1000 THEN end_of_first_week ELSE null END AS milestone_achieved_date
FROM FIRST_WEEK_EARNINGS