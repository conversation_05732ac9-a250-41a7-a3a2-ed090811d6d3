SELECT creator_id FROM
(
    (
        SELECT creator_id -- , day_since_last_blast, day_since_last_dm, day_since_last_post
        FROM {{ref("fact_creator_last_activity")}}
        WHERE (day_since_last_blast> 30 OR day_since_last_blast is null)
        AND (day_since_last_post > 30 OR day_since_last_post is null)
        AND (day_since_last_dm > 30 OR day_since_last_dm is null)
    )
    UNION
    (
        SELECT 
            creator_id
        FROM {{ref("stg_fanfix_creator_profiles")}}
        WHERE accepted_at is NOT null
        AND creator_id NOT IN (
            SELECT DISTINCT 
            creator_id FROM {{ref("stg_fanfix_posts")}}
            )
    )
)
GROUP BY 1