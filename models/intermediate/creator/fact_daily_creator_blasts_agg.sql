with daily_message_blasts_agg as (
    (SELECT created_at::date AS created_day, creator_id, COUNT(DISTINCT message_blast_id) AS COUNT, 'blasts' AS type
    FROM {{ ref("stg_fanfix_messaging_message_blast")}}
    WHERE creator_id is NOT null  AND status in ('sent', 'unsent', 'queued', 'resent', 'scheduled') AND created_at >= '2023-11-17'
    GROUP BY 1,2
    ORDER BY 1 desc)
    UNION
    (SELECT completed_at::date AS created_day, user_id AS creator_id, COUNT(DISTINCT blast_id) AS COUNT, 'blasts' AS type
    FROM {{ref("stg_sendbird_message_blast")}}
    WHERE completed_at < '2023-11-17' AND user_id is NOT null
    GROUP BY 1,2)
)
select * from daily_message_blasts_agg