WITH subscriber_first_payment_by_creator AS (
    SELECT creator_fanfix_id, creator_username, fan_fanfix_id,
            MIN(date_trunc('day', time)) AS first_subscription_date
    FROM {{ref("fact_creator_charges")}}
    WHERE type like '%subscription%'
    GROUP BY 1,2,3
),
cumulative_daily_subscriber_count AS (
    SELECT first_subscription_date, creator_fanfix_id, creator_username, fan_fanfix_id,
            row_number() over (partition by creator_fanfix_id, creator_username ORDER BY first_subscription_date asc) AS cumulative_subscriber_count
    FROM SUBSCRIBER_FIRST_PAYMENT_BY_CREATOR
)
SELECT creator_fanfix_id,
        creator_username,
        MAX(cumulative_subscriber_count) AS lifetime_subscriber_count,
        MIN(CASE WHEN cumulative_subscriber_count >= 5000 THEN first_subscription_date ELSE null END) AS milestone_achieved_date,
        CASE WHEN MAX(cumulative_subscriber_count >= 5000) THEN True ELSE False END AS milestone_achieved
FROM CUMULATIVE_DAILY_SUBSCRIBER_COUNT
GROUP BY 1,2 ORDER BY 3 desc