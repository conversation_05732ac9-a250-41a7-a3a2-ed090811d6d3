with churn_revenue as (
    SELECT la.creator_id, 
            la.last_activity as last_activity_date, 
        SUM(CASE WHEN datediff('day',  date_trunc('day', c.time), last_activity) <= 30  and datediff('day', date_trunc('day', c.time), last_activity) >= 0  THEN gross_revenue ELSE 0 END) AS churn_revenue
    FROM {{ref("fact_creator_last_activity")}} la
    LEFT JOIN {{ref("fact_creator_charges")}} c ON la.creator_id = c.creator_fanfix_id
    GROUP BY 1,2
)
select * from churn_revenue

