with inscope_creator as (
    (
        select cp.creator_id  as creator_fanfix_id, 
                null as username
        from {{ref("stg_fanfix_creator_profiles")}} cp 
        where cp.source = 'sunroom'
    )
    union all (
        select creator_fanfix_id, 
                creator_fanfix_username as username
        from {{ref("fanfix_sunroom_bridge_user")}}
    )
),
sunroom_creator as (
    select distinct creator_fanfix_id,
                case when u.username is not null then u.username else ic.username end as username,
            case when u.deleted_at is null and u.updated_at is not null then u.updated_at else null end as activation_time,
            case when u.deleted_at is null and u.updated_at is not null then 'Account Activated' else 'Account Not Activated' end as account_activation_flag,
    from inscope_creator ic
    left join {{ref("stg_fanfix_user_users")}} u on u.user_id = ic.creator_fanfix_id
)
select * from sunroom_creator 