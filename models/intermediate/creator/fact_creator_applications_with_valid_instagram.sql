with full_applications as (
    select cp.*, sa.instagram, sa.tiktok,
        row_number() over (partition by instagram order by applied_at) as ig_handle_occurrance_asc
    from {{ref("stg_fanfix_creator_profiles")}} cp
    left join {{ref("stg_fanfix_social_accounts")}} sa on sa.creator_id = cp.creator_id
    where accepted_at is null and rejected_at is null
        and instagram is not null 
    order by applied_at desc
),
eligible_application as (
    select * 
    from full_applications 
    where length(instagram) > 4 and ig_handle_occurrance_asc = 1 
)
select * from eligible_application