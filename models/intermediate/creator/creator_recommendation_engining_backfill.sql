with new_stars as (
    select fanfix_id as creator_fanfix_id, fanfix_username as creator_fanfix_username, country
    from {{ref("dim_creator_profile")}}
    where launch_date >= dateadd(day,  -30, current_date())
    and launch_revenue >= 3000
),
rising_creators as (
    select creator_id as creator_fanfix_id, username as creator_fanfix_username, cp.country
    from {{ref("fact_creator_stats_by_period")}} sp
    left join {{ref("dim_creator_profile")}} cp on cp.fanfix_id = sp.creator_id
    where total_r30_gmv_delta >= 0.5 and last_r30_total_gmv >= 2000 and last_r30_total_gmv <= 5000
)
(select creator_fanfix_id, creator_fanfix_username, country from new_stars)
union
(select creator_fanfix_id, creator_fanfix_username, country from rising_creators)