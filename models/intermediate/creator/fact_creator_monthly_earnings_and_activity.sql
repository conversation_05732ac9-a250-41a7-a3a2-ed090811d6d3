SELECT creator_id, username, date_trunc('month', day_created) AS month,
        SUM(gross_total_revenue) AS gross_total_revenue,
        SUM(gross_subscriptions_revenue) AS gross_subscriptions_revenue,
        SUM(gross_media_unlock_revenue) AS gross_media_unlock_revenue,
        SUM(gross_post_unlock_revenue) AS gross_post_unlock_revenue,
        SUM(gross_tips_revenue) AS gross_tips_revenue,

        SUM(net_total_revenue) AS net_total_revenue,
        SUM(net_subscriptions_revenue) AS net_subscriptions_revenue,
        SUM(net_media_unlock_revenue) AS net_media_unlock_revenue,
        SUM(net_post_unlock_revenue) AS net_post_unlock_revenue,
        SUM(net_tips_revenue) AS net_tips_revenue,

        SUM(post_count) AS post_count,
        SUM(blast_count) AS blast_count,
        SUM(outgoing_dm_count) AS outgoing_dm_count
FROM {{ ref ('fact_creator_daily_earning_and_activity') }}
GROUP BY 1,2,3