with daily_outgoing_dm AS (
    SELECT m.created_at::date AS created_day, 
            sender_user_id AS creator_id, 
            COUNT(DISTINCT m.id) AS COUNT, 
            'outgoing_dm' AS type
    FROM {{ref("stg_fanfix_messaging_messages")}} m
    LEFT JOIN {{ref("stg_fanfix_user_users")}} cp ON cp.user_id = m.sender_user_id
    LEFT JOIN {{ref("stg_fanfix_greeting_messages")}} gm ON gm.creator_id = m.sender_user_id AND gm.text = m.content
    WHERE cp.roles = '["creator"]' AND sender_user_id is NOT null AND m.created_at >= '2023-11-17'  AND gm.id is  null
    AND (message_blast_id = '<nil>' OR message_blast_id is null)
    GROUP BY 1,2 ORDER BY created_day desc
)
select * from daily_outgoing_dm