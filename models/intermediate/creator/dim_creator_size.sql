WITH daily_subs AS (
    SELECT date, creator_fanfix_id, creator_username, SUM(subscriber_count) AS subscriber_count
    FROM {{ref("fact_daily_active_subscriber_by_creator")}}
    GROUP BY 1,2,3 ORDER BY 1 desc
),
peak_sub AS (
    SELECT
        creator_fanfix_id
        , MAX(subscriber_count) AS peak_subscriber_ct
    FROM DAILY_SUBS
    GROUP BY creator_fanfix_id
)
, peak_sub_date AS (
    SELECT
        p.creator_fanfix_id
        , p.peak_subscriber_ct
        , MAX(c.date::date) AS peak_subscriber_ct_date
    FROM PEAK_SUB p
    LEFT JOIN DAILY_SUBS c ON p.creator_fanfix_id = c.creator_fanfix_id AND c.subscriber_count = p.peak_subscriber_ct
    GROUP BY 1, 2
)
, cur_sub AS (
    SELECT
        creator_fanfix_id
        , SUM(subscriber_count) AS current_subscriber_ct
    FROM DAILY_SUBS
    WHERE date::date = current_date()::date
    GROUP BY 1
)
, cum_sub AS (
    SELECT
        creator_fanfix_id
        , COUNT(DISTINCT fan_fanfix_id) AS cum_subscriber_ct
    FROM {{ref("fact_subscriptions")}}
    GROUP BY 1
)
, follower_ct AS (
    SELECT
        followed_creator_id
        , COUNT(DISTINCT following_user_id) AS current_follower_ct
    FROM {{ref("stg_fanfix_creator_follows")}}
    WHERE deleted_at IS NULL
    GROUP BY 1
)
, paid_fan AS (
    SELECT DISTINCT
        fan_fanfix_id
        , creator_fanfix_id
    FROM {{ref("fact_creator_charges")}}
    WHERE time >= date_trunc('month', dateadd('month', -1, current_date()))
)
, msg_fan AS (
    SELECT DISTINCT
        m.sender_user_id AS fan_fanfix_id
        , m.recipient_user_id AS creator_fanfix_id
    FROM {{ref("stg_fanfix_user_users")}} u
    LEFT JOIN {{ref("stg_fanfix_messaging_messages")}} m ON u.user_id = m.sender_user_id
    WHERE u.roles like '%fan%'
        AND m.created_at >= date_trunc('month', dateadd('month', -1, current_date()))
)
, active_fan AS (
    SELECT * FROM MSG_FAN
    UNION
    SELECT * FROM PAID_FAN
)
, active_follower_ct AS (
    SELECT
        creator_fanfix_id
        , COUNT(DISTINCT fan_fanfix_id) AS active_follower_ct
    FROM ACTIVE_FAN fan
    INNER JOIN {{ref("stg_fanfix_creator_follows")}} fol
        ON fol.followed_creator_id = fan.creator_fanfix_id AND fol.following_user_id = fan.fan_fanfix_id
    GROUP BY 1
)
SELECT DISTINCT
    p.fanfix_id
    , p.fanfix_username
    , ps.peak_subscriber_ct_date
    , ps.peak_subscriber_ct
    , cs.cum_subscriber_ct
    , cur.current_subscriber_ct
    , f.current_follower_ct
    , af.active_follower_ct
FROM {{ref("dim_creator_profile")}} p
LEFT JOIN PEAK_SUB_DATE ps ON p.fanfix_id = ps.creator_fanfix_id
LEFT JOIN CUM_SUB cs ON p.fanfix_id = cs.creator_fanfix_id
LEFT JOIN CUR_SUB cur ON p.fanfix_id = cur.creator_fanfix_id
LEFT JOIN FOLLOWER_CT f ON p.fanfix_id = f.followed_creator_id
LEFT JOIN ACTIVE_FOLLOWER_CT af ON p.fanfix_id = af.creator_fanfix_id
