WITH monthly_creator_revenue AS (
  SELECT 
    DATE_TRUNC('MONTH', ch.TIME) AS month,
    CASE WHEN c.country IN ('United States', 'Brazil', 'MENA') THEN c.country ELSE 'Other' END AS country, 
    c.fanfix_id, c.fanfix_username,
    sum(ch.gross_revenue) as monthly_gross_revenue,
    ROW_NUMBER() OVER (PARTITION BY DATE_TRUNC('MONTH',ch.TIME),CASE
                    WHEN c.country IN ('United States', 'Brazil', 'MENA') THEN c.country
                    ELSE 'Other'
                END ORDER BY sum(ch.gross_revenue) DESC) as revenue_rank
  FROM {{ref ('fact_creator_charges')}} ch
  JOIN {{ref ('dim_creator_profile')}} c ON ch.creator_fanfix_id = c.FANFIX_ID
  GROUP BY 1,2,3,4
),
country_totals AS (
  SELECT 
    month,
    country,
    SUM(monthly_gross_revenue) as total_country_revenue,
    SUM(CASE WHEN revenue_rank <= 20 THEN monthly_gross_revenue ELSE 0 END) as top_20_revenue
  FROM monthly_creator_revenue
  GROUP BY 1,2
)
SELECT 
  month,
  COUNTRY,
  total_country_revenue,
  top_20_revenue,
  (top_20_revenue / NULLIF(total_country_revenue,0)) * 100 as top_20_percentage
FROM country_totals
WHERE total_country_revenue > 0
ORDER BY month DESC, total_country_revenue DESC