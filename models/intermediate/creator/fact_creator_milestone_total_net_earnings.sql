WITH daily_earning_by_creator AS (
    SELECT date_trunc('day', time) AS day,
            creator_fanfix_id,
            creator_username,
            SUM(net_revenue) AS daily_net_revenue
    FROM {{ref("fact_creator_charges")}}
    GROUP BY 1,2,3
),
cumulative_daily_earning_by_creator AS (
    SELECT day,
            creator_fanfix_id,
            creator_username, daily_net_revenue,
            SUM(daily_net_revenue) over (partition by creator_fanfix_id, creator_username ORDER BY day asc) AS cumulative_net_revenue
    FROM DAILY_EARNING_BY_CREATOR
)
SELECT creator_fanfix_id,
        creator_username,
    MAX(cumulative_net_revenue) AS lifetime_net_revenue,
    MIN(CASE WHEN cumulative_net_revenue >= 1000000 THEN day ELSE null END) AS milestone_achieved_date,
    CASE WHEN MAX(cumulative_net_revenue) >= 1000000 THEN True ELSE False END AS milestone_achieved
FROM CUMULATIVE_DAILY_EARNING_BY_CREATOR
GROUP BY 1,2 ORDER BY 3 desc