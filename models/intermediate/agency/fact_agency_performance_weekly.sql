WITH weekly_earnings_by_agency AS (
    SELECT date_trunc('week', time) AS week, account_management_agency, SUM(GROSS_REVENUE) AS total_gmv, SUM(net_revenue) AS total_nmv
    FROM {{ ref ('fact_creator_charges') }} c
    LEFT JOIN {{ ref ('dim_creator_profile') }} cp ON cp.fanfix_id = c.creator_fanfix_id
    GROUP BY 1,2
),
weekly_activity_by_agency AS (
    SELECT date_trunc('week', created_day) AS week, account_management_agency, COUNT(DISTINCT fanfix_id) AS active_creator_count
    FROM {{ ref ('fact_daily_creator_activity') }} a
    LEFT JOIN {{ ref ('dim_creator_profile') }} cp ON cp.fanfix_id = a.creator_id
    WHERE post_count > 0 OR blast_count > 0 OR outgoing_dm_count > 0
    GROUP BY 1,2
),
weekly_creator_launch_by_agency AS (
    SELECT date_trunc('week', first_earning_day) AS week,  account_management_agency, COUNT(DISTINCT fanfix_id) AS new_creator_count
    FROM {{ ref ('dim_creator_profile') }}
    GROUP BY 1,2
),
weekly_creator_churn_by_agency AS (
    SELECT date_trunc('week', dateadd('day',30, last_activity_date)) AS week, account_management_agency, COUNT(DISTINCT fanfix_id) AS churned_creator_count
    FROM {{ ref ('dim_creator_profile') }}
    GROUP BY 1,2
)
SELECT ea.week, ea.account_management_agency,
        ifnull(total_gmv, 0) AS total_gmv,
        ifnull(total_nmv, 0) AS total_nmv,
        ifnull(active_creator_count, 0) AS weekly_active_creator_count,
        ifnull(new_creator_count, 0) AS new_creator_count,
        ifnull(churned_creator_count, 0) AS churned_creator_count,

        lag(total_gmv, 1) over (partition by ea.account_management_agency ORDER BY ea.week asc) AS last_week_total_gmv,
        lag(total_nmv, 1) over (partition by ea.account_management_agency ORDER BY ea.week asc) AS last_week_total_nmv,
        lag(weekly_active_creator_count, 1) over (partition by ea.account_management_agency ORDER BY ea.week asc) AS last_week_active_creator_count,
        lag(new_creator_count, 1) over (partition by ea.account_management_agency ORDER BY ea.week asc) AS last_week_new_creator_count,
        lag(churned_creator_count, 1) over (partition by ea.account_management_agency ORDER BY ea.week asc) AS last_week_churned_creator_count,

        CASE WHEN last_week_total_gmv = 0 THEN null
            ELSE (total_gmv - last_week_total_gmv)/last_week_total_gmv END AS wow_gmv_delta,
        CASE WHEN last_week_total_nmv = 0 THEN null
            ELSE (total_nmv - last_week_total_nmv)/last_week_total_nmv END AS wow_nmv_delta,
        CASE WHEN last_week_active_creator_count = 0 THEN null
            ELSE (weekly_active_creator_count - last_week_active_creator_count)/last_week_active_creator_count END AS wow_weekly_active_creator_delta,
        CASE WHEN last_week_new_creator_count = 0 THEN null
            ELSE (new_creator_count - last_week_new_creator_count)/last_week_new_creator_count END AS wow_new_creator_count_delta,
        CASE WHEN last_week_churned_creator_count = 0 THEN null
            ELSE (churned_creator_count - last_week_churned_creator_count)/last_week_churned_creator_count END AS wow_churned_creator_delta
FROM WEEKLY_EARNINGS_BY_AGENCY ea
LEFT JOIN WEEKLY_ACTIVITY_BY_AGENCY aa ON ea.week = aa.week AND ea.account_management_agency = aa.account_management_agency
LEFT JOIN WEEKLY_CREATOR_CHURN_BY_AGENCY ca ON ca.week = ea.week AND ca.account_management_agency = ea.account_management_agency
LEFT JOIN WEEKLY_CREATOR_LAUNCH_BY_AGENCY cl ON cl.week = ea.week AND cl.account_management_agency = ea.account_management_agency
ORDER BY account_management_agency, week desc