with
    revenue_metrics as (
        select
            m.message_blast_id,
            coalesce(sum(m.payment_amount / 100), 0) as gmv,
            coalesce(avg(m.payment_amount / 100), 0) as avg_unlock_price,
            count(distinct m.message_id) as sent_count,
            sum(case when m.unlocked_at is not null then 1 else 0 end) as unlock_count
        from {{ ref("stg_fanfix_messaging_messages") }} m
        left join {{ ref("stg_fanfix_messaging_message_blast") }} b
            on b.message_blast_id = m.message_blast_id
        where m.message_blast_id <> '<nil>' and m.message_blast_id is not null
        group by 1
    ),
    blast_assets as (
        select
            message_blast_id,
            ba.asset_id,
            ba.message_blast_asset_id as mb_asset_id,
            a.mime_type,
            a.name
        from {{ ref("stg_fanfix_messaging_message_blast_assets") }} ba
        left join {{ ref("stg_fanfix_assets") }} a 
            on ba.asset_id = a.asset_id
    ),
    blast_asset_metrics as (
        select
            message_blast_id,
            count(distinct asset_id) as asset_count,
            sum(case when mime_type like '%image%' then 1 else 0 end) as image_count,
            sum(case when mime_type like '%video%' then 1 else 0 end) as video_count
        from blast_assets
        group by 1
    )
select
    b.message_blast_id,
    created_at,
    b.scheduled_at,
    case when scheduled_at is null then created_at else scheduled_at end as sent_at,
    b.creator_id,
    b.target_channel_ids,
    b.text,
    b.status,
    b.unlock_price_in_cents / 100 as unlock_price,
    case
        when parent_id is null or parent_id = '<nil>' then false else true
    end as is_resend,
    case
        when bam.image_count > 0 or bam.video_count > 0 then true else false
    end as include_media,
    b.is_preview_enabled,
    b.deleted_at,
    case when b.deleted_at is not null then true else false end as is_deleted,
    -- CASE WHEN b.fan_list_id is null OR b.fan_list_id = '<nil>' THEN false ELSE true
    -- END AS not_to_all_fans,
    -- CASE WHEN el.id is null THEN false ELSE true END AS has_exclusion_list,
    b.sent_user_count,
    r.gmv,
    r.sent_count,
    r.unlock_count,
    r.unlock_count * b.unlock_price_in_cents / 100 as total_face_value,
    case
        when sent_user_count = 0
        then null
        when r.unlock_count = 0
        then 0
        else round(r.unlock_count / b.sent_user_count, 2)
    end as unlock_rate,
    bam.asset_count,
    bam.image_count,
    bam.video_count,
    case
        when bam.image_count > 0 then 'contain images' else 'does NOT contain images'
    end as contains_images,
    case
        when bam.video_count > 0 then 'contains video' else 'does NOT contain videos'
    end as contains_videos,
    EXPIRES_AT,
    case when EXPIRES_AT is not null and EXPIRES_AT < current_timestamp() then 'expired' else 'not expired' end as expiration_status,
    case when EXPIRES_AT is not null then datediff('hour', sent_at, EXPIRES_AT) else null end as time_elapsed_before_expiration,
    case when su.creator_fanfix_id is not null and created_at <= '2025-08-13' then 'sunroom_legacy_assets' else 'fanfix_assets' end as sunroom_asset_label
from {{ ref("stg_fanfix_messaging_message_blast") }} b
-- LEFT JOIN FANFIX_RAW.fanfix_messaging.MESSAGE_BLAST_EXCLUDE_LISTS el ON
-- el.message_blast_id = b.id
left join revenue_metrics r on r.message_blast_id = b.message_blast_id
left join blast_asset_metrics bam on bam.message_blast_id = b.message_blast_id
left join {{ref("fanfix_sunroom_bridge_user")}} su on su.creator_fanfix_id = b.creator_id
