WITH message_blast_unlock AS (
    SELECT m.message_id::varchar as message_id,
            content AS message_content, created_at,
            sender_user_id,
            recipient_user_id,
            unlock_price_in_cents/100 AS unlock_amount,
            unlocked_at,
            message_blast_id,
            'fan' AS recipient_type_adjusted,
            'message_blast' AS message_type
    FROM {{ ref("stg_fanfix_messaging_messages") }} m
    WHERE m.message_blast_id <> '<nil>' AND m.message_blast_id IS NOT NULL
    AND created_at > dateadd('day', -90, current_date()) AND unlocked_at is NOT null
),
dm_read_receipt AS (
    SELECT  m.message_id::varchar as message_id,
            content AS message_content, m.created_at,
            sender_user_id,
            recipient_user_id,
            unlock_price_in_cents/100 AS unlock_amount,
            unlocked_at,
            message_blast_id,
        CASE WHEN u1.roles = '["fan"]' THEN 'fan'
            WHEN u1.roles = '["creator"]' THEN 'creator'
            ELSE 'unknown' END
        AS recipient_type_adjusted,
        'direct_messages' AS message_type
    FROM {{ ref("stg_fanfix_messaging_messages") }} m
    LEFT JOIN FANFIX_RAW.fanfix_user.users u1 ON u1.id = m.recipient_user_id
    WHERE ( m.message_blast_id = '<nil>' OR m.message_blast_id IS  NULL )  -- NOT message blast
         AND (recipient_type_adjusted = 'fan')
        AND unlocked_at is NOT null
),
data_union AS (
    (SELECT * FROM MESSAGE_BLAST_UNLOCK) UNION (SELECT * FROM DM_READ_RECEIPT)
)
SELECT du.*,  up.username AS fan_username
FROM DATA_UNION du
LEFT JOIN FANFIX_RAW.FANFIX.USER_PROFILES up ON du.recipient_user_id = up.user_id