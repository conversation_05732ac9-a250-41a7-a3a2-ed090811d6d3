with creator_fan_messages as (
    select
        date_trunc('hour', m.CREATED_AT) as hour,
        CASE 
            WHEN sender_u.ROLES = '["creator"]' THEN m.SENDER_USER_ID
            WHEN recipient_u.ROLES = '["creator"]' THEN m.RECIPIENT_USER_ID
            ELSE NULL
        END as creator_user_id,
        CASE 
            WHEN sender_u.ROLES = '["fan"]' THEN m.SENDER_USER_ID
            WHEN recipient_u.ROLES = '["fan"]' THEN m.RECIPIENT_USER_ID
            ELSE NULL
        END as fan_user_id,
        sum(case when m.SENDER_USER_ID = creator_user_id then 1 else 0 end) as creator_msg,
        sum(case when m.SENDER_USER_ID = fan_user_id then 1 else 0 end) as fan_msg,
        sum(case when (m.SENDER_USER_ID = fan_user_id) AND (m.payment_amount = 0) THEN 1 ELSE 0 END) as fan_message_free,
        sum(case when (m.SENDER_USER_ID = fan_user_id) AND (m.payment_amount is null) THEN 1 ELSE 0 END) as fan_message_perks,
        sum(case when (m.SENDER_USER_ID = fan_user_id) AND (m.payment_amount > 0) THEN 1 ELSE 0 END) as fan_message_paid,
        count(case when m.sender_user_id = creator_user_id and m.UNLOCK_PRICE_IN_CENTS > 0 then message_id else null end) as creator_msg_with_assets
    from {{ ref('stg_fanfix_messaging_messages') }} m
    join {{ ref('stg_fanfix_user_users') }} sender_u ON m.SENDER_USER_ID = sender_u.user_ID
    join {{ ref('stg_fanfix_user_users') }} recipient_u ON m.RECIPIENT_USER_ID = recipient_u.user_ID
    left join {{ ref('stg_fanfix_greeting_messages') }} gm
        on m.content = gm.text and m.sender_user_id = gm.creator_id
    where (m.MESSAGE_BLAST_ID is null or m.message_blast_id = '<nil>')
        and gm.id is null
    group by 1,2,3
    --  having creator_user_id is not null and fan_user_id is not null
), unlocks_charges as (
    select
        date_trunc('hour', c.time) as hour,
        c.CREATOR_FANFIX_ID, 
        c.FAN_FANFIX_ID,
        COUNT(DISTINCT CHARGE_ID) AS number_of_unlocks_from_charges,
        sum(gross_revenue) as unlock_amount_from_charges
    from {{ ref('fact_creator_charges') }} c
    where type like '%media unlock%'
    group by 1,2,3
), unlocks_messages as (
    select
        date_trunc('hour', unlocked_at) as hour,
        sender_user_id as creator_fanfix_id, 
        recipient_user_id as fan_fanfix_id, 
        count(distinct message_id) as number_of_unlocks_from_messages,
        sum(UNLOCK_PRICE_IN_CENTS) / 100 as unlock_amount_from_messages, 
        sum(case when message_blast_id is null then UNLOCK_PRICE_IN_CENTS end) / 100 as dm_unlock_amount,
        sum(case when message_blast_id is not null then UNLOCK_PRICE_IN_CENTS end) / 100 as blast_unlock_amount,
    from {{ ref('stg_fanfix_messaging_messages') }}
    where UNLOCKED_AT is not null
    group by 1,2,3     
)
select   
    m.hour,
    m.creator_user_id,
    m.fan_user_id,
    m.creator_msg,
    m.fan_msg,
    m.fan_message_free,
    m.fan_message_perks,
    m.fan_message_paid,
    m.creator_msg_with_assets,
    coalesce(uc.number_of_unlocks_from_charges,0) as number_of_unlocks_from_charges,
    coalesce(uc.unlock_amount_from_charges,0) as unlock_amount_from_charges,
    coalesce(um.number_of_unlocks_from_messages,0) as number_of_unlocks_from_messages,
    coalesce(um.unlock_amount_from_messages,0) as unlock_amount_from_messages,
    coalesce(um.dm_unlock_amount,0) as dm_unlock_amount,
    coalesce(um.blast_unlock_amount,0) as blast_unlock_amount,
from creator_fan_messages m
left join unlocks_charges uc
    on m.hour = uc.hour and uc.CREATOR_FANFIX_ID = m.CREATOR_USER_ID and uc.FAN_FANFIX_ID = m.FAN_USER_ID
left join unlocks_messages um
    on m.hour = um.hour and um.CREATOR_FANFIX_ID = m.CREATOR_USER_ID and um.FAN_FANFIX_ID = m.FAN_USER_ID
order by 1 desc, 4 desc