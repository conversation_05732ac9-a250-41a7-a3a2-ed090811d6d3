WITH daily_outgoing_dm AS (
    SELECT m.created_at::date AS created_day, sender_user_id AS creator_id,  COUNT(DISTINCT m.message_id) AS COUNT, 'outgoing_dm' AS type
    FROM {{ ref('stg_fanfix_messaging_messages') }} m
    LEFT JOIN {{ ref('stg_fanfix_creator_profiles') }} cp ON cp.creator_id = m.sender_user_id
    LEFT JOIN {{ ref('stg_fanfix_greeting_messages') }} gm ON gm.creator_id = m.sender_user_id AND gm.text = m.content
    WHERE cp.creator_id is NOT null AND sender_user_id is NOT null AND m.created_at >= '2023-11-17'  AND gm.id is  null
    AND (message_blast_id = '<nil>' OR message_blast_id is null)
    GROUP BY 1,2 ORDER BY created_day desc
),
daily_incoming_dm AS (
    SELECT m.created_at::date AS created_day, recipient_user_id AS creator_id, COUNT(DISTINCT m.message_id) AS COUNT, 'incoming_dm' AS type
    FROM {{ ref('stg_fanfix_messaging_messages') }} m
    LEFT JOIN {{ ref('stg_fanfix_user_users') }} u ON u.user_id = m.sender_user_id
    WHERE u.roles = '["fan"]' AND recipient_user_id is NOT null AND m.created_at >= '2023-11-17'
    AND (message_blast_id = '<nil>' OR message_blast_id is null)
    GROUP BY 1,2 ORDER BY 1 desc
),
union_view AS (
    (SELECT * FROM DAILY_OUTGOING_DM ) UNION (SELECT * FROM DAILY_INCOMING_DM )
)
SELECT created_day, creator_id,
    SUM(CASE WHEN type = 'outgoing_dm' THEN COUNT ELSE 0 END) AS total_daily_outgoing_dm_count,
    SUM(CASE WHEN type = 'incoming_dm' THEN COUNT ELSE 0 END) AS total_daily_incoming_dm_count,
    CASE WHEN SUM(CASE WHEN type = 'incoming_dm' THEN COUNT ELSE 0 END) = 0 THEN null
        ELSE SUM(CASE WHEN type = 'outgoing_dm' THEN COUNT ELSE 0 END)/ SUM(CASE WHEN type = 'incoming_dm' THEN COUNT ELSE 0 END)
    END AS daily_outgoing_to_incoming_dm_ratio
FROM UNION_VIEW
GROUP BY 1,2