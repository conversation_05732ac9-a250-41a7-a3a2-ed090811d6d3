WITH inv_payments_cte AS (
    SELECT
        *
        , RANK () OVER (PARTITION BY INVOICE_ID ORDER BY ATTEMPT_NUMBER DESC) AS rnk
    FROM {{ ref('stg_fanfix_subscription_invoice_payments') }}
)
SELECT
    invs.invoice_id
    , charges.charge_id
    , charges.customer_id
    , payments.payment_processor_transaction_id AS payment_intent_id
    , subs.subscription_id
    , subs.created_at::date AS subscription_created_date
    , subs.status AS subscription_status
    , user.username AS creator_username
    , subs.subscription_price_id AS price_id
    , subs.creator_id AS creator_fanfix_id
    , subs.fan_id AS fan_fanfix_id
    , charges.created_at AS invoice_paid
    , inv_payments.status AS invoice_status
    , invs.created_at::date AS subscription_period_start
    , ADD_MONTHS(invs.created_at::date, prices.duration_in_months)  AS subscription_period_end
    , CASE
        WHEN subs.status = 'expired'  THEN subs.deleted_at::date
        WHEN subs.status = 'canceled' THEN DATEADD(day, -1, subs.next_billing_cycle_starts_at::date)
        ELSE  NULL
    END AS subscription_ended_at
    , subs.canceled_at AS subscription_canceled_at
    , subs.location_country_code AS country
    , prices.duration_in_months AS recurring_interval_count
    , CAST((prices.price_in_cents/100) AS DECIMAL(10, 2)) AS plan_price
    , invs.price AS invoice_price
    , CAST((invs.total_amount - invs.tax_amount - invs.processing_fee_amount) AS DECIMAL(10, 2)) AS subtotal
    , invs.tax_amount AS sales_tax
    , payments.amount AS charged_total_usd
    , connected_accounts.default_currency AS account_default_currency
    , 'usd' AS invoice_currency
    , CAST((transfers.amount/100) AS DECIMAL(10, 2)) AS transferred_total_local_cur
    , transfers.currency AS transfer_currency
    , (prices.duration_in_months * 30) AS total_subscription_days
    , CAST(
        (invs.total_amount - invs.tax_amount - invs.processing_fee_amount) / (prices.duration_in_months * 30) AS DECIMAL(10, 6)
    ) AS revenue_per_day
FROM {{ ref('stg_fanfix_subscription_invoices') }} invs
LEFT JOIN (SELECT * FROM INV_PAYMENTS_CTE WHERE rnk = 1) inv_payments ON inv_payments.invoice_id = invs.invoice_id
LEFT JOIN {{ ref('stg_fanfix_payment_public_payments') }} payments ON inv_payments.payment_id = payments.payment_id
LEFT JOIN {{ ref('stg_stripe_charges') }} charges ON charges.payment_intent = payments.payment_processor_transaction_id
LEFT JOIN {{ ref('stg_stripe_transfers') }} transfers ON charges.transfer_id = transfers.transfer_id
LEFT JOIN {{ ref('stg_stripe_connected_accounts') }} connected_accounts ON connected_accounts.connected_account_id = charges.destination_id
LEFT JOIN {{ ref('stg_fanfix_subscription_subscriptions') }} subs ON subs.subscription_id = invs.subscription_id
LEFT JOIN {{ ref('stg_fanfix_subscription_subscription_prices') }} prices ON subs.subscription_price_id = prices.subscription_price_id
LEFT JOIN {{ ref('stg_fanfix_user_users') }} user ON subs.creator_id = user.user_id
WHERE inv_payments.status = 'Pending'
AND charges.paid = TRUE