SELECT
    subscription_id, 
    s.price_id, 
    s.creator_fanfix_id, 
    s.creator_username,
    fan_fanfix_id,   
    recurring_interval_count,  
    one_month_price, 
    sub_price AS sub_price_per_interval,
    subscription_status,
    sp.subscription_plan_id AS plan_id, 
    display_name AS subscription_membership_name,
    subscription_created_date, 
    subscription_canceled_at, 
    subscription_ended_at
FROM {{ ref('fact_subscriptions_with_valid_charges') }} s
LEFT JOIN {{ ref('dim_subscription_plan_pricing') }} spp ON spp.price_id = s.price_id
LEFT JOIN {{ ref('dim_subscription_plans') }} sp ON sp.subscription_plan_id = spp.subscription_plan_id
GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14
ORDER BY subscription_created_date DESC