WITH subs AS (
SELECT DISTINCT
    s.subscription_id
    , COALESCE(s.price_id, si.price_id) AS price_id
    , s.status AS status
    , s.customer_id
    , s.created
    , s.canceled_at
    , s.current_period_end
FROM {{ref ('stg_stripe_subscriptions') }} s
LEFT JOIN (
    SELECT 
        si.subscription_item_id
        , si.subscription_id
        , si.price_id
    FROM {{ref ('stg_stripe_subscription_items') }} si
    LEFT JOIN {{ref('stg_stripe_products')}} p ON p.product_id = si.plan_product_id
    WHERE si.plan_amount > 0 AND (p.name ilike 'publication -%' OR p.name ilike 'creator -%')
    ) si ON s.subscription_id = si.subscription_id
)
SELECT DISTINCT
    invoices.invoice_id
    , charges.charge_id
    , charges.customer_id
    , charges.payment_intent AS payment_intent_id
    , subs.subscription_id
    , subs.created::date AS subscription_created_date
    , subs.status AS subscription_status
    , user.username AS creator_username
    , prices.price_id
    , connected_accounts_metadata.value AS creator_fanfix_id
    , customers_metadata.value AS fan_fanfix_id
    , invoices.status_transitions_paid_at AS invoice_paid
    , invoices.status AS invoice_status
    , CASE
        WHEN ABS(DATEDIFF(hour, invoices.status_transitions_paid_at, invoice_line_items.period_start)) <= 3
            THEN invoices.status_transitions_paid_at
        ELSE invoice_line_items.period_start
    END::date AS subscription_period_start
    , invoice_line_items.period_end::date AS subscription_period_end
    , subs.current_period_end::date AS subscription_ended_at
    , subs.canceled_at::date AS subscription_canceled_at
    , connected_accounts.country AS country
    , prices.recurring_interval_count
    , prices.unit_amount AS plan_price
    , CAST((invoices.amount_paid) / 100 AS DECIMAL(10, 2)) AS invoice_price
    , CAST((charges.amount / 100) - COALESCE(tax.tax_amount, 0) - COALESCE(tax.processing_fee, 0) AS DECIMAL(10, 2)) AS subtotal
    , COALESCE(tax.tax_amount, 0) AS sales_tax
    , CAST((charges.amount / 100) AS DECIMAL(10, 2)) AS charged_total_usd
    , connected_accounts.default_currency AS account_default_currency
    , invoices.currency AS invoice_currency
    , CAST((transfers.amount / 100) AS DECIMAL(10, 2)) AS transferred_total_local_cur
    , transfers.currency AS transfer_currency
    , (prices.recurring_interval_count * 30) AS total_subscription_days
    , CAST(
        ((charges.amount / 100) - COALESCE(tax.tax_amount, 0)- COALESCE(tax.processing_fee, 0)) / (prices.recurring_interval_count * 30) AS DECIMAL(10, 6)
    ) AS revenue_per_day
FROM {{ ref('stg_stripe_invoices') }} invoices
INNER JOIN SUBS ON invoices.subscription_id = subs.subscription_id
INNER JOIN (
    SELECT
        invoice_id, MAX(period_start) AS period_start, MAX(period_end) AS period_end
    FROM {{ ref('stg_stripe_invoice_line_items') }}
    GROUP BY invoice_id
) AS invoice_line_items ON invoice_line_items.invoice_id = invoices.invoice_id
INNER JOIN {{ref('stg_stripe_prices')}} prices ON subs.price_id = prices.price_id -- JOIN SUBSCRIPTIONS ON plans to get product
LEFT JOIN {{ref('stg_stripe_products')}} products ON prices.product_id = products.product_id -- JOIN PLANS ON products to get product name
LEFT JOIN {{ref('stg_stripe_charges')}} charges ON charges.invoice_id = invoices.invoice_id
LEFT JOIN {{ref('stg_stripe_transfers')}} transfers ON charges.transfer_id = transfers.id
LEFT JOIN {{ref('stg_stripe_connected_accounts')}} conntected_accounts ON connected_accounts.id = charges.destination_id
LEFT JOIN {{ref('stg_stripe_connected_accounts_metadata')}} connected_accounts_metadata ON connected_accounts_metadata.account_id = connected_accounts.connected_accounts_id
LEFT JOIN {{ ref('stg_stripe_customers_metadata') }} customers_metadata ON customers_metadata.customer_id = charges.customer_id
LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} user ON connected_accounts_metadata.value = user.user_id
LEFT JOIN {{ ref('fact_payment_tax_collection') }} tax ON charges.payment_intent = tax.payment_processor_transaction_id
WHERE 1 = 1
    AND charges.paid = true
    AND charges.status = 'succeeded'
    AND invoices.status_transitions_paid_at::date < current_date()::date
    AND (connected_accounts_metadata.key = 'id' AND customers_metadata.key = 'appUserId')
    AND charges.destination_id IS NOT NULL