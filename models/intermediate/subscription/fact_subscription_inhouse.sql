WITH inv_payments_cte AS (
    SELECT
        *
        , RANK () OVER (PARTITION BY INVOICE_ID ORDER BY ATTEMPT_NUMBER DESC) AS rnk
    FROM {{ ref('stg_fanfix_subscription_invoice_payments') }}
)
SELECT
    invs.invoice_id
    , charges.charge_id
    , charges.customer_id AS customer_id
    , payments.payment_processor_transaction_id AS payment_intent_id
    , subs.subscription_id
    , subs.created_at::date AS subscription_created_date
    , cast(subs.created_at as TIMESTAMP_NTZ) as subscription_created_at
    , subs.status AS subscription_status
    , user.username AS creator_username
    , subs.subscription_price_id AS price_id
    , subs.creator_id AS creator_fanfix_id
    , subs.fan_id AS fan_fanfix_id
    , charges.created_at AS invoice_paid
    , inv_payments.status AS invoice_status
    , invs.created_at::date AS subscription_period_start
    , ADD_MONTHS(invs.created_at::date, prices.duration_in_months)  AS subscription_period_end
    , cast(invs.created_at as TIMESTAMP_NTZ) AS subscription_period_start_ts
    , cast(ADD_MONTHS(invs.created_at, prices.duration_in_months) as TIMESTAMP_NTZ) AS subscription_period_end_ts
    , CASE
        WHEN subs.status = 'expired'  THEN subs.deleted_at::date
        WHEN subs.status = 'canceled' THEN DATEADD(day, -1, subs.next_billing_cycle_starts_at::date)
        ELSE  NULL
    END AS subscription_ended_at
    , subs.canceled_at As subscription_canceled_at
    , subs.location_country_code AS country
    , prices.duration_in_months AS recurring_interval_count
    , prices.price AS plan_price
    , invs.price AS invoice_price
    , CAST((invs.total_amount - invs.tax_amount - invs.processing_fee_amount) AS DECIMAL(10, 2)) AS subtotal
    , invs.tax_amount AS sales_tax
    , payments.amount AS charged_total_usd
    , connected_accounts.default_currency AS account_default_currency
    , 'usd' AS invoice_currency
    , CAST((transfers.amount/100) AS DECIMAL(10, 2)) AS transferred_total_local_cur
    , transfers.currency AS transfer_currency
    , prices.duration_in_days AS total_subscription_days
    , CAST(
        (invs.total_amount - invs.tax_amount - invs.processing_fee_amount) / prices.duration_in_days AS DECIMAL(10, 6)
    ) AS revenue_per_day
    , subs.applied_promotion_id
    , CASE 
        WHEN subs.applied_promotion_id IS NOT NULL THEN subs.created_at::date 
        ELSE NULL 
    END AS promotion_used_date
    , CASE 
        WHEN subs.applied_promotion_id IS NOT NULL THEN subs.promotion_ends_at::date 
        ELSE NULL 
    END AS  promotion_end_date
    , CASE 
        WHEN subs.applied_promotion_id IS NOT NULL THEN subs.promotion_end_notification_sent_at
        ELSE NULL 
    END AS  promotion_end_notification_sent_at
    , CASE 
        WHEN subs.applied_promotion_id IS NULL THEN NULL
        WHEN subs.promotion_ends_at::date < invs.created_at::date THEN TRUE
        ELSE FALSE 
    END AS  is_promotion_expired
FROM {{ ref('stg_fanfix_subscription_invoices') }} invs
LEFT JOIN (SELECT * FROM inv_payments_cte WHERE rnk = 1) inv_payments ON inv_payments.invoice_id = invs.invoice_id
LEFT JOIN {{ ref('stg_fanfix_payment_payments') }} payments ON inv_payments.payment_id::text = payments.payment_id::text
LEFT JOIN {{ ref('stg_stripe_charges') }} charges ON charges.payment_intent = payments.payment_processor_transaction_id
LEFT JOIN {{ ref('stg_stripe_transfers') }} transfers ON charges.transfer_id = transfers.transfer_id
LEFT JOIN {{ ref('stg_stripe_connected_accounts') }} connected_accounts ON connected_accounts.connected_account_id = charges.destination_id
LEFT JOIN {{ ref('stg_fanfix_subscription_subscriptions') }} subs ON subs.subscription_id = invs.subscription_id
LEFT JOIN {{ ref('stg_fanfix_subscription_subscription_prices') }} prices ON subs.subscription_price_id = prices.subscription_price_id
LEFT JOIN {{ ref('stg_fanfix_user_profiles') }} user ON subs.creator_id = user.user_id
WHERE 1=1
--AND inv_payments.status in ('Succeeded', 'Refunded')
AND charges.paid = TRUE
AND payments.payment_processor_transaction_id NOT IN (
    SELECT payment_intent_id
    FROM {{ ref('fact_subscription_stripe') }}
    )