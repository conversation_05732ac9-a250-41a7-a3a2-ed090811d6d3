with tmp as (
select 
    p.fanfix_id, 
    p.fanfix_username,
    date_trunc(month, c.time) as month,
    sum(net_revenue) as monthly_earning
from {{ref("dim_creator_profile")}} p
join {{ref("fact_creator_charges")}} c
on p.fanfix_id = c.creator_fanfix_id
where (p.country is null or p.country <> 'MENA')  -- don't want to look at MENA creators for this request
    and last_activity_date >= '2025-09-01'  -- want to make sure they're active at least a month before the data was pulled
    and date_trunc(month, c.time) >= '2025-04-01' -- want to rank by 6 months total earning
    and date_trunc(month, c.time) < '2025-10-01'
group by 1, 2, 3
)
select
    fanfix_id, 
    fanfix_username,
    'creator' || row_number() over (order by sum(monthly_earning) desc) as masked_username,
    round(avg(monthly_earning),2) as average_monthly_earning, 
    sum(monthly_earning) as total_6month_earning
from tmp
group by 1, 2
order by total_6month_earning desc
limit 150