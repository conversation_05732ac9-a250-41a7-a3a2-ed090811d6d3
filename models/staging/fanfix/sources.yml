version: 2

sources:
  - name: fanfix
    description: Core Fanfix application database containing user profiles, content, and platform interactions
    database: "{{ env_var('DBT_FANFIX_DATABASE', var('fanfix_database')) }}"
    schema: fanfix

    config: 
      freshness: 
        warn_after: {count: 12, period: hour}
        error_after: {count: 24, period: hour}
      loaded_at_field: updated_at

    tables:
      - name: user_profiles
        description: User profile information including bio, settings, and account details

      - name: creator_profiles
        description: Creator-specific profile data including monetization settings and creator tools

      - name: fan_lists
        description: Fan list memberships and follower relationships between users and creators

      - name: posts
        description: Content posts created by users including text, media, and engagement metadata

      - name: premium_post_unlocks
        description: post unlock transactions recording the payment details fans pay to access locked content including the original post, timestamps, and tip amounts

      - name: post_assets
        description: Media assets (images, videos, files) associated with posts

      - name: assets
        description: Media assets (images, videos, files) themselves associated with posts
        
      - name: social_accounts
        description: Social media accounts linked to user profiles for cross-platform engagement

      - name: greeting_messages
        description: Greeting messages (present and past)

      - name: creator_follows
        description: Follow relationships between users and creators

      - name: creator_blocks
        description: Blocked users by creators