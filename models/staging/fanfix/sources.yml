version: 2

sources:
  - name: fanfix
    description: Core Fanfix application database containing user profiles, content, and platform interactions
    database: "{{ env_var('DBT_FANFIX_DATABASE', var('fanfix_database')) }}"
    schema: fanfix
    tables:
      - name: user_profiles
        description: User profile information including bio, settings, and account details

      - name: creator_profiles
        description: Creator-specific profile data including monetization settings and creator tools

      - name: fan_lists
        description: Fan list memberships and follower relationships between users and creators

      - name: posts
        description: Content posts created by users including text, media, and engagement metadata

      - name: post_assets
        description: Media assets (images, videos, files) associated with posts
        
      - name: social_accounts
        description: Social media accounts linked to user profiles for cross-platform engagement

      - name: greeting_messages
        description: Greeting messages (present and past)
