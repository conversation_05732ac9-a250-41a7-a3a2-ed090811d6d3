with source as (
    select * from {{ source('fanfix', 'posts') }}
),
renamed as (
    select
        ID as post_id,
        creator_id as creator_id,
        TITLE as title,
        STATUS as status,
        IS_EXCLUSIVE as is_exclusive,
        IS_PINNED as is_pinned,
        IS_FIRST_ASSET_PUBLIC as is_first_asset_public,
        SUBSCRIBER_PRICE as subscriber_price, 
        NON_SUBSCRIBER_PRICE as non_subscriber_price,
        COMMENT_COUNT as comment_count,
        LIKE_COUNT as like_count,
        VIEW_COUNT as view_count,
        CREATED_AT as created_at,
        UPDATED_AT as updated_at,
        SCHEDULED_FOR as scheduled_for
    from source
)
select * from renamed
