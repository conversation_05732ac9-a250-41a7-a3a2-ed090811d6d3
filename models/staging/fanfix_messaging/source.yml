version: 2

sources:
  - name: fanfix_messaging
    description: All the data related to messaging and message blast
    database: "{{ env_var('DBT_FANFIX_DATABASE', var('fanfix_database')) }}"
    schema: fanfix_messaging
        
    config: 
      freshness: 
        warn_after: {count: 12, period: hour}
        error_after: {count: 24, period: hour}
      loaded_at_field: updated_at

    tables:
      - name: messages
        description: All message records
        columns:
          - name: created_at
            description: timestamp when the message was created
          - name: sender_user_id
            description: user id of the message sender
          - name: updated_at
            description: timestamp when the message was updated
          - name: content
            description: content of the message
          - name: MESSAGE_CHANNEL_ID
            description: id of the message channel (conversation)
          - name: id
            description: Message id
          - name: UNLOCK_PRICE_IN_CENTS
            description: Price to unlock this message
          - name: IS_PREVIEW_ENABLED BOOLEAN
            description: Whether or not the message (media) has enabled preview
          - name: RECIPIENT_USER_ID
            description: id of the message recipient
          - name: synced_at
            description: timestamp when the message was synced
          - name: expires_at
            description: timestamp when the message was expired
          - name: payment_id
            description: Id of the related payment
          - name: unlocked_at
            description: timestamp when the message was unlocked
          - name: payment_amount
            description: the payment amount related to this message
          - name: message_blast_id
            description: if the message is a message blast, this is the id
          - name: locked_text
            description: locked text


      - name: message_blasts
        description: All message blast records
        columns:
          - name: id
            description: Unique identifier of the message blast
          - name: text
            description: Text in the message blast
          - name: created_at
            description: Time when the message blast was created
          - name: sent_user_count
            description: Number of users the message blast was sent to
          - name: status
            description: Current status of the message blast
          - name: creator_id
            description: ID of the user who created the message blast
          - name: updated_at
            description: Time when the message blast was last updated
          - name: unlock_price_in_cents
            description: Unlock price for the message blast in cents
          - name: is_preview_enabled
            description: Whether preview mode is enabled for the message blast
          - name: target_channel_ids
            description: Target channel IDs for the message blast
          - name: unlock_count
            description: Number of times the message blast was unlocked
          - name: unlock_revenue_in_cents
            description: Total unlock revenue in cents for the message blast
          - name: parent_id
            description: ID of the parent message blast, if applicable
          - name: deleted_at
            description: Time when the message blast was deleted
          - name: expires_at
            description: Time when the message blast will expire
          - name: scheduled_at
            description: Time when the message blast is scheduled to be sent
          - name: text_fallbacks
            description: Fallback text versions of the message blast
          - name: audience_list_selection
            description: Audience list selection criteria for the message blast
          - name: unsend_on_expiration
            description: Whether the message blast is unsent upon expiration
          - name: locked_text
            description: Locked text content of the message blast
          - name: deleted_by
            description: ID of the user who deleted the message blast
          - name: exclude_fan_replies_within_seconds
            description: Exclusion window in seconds for fan replies

      - name: message_blast_assets
        description: All message blast asset records