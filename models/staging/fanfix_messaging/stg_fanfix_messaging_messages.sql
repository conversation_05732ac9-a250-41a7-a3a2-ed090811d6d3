with source as (
    select * from {{ source('fanfix_messaging', 'messages') }}
)
select 	
	ID as message_id,
	CREATED_AT,
	SENDER_USER_ID,
	UPDATED_AT,
	CONTENT,
	MESSAGE_CHANNEL_ID,
	UNLOCK_PRICE_IN_CENTS,
	IS_PREVIEW_ENABLED,
	REC<PERSON>IE<PERSON>_USER_ID,
	<PERSON><PERSON>OVED,
	S<PERSON><PERSON>ED_AT,
	<PERSON><PERSON><PERSON><PERSON><PERSON>_AT,
	PAY<PERSON>NT_ID,
	SE<PERSON><PERSON>RD_ID,
	UNLOCKED_AT,
	PAYMENT_AMOUNT,
	MESSAGE_BLAST_ID,
	LOCKED_TEXT
from source