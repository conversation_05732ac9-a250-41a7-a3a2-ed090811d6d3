with source as (
    select * from {{ source('fanfix_messaging', 'messages') }}
)
select 	CREATED_AT,
	SENDER_USER_ID,
	UPDATED_AT,
	CONTENT,
	MESSAGE_CHANNEL_ID,
	ID,
	UNLOCK_PRICE_IN_CENTS,
	IS_PREVIEW_ENABLED,
	RECIPIENT_USER_ID,
	<PERSON><PERSON>OVED,
	<PERSON>Y<PERSON>ED_AT,
	EX<PERSON>RE<PERSON>_AT,
	<PERSON>Y<PERSON>NT_ID,
	SE<PERSON><PERSON><PERSON>_ID,
	UNLOCKED_AT,
	PAYMENT_AMOUNT,
	MESSAGE_BLAST_ID,
	LOCKED_TEXT
from source