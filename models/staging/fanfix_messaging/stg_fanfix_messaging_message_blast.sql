with source as (
    select * from {{ source('fanfix_messaging', 'message_blasts') }}
)
select ID,
	TEXT,
	CREATED_AT,
	SENT_USER_COUNT,
	STATUS,
	CREATOR_ID,
	UPDATED_AT,
	UNLOCK_PRICE_IN_CENTS,
	IS_PREVIEW_ENABLED,
	TARGET_CHANNEL_IDS,
	UNLOCK_COUNT,
	UNLOCK_REVENUE_IN_CENTS,
	PARENT_ID,
	DELETED_AT,
	EXPIRES_AT,
	SCHEDULED_AT,
	TEXT_FALLBACKS,
	AUDIENCE_LIST_SELECTION,
	UNSEND_ON_EXPIRATION,
	LOCKED_TEXT,
	DELETED_BY,
	EXCLUDE_FAN_REPLIES_WITHIN_SECONDS
from source