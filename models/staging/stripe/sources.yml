version: 2

sources:
  - name: stripe
    description: Stripe payment processing data including charges, customers, invoices, and account information
    database: "{{ var('stripe_database') }}"
    schema: "{{ env_var('DBT_STRIPE_SCHEMA', var('stripe_schema')) }}"
    
    config:
      freshness: 
        warn_after: {count: 12, period: hour}
        error_after: {count: 24, period: hour}

    tables:
      - name: application_fees
        description: Fees collected by the platform from connected accounts in marketplace transactions

      - name: charges
        description: Stripe charge records representing payment attempts and their outcomes
        config:
          loaded_at_field: batch_timestamp

      - name: connected_account_balance_transactions
        description: Balance transactions for Stripe Connect accounts
        config:
          loaded_at_field: batch_timestamp

      - name: connected_account_charges
        description: Charges processed through Stripe Connect accounts
        config:
          loaded_at_field: batch_timestamp

      - name: connected_account_refunds
        description: Refunds processed through Stripe Connect accounts
        config:
          freshness: 
            warn_after: {count: 36, period: hour}
            error_after: {count: 72, period: hour}
          loaded_at_field: batch_timestamp

      - name: connected_accounts
        description: Stripe Connect accounts linked to the platform for marketplace functionality
        config:
          loaded_at_field: batch_timestamp

      - name: connected_accounts_metadata
        description: Additional metadata and custom fields for Stripe Connect accounts
        config:
          loaded_at_field: batch_timestamp

      - name: customers
        description: Stripe customer records containing billing and contact information
        config:
          loaded_at_field: batch_timestamp

      - name: customers_metadata
        description: Additional metadata and custom fields for Stripe customers
        config:
          loaded_at_field: batch_timestamp
      - name: disputes
        description: Chargeback and dispute records for contested payments
        config:
          loaded_at_field: batch_timestamp

      - name: financial_report_connect_payouts_itemized
        description: Itemized payouts report for Stripe Connect accounts

      - name: invoice_line_items
        description: Individual line items and charges within Stripe invoices
        config:
          loaded_at_field: batch_timestamp
      - name: invoices
        description: Stripe invoices for subscription billing and one-time payments
        config:
          loaded_at_field: batch_timestamp

      - name: payment_intents_metadata
        description: Additional metadata for Stripe Payment Intents including custom fields
        config:
          loaded_at_field: batch_timestamp
      - name: payment_methods
        description: Payment methods stored in Stripe for customer billing and transactions
        config:
          loaded_at_field: batch_timestamp

      - name: prices
        description: Stripe pricing information for products and subscription plans

      - name: products
        description: Stripe product catalog including subscription plans and one-time purchase items

      - name: refunds
        description: Refund transactions for previously successful charges
        config:
          freshness: 
            warn_after: {count: 36, period: hour}
            error_after: {count: 72, period: hour}
          loaded_at_field: batch_timestamp

      - name: subscription_items
        description: Individual items within Stripe subscriptions linking products and quantities

      - name: subscriptions
        description: Stripe subscription records for recurring billing relationships

      - name: subscriptions_metadata
        description: Additional metadata and custom fields for Stripe subscriptions

      - name: transfers
        description: Money transfers between Stripe accounts and bank accounts
        config:
          loaded_at_field: batch_timestamp

      - name: transfer_reversals
        description: Place holder
        config:
          freshness: 
            warn_after: {count: 36, period: hour}
            error_after: {count: 72, period: hour}
          loaded_at_field: batch_timestamp
          
