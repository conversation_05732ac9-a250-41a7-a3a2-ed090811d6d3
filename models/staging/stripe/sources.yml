version: 2

sources:
  - name: stripe
    description: Stripe payment processing data including charges, customers, invoices, and account information
    database: "{{ var('stripe_database') }}"
    schema: "{{ env_var('DBT_STRIPE_SCHEMA', var('stripe_schema')) }}"
    tables:
      - name: application_fees
        description: Fees collected by the platform from connected accounts in marketplace transactions

      - name: charges
        description: Stripe charge records representing payment attempts and their outcomes

      - name: connected_account_balance_transactions
        description: Balance transactions for Stripe Connect accounts

      - name: connected_account_charges
        description: Charges processed through Stripe Connect accounts

      - name: connected_account_refunds
        description: Refunds processed through Stripe Connect accounts

      - name: connected_accounts
        description: Stripe Connect accounts linked to the platform for marketplace functionality

      - name: connected_accounts_metadata
        description: Additional metadata and custom fields for Stripe Connect accounts

      - name: customers
        description: Stripe customer records containing billing and contact information

      - name: customers_metadata
        description: Additional metadata and custom fields for Stripe customers

      - name: disputes
        description: Chargeback and dispute records for contested payments

      - name: financial_report_connect_payouts_itemized
        description: Itemized payouts report for Stripe Connect accounts

      - name: invoice_line_items
        description: Individual line items and charges within Stripe invoices

      - name: invoices
        description: Stripe invoices for subscription billing and one-time payments

      - name: payment_intents_metadata
        description: Additional metadata for Stripe Payment Intents including custom fields

      - name: payment_methods
        description: Payment methods stored in Stripe for customer billing and transactions

      - name: prices
        description: Stripe pricing information for products and subscription plans

      - name: products
        description: Stripe product catalog including subscription plans and one-time purchase items

      - name: refunds
        description: Refund transactions for previously successful charges

      - name: subscription_items
        description: Individual items within Stripe subscriptions linking products and quantities

      - name: subscriptions
        description: Stripe subscription records for recurring billing relationships

      - name: subscriptions_metadata
        description: Additional metadata and custom fields for Stripe subscriptions

      - name: transfers
        description: Money transfers between Stripe accounts and bank accounts

      - name: transfer_reversals
        description: Place holder