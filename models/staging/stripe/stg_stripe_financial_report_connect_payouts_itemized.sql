{{ config(
    pre_hook="SET (TIMEZONE, START_DATE, END_DATE) = ('{{ var('TIMEZONE') }}', '{{ var('START_DATE') }}', '{{ var('END_DATE') }}')"
) }}

with source as (
    select * from {{ source('stripe', 'financial_report_connect_payouts_itemized') }}
),
renamed as (
    select
        PAYOUT_ID as payout_id, 
        BALANCE_TRANSACTION_ID as balance_transaction_id, 
        PAYOUT_DESTINATION_ID as payout_destination_id,
        CONNECTED_ACCOUNT as connected_account, 
        PAYOUT_STATUS as payout_status, 
        PAYOUT_TYPE as payout_type, 
        EFFECTIVE_AT_UTC as effective_at_utc, 
        EFFECTIVE_AT as effective_at, 
        CURRENCY as currency, 
        GROSS as gross, 
        FEE as fee, 
        NET as net, 
        REPORTING_CATEGORY as reporting_category, 
        DESCRIPTION as description, 
        PAYOUT_EXPECTED_ARRIVAL_DATE as payout_expected_arrival_date, 
        PAYOUT_REVERSED_AT_UTC as payout_reversed_at_utc, 
        PAYOUT_REVERSED_AT as payout_reversed_at, 
        PAYOUT_DESCRIPTION as payout_description
    from source
)
select * from renamed