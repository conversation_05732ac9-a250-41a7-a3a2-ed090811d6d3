with source as (
    select * from {{ source('stripe', 'refunds') }}
),
renamed as (
    select
        id as refund_id,
        merchant_id,
        balance_transaction_id,
        source_transfer_reversal_id,
        refund_transfer_reversal_id,
        refund_payment_intent,
        charge_id,
        amount,
        currency,
        failure_reason,
        reason,
        receipt_number,
        status,
        created as created_at,
        BATCH_TIMESTAMP as batch_timestamp 
    from source
)
select * from renamed
