with source as (
    select * from {{ source('stripe', 'connected_account_balance_transactions') }}
),
renamed as (
    select
        ID as balance_transaction_id,
        AUTOMATIC_TRANSFER_ID as automatic_transfer_id,
        ACCOUNT as account,
        MERCHANT_ID as merchant_id,
        SOURCE_ID as source_id,
        BALANCE_TYPE as balance_type,
        REPORTING_CATEGORY as reporting_category,
        AVAILABLE_ON as available_on,
        ROUND(AMOUNT/100,2) AS total_with_tax_and_processing_fee,
        ROUND(NET * 1.25/ 100, 2) AS total,
        ROUND(NET/100, 2) AS net,
        ROUND(FEE/100, 2) as fee,
        STATUS as status,
        CURRENCY as currency,
        DESCRIPTION as description,
        TYPE as type,
        CREATED as created_at,
        BATCH_TIMESTAMP as batch_timestamp
    from source
)
select * from renamed
