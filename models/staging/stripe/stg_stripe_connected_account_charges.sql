with source as (
    select * from {{ source('stripe', 'connected_account_charges') }}
),
renamed as (
    select
        ID as connected_account_charge_id,
        CUSTOMER_ID as customer_id,
        PAYMENT_METHOD_ID AS payment_method_id,
        CARD_ID as card_id,
        SOURCE_ID as source_id,
        BALANCE_TRANSACTION_ID as balance_transaction_id,
        DESTINATION_ID as destination_id,
        TRANSFER_ID as transfer_id,
        MERCHANT_ID as merchant_id,
        DISPUTE_ID as dispute_id,
        STATUS as status,
        PAID as paid,
        AMOUNT as charge_amount,
        REFUNDED as refunded,
        AMOUNT_REFUNDED as amount_refunded,
        PAYMENT_METHOD_TYPE as payment_method_type,
        DESC<PERSON>P<PERSON>ON as description,
        CAPTURED_AT as captured_at,
        CREATED as created_at
    from source
)
select * from renamed
