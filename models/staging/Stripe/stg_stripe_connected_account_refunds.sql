with source as (
    select * from {{ source('stripe', 'connected_account_refunds') }}
),
renamed as (
    select
        ID as connected_account_refund_id,
        REFUND_TRANSFER_REVERSAL_ID as refund_transfer_reversal_id,
        SOURCE_TRANSFER_REVERSAL_ID as source_transfer_reversal_id,
        BALANCE_TRANSACTION_ID as balance_transaction_id,
        MERCHANT_ID as merchant_id,
        STATUS as status,
        AMOUNT as refund_amount,
        REASON as reason,
        REFUND_DESCRIPTION as refund_description,
        CREATED as created_at
    from source
)
select * from renamed
