with source as (
    select * from {{source('sendbird', 'fanfix_messaging_messages')}}
),
renamed as (
    select 	
        _SOURCE_ID,
	    _TIMESTAMP,
	    APPROVED,
	    CHANNEL_URL,
	    CREATED_AT,
	    DELETED,
	    EVENTN_CTX_EVENT_ID,
	    ID as message_id,
	    IS_READ,
	    RECIPIENT_TYPE,
	    RECIPIENT_USER_ID,
	    SENDER_TYPE,
	    SENDER_USER_ID,
        SRC,
        TIP_AMOUNT,
        TIP_ID,
        BLAST_ID,
        UPDATED_AT
    from source
)
select * from renamed