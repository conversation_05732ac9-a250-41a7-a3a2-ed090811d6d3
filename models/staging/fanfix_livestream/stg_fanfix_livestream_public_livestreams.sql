with source as (
    select * from {{ source('fanfix_livestream', 'public_livestreams') }}
)
select 	
    ALLOWS_COMMENTS,
	ALLOWS_TIPS,
	AUDIENCE_TYPE,
	CREATED_AT,
	CREATOR_ID,
	ENDED_AT,
	ID as livestream_id,
	MINIMUM_COMMENT_TIP_AMOUNT_IN_CENTS,
	ROOM_ID,
	SESSION_ID,
	STARTED_AT,
	TITLE,
	TOTAL_TIP_AMOUNT_IN_CENTS,
	UPDATED_AT,
	DELETED_AT,
	SUBSCRIBER_PRICE_IN_CENTS,
	NON_SUBSCRIBER_PRICE_IN_CENTS,
	PRICING,
	DEFAULT_PRICE_IN_CENTS,
	LIVESTREAM_TYPE,
	HAS_VIDEO,
	PRICING_MODEL_TYPE,
	TARGET_FAN_ID,
	PINNED_COMMENT_ID,
from source