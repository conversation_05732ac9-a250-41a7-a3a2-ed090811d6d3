version: 2

sources:
  - name: fanfix_livestream
    description: All the data related to fanfix livestreams and livestream activities
    database: "{{ env_var('DBT_FANFIX_DATABASE', var('fanfix_database')) }}"
    schema: fanfix_livestream
    
    config: 
      freshness: 
        warn_after: {count: 12, period: hour}
        error_after: {count: 24, period: hour}
      loaded_at_field: updated_at
    
    tables:
      - name: livestreams
        description: all livestream records 

      - name: livestream_activities
        description: all livestream activity records
