with source as (
    select * from {{ source('fanfix_livestream', 'livestreams') }}
)
select 	
    ALLOWS_COMMENTS,
	ALLOWS_TIPS,
	CREATED_AT,
	CREATOR_ID,
	ENDED_AT,
	ID as livestream_id,
	MINIMUM_COMMENT_TIP_AMOUNT_IN_CENTS,
	R<PERSON><PERSON>_ID,
	SESSION_ID,
	STARTED_AT,
	TITLE,
	TOTAL_TIP_AMOUNT_IN_CENTS,
	UPDATED_AT,
	DELETED_AT,
	PRICING,
	DEFAULT_PRICE_IN_CENTS,
	LIVESTREAM_TYPE,
	HAS_VIDEO,
	PRICING_MODEL_TYPE,
	TARGET_FAN_ID,
	PINNED_COMMENT_ID,
    TIP_GOAL,
    STATUS
from source