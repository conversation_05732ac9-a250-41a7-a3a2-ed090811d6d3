with source as (
    select * from {{ source('posthog', 'custom_fact_events_from_promotion_links') }}
    union all
    select * from {{ source('posthog', 'custom_fact_events_from_promotion_links') }}
),
renamed as (
    select
    uuid,
    timestamp,
    event,
    session_id,
    session_entry_url,
    session_entry_utm_medium,
    session_entry_utm_campaign,
    session_entry_referrer_domain,
    creator_id,
    distinct_id,
    user_id,
    person_id,
    current_url,
    utm_campaign,
    utm_content,
    utm_medium,
    utm_source
    from source
)
select * from renamed
