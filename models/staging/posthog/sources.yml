version: 2

sources:
  - name: posthog
    description: PostHog analytics and user behavior tracking data including events, sessions, and user profiles
    database: "{{ env_var('DBT_FANFIX_DATABASE', var('fanfix_database')) }}"
    schema: posthog
    tables:
      - name: persons
        identifier: '"persons"'
        description: User profiles and person records tracked by PostHog analytics      
      - name: custom_daily_creator_pageview
        description: creator pageview stats by day including top referring domain, top country, and unique visitors
      - name: custom_daily_creator_pageview_backfill
        description: creator pageview stats (backfill between 2025-10-29 to 2025-11-13 based on 2025-11-14, 2025-10-28 average)

      - name: custom_daily_creator_profile_traffic
        description: creator pageview stats

      - name: custom_daily_creator_profile_traffic_backfill
        description: creator pageview stats (backfill between 2025-10-29 to 2025-11-13 based on 2025-11-14, 2025-10-28 average)
      
      - name: custom_monthly_web_metrics
        description: web traffic metrics used for investor model

      - name: custom_daily_creator_subscription_flow_conversion
        description: daily subscriber conversion stats on creator profile