with source as (
    select * from {{ source('posthog', 'custom_daily_creator_pageview') }}
    union all
    select * from {{ source('posthog', 'custom_daily_creator_pageview_backfill') }}
),
renamed as (
    select
        daily::date as daily,
        creator_username,
        top_standardized_referring_domain,
        total_pageview,
        top_country,
        unique_visitors
    from source
)
select * from renamed
