with source as (
    select * from {{ source('fanfix_subscription', 'promotions') }}
),
renamed as (
    select
        ID as promotion_id,
        CODE as code,
        TYPE as type,
        CREATOR_ID as creator_id,
        USES_COUNT as uses_count,
        IS_ARCHIVED as is_archived,
        RESTRICTED_TO  as restricted_to,
        MAX_USES_COUNT as max_uses_count,
        DISCOUNT_PERCENTAGE as discount_percentage,
        SUBSCRIPTION_PLAN_ID as subscription_plan_id,
        TRIAL_DURATION_IN_DAYS  as trial_duration_in_days,
        DISCOUNT_DURATION_IN_MONTHS as discount_duration_in_months,
        VALID_UNTIL as valid_until,
        VALID_UNTIL::date + INTERVAL '1 day' as expires_at,
        CREATED_AT as created_at,
        UPDATED_AT as updated_at
    from source
)
select * from renamed