with source as (
    select * from {{ source('fanfix_subscription', 'invoices') }}
),
renamed as (
    select
        ID as invoice_id,
        SUBSCRIPTION_ID as subscription_id,
        COALESCE(ROUND(PRICE_IN_CENTS / 100, 2), 0) as price,
        COALESCE(ROUND(TAX_AMOUNT_IN_CENTS / 100, 2), 0) as tax_amount,
        COALESCE(ROUND(FANFIX_FEE_AMOUNT_IN_CENTS / 100, 2), 0) as fanfix_fee_amount,
        COALESCE(ROUND(PROCESSING_FEE_AMOUNT_IN_CENTS / 100, 2), 0) as processing_fee_amount,
        COALESCE(ROUND(TOTAL_AMOUNT_IN_CENTS / 100, 2), 0) as total_amount,
        STATUS as status,
        IS_INITIAL as is_initial,
        CREATED_AT as created_at,
        UPDATED_AT as updated_at
    from source
)
select * from renamed
