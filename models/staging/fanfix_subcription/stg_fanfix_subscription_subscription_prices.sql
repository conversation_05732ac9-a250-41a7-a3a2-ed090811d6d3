with source as (
    select * from {{ source('fanfix_subscription', 'subscription_prices') }}
),
renamed as (
    select
        ID as subscription_price_id,
        SUBSCRIPTION_PLAN_ID as subscription_plan_id,
        PRICE_IN_CENTS as price_in_cents,
        CAST((PRICE_IN_CENTS/100) AS DECIMAL(10, 2)) AS price,
        DURATION_IN_MONTHS as duration_in_months,
        DURATION_IN_MONTHS * 30 AS duration_in_days,
        IS_ACTIVE as is_active,
        CREATED_AT as created_at,
        UPDATED_AT as updated_at
    from source
)
select * from renamed
