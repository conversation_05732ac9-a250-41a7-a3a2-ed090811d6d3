version: 2

sources:
  - name: fanfix_payment
    description: Payment processing database containing transaction records, payment methods, and financial data
    database: "{{ env_var('DBT_FANFIX_DATABASE', var('fanfix_database')) }}"
    schema: fanfix_payment
    
    config: 
      freshness: 
        warn_after: {count: 12, period: hour}
        error_after: {count: 24, period: hour}
      loaded_at_field: updated_at
    

    tables:
      - name: public_payments
        description: Payment transaction records including amounts, status, and payment processing details
        columns:
          - name: ID
            description: Unique identifier for the payment transaction
          - name: FAN_ID
            description: Identifier for the fan making the payment
          - name: CREATOR_ID
            description: Identifier for the creator receiving the payment
          - name: AMOUNT_IN_CENTS
            description: Payment amount in cents
          - name: PAYMENT_PROCESSOR
            description: Payment processor used (e.g., Stripe, Stub)
          - name: CREATED_AT
            description: Timestamp when the payment was created
          - name: UPDATED_AT
            description: Timestamp when the payment was last updated

      - name: public_cards
        description: Stored payment card information and payment method details for users
        columns:
          - name: ID
            description: Unique identifier for the card record
          - name: FAN_ID
            description: Identifier for the fan who owns the card
          - name: CARD_NUMBER_TOKEN
            description: Tokenized card number for security
          - name: STRIPE_CARD_ID
            description: Stripe payment method identifier
          - name: LAST_FOUR
            description: Last four digits of the card number
          - name: CREATED_AT
            description: Timestamp when the card was added
          - name: UPDATED_AT
            description: Timestamp when the card was last updated
          - name: DELETED_AT
            description: Timestamp when the card was deleted (if applicable)
  
