with source as (
    select * from {{ source('fanfix_payment', 'public_cards') }}
),
renamed as (
    select
        ID as card_id,
        FAN_ID as fan_id,
        CASE
            WHEN card_type is null THEN 'regular'
            ELSE card_type
        END AS card_type,
        CARD_NUMBER_TOKEN as card_number_token,
        STRIPE_CARD_ID as stripe_card_id,
        LAST_FOUR as last_four_digits,
        CREATED_AT as created_at,
        UPDATED_AT as updated_at,
        DELETED_AT as deleted_at
    from source
)
select * from renamed
