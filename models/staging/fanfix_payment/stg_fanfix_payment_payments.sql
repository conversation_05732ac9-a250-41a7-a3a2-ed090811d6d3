with source as (
    select * from {{ source('fanfix_payment', 'payments') }}
),
renamed as (
    select
        ID as payment_id,
        PAYMENT_PROCESSOR_TRANSACTION_ID as payment_processor_transaction_id,
        FAN_ID as fan_id,
        CREATOR_ID as creator_id,
        CARD_ID as card_id,
        COALESCE(ROUND(AMOUNT_IN_CENTS / 100, 2), 0) as amount,
        COALESCE(ROUND(SALES_TAX_AMOUNT_IN_CENTS / 100, 2), 0) as sales_tax_amount,
        COALESCE(ROUND(PROCESSING_FEE_AMOUNT_IN_CENTS / 100, 2), 0) as processing_fee,
        COALESCE(ROUND(APPLICATION_FEE_AMOUNT_IN_CENTS / 100, 2), 0) AS application_fee,
        TIP_TYPE as tip_type,
        PAYMENT_PROCESSOR as payment_processor,
        related_entity_id,
        SALES_TAX_BILLING_LOCATION as sales_tax_billing_location,
        UPPER(REPLACE(try_parse_json(SALES_TAX_BILLING_LOCATION):countryCode, '"','')) AS sales_tax_countryCode,
        UPPER(REPLACE(try_parse_json(SALES_TAX_BILLING_LOCATION):region, '"','')) AS sales_tax_region,
        REPLACE(try_parse_json(SALES_TAX_BILLING_LOCATION):zip, '"','') AS sales_tax_zip,
        CREATED_AT as created_at,
        UPDATED_AT as updated_at
    from source
)
select * from renamed
