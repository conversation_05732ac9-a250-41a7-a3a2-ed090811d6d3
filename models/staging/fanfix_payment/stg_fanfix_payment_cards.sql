with source as (
    select * from {{ source('fanfix_payment', 'cards') }}
),
renamed as (
    select
        ID as card_id,
        FAN_ID as fan_id,
        CARD_TYPE as card_type,
        CARD_NUMBER_TOKEN as card_number_token,
        STRIPE_CARD_ID as stripe_card_id,
        LAST_FOUR as last_four_digits,
        CARD_EXPIRATION_DATE as card_expiration_date,
        CARDHOLDER_NAME AS cardholder_name,
        BILLING_ZIP_CODE as billing_zip_code,
        CARD_BRAND as card_brand,
        STATUS as status,
        BILLING_CITY as billing_city,
	    BILLING_LINE1 as billing_line1,
	    BILLING_LINE2 as billing_line2,
	    BILLING_STATE as billing_state,
	    BILLING_COUNTRY as billing_country,
        CREATED_AT as created_at,
        UPDATED_AT as updated_at,
        DELETED_AT as deleted_at
    from source
)
select * from renamed
