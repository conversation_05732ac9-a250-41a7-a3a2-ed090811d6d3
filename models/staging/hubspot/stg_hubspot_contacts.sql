with source as (
    select * from {{source('hubspot', 'contacts')}}
),
renamed as (
    select 
        id as id,
        properties_fanfix_username AS fanfix_username,
        properties_firstname AS firstname,
        properties_lastname AS lastname,
        properties_fanfix_id AS fanfix_id,
        properties_email as email,
        properties_phone AS phone,
        source."createdAt"::date AS create_date,
        properties_closedate::date AS close_date,
        properties_country as country,
        
        CASE
            WHEN properties_lead_source = '<nil>' THEN ''
            ELSE properties_lead_source
        END AS lead_source 
        , CASE
            WHEN properties_cs_managed_ = '<nil>' THEN ''
            ELSE properties_cs_managed_
        END AS cs_managed
        , CASE
            WHEN properties_vertical = '<nil>' THEN ''
            ELSE properties_vertical
        END AS vertical
        , CASE
            WHEN properties_deal_creator = '<nil>' THEN ''
            ELSE properties_deal_creator
        END AS deal_creator_id,
        properties_associatedcompanyid AS agent_id,
        properties_account_management_agency AS account_management_agency,
        properties_n3rd_party_management AS n3rd_party_management,
        CASE
            WHEN properties_n3rd_party_management_date = '<nil>' THEN ''
            ELSE properties_n3rd_party_management_date
        END as agency_managed_date,
        properties_churn_reason AS churn_reason,
        properties_churn_reason___competitive_platform AS churn_reason_competitive_platform,
        PROPERTIES_HS_MERGED_OBJECT_IDS as HS_MERGED_OBJECT_IDS,

        properties_hubspot_owner_id as hubspot_owner_id,
        properties_prospected_by as prospected_by,

        -- referral info
        properties_amount AS amount,
        properties_contract_term_length AS contract_term_length,
        properties_contract_term_length__referral_ AS contract_term_length_referral,
        properties_referrer_name AS referrer_name,
        properties_referral_start_date::date AS referral_start_date,
        properties_referral_end_date::date AS referral_end_date,
        properties_referral_fee AS referral_fee,

        -- mg info
        properties_minimum_guarantee AS minimum_guarantee,
        properties_minimum_guarantee__monthly_ AS minimum_guarantee_monthly,
        properties_minimum_guarantee_start_date AS minimum_guarantee_start_date,
        properties_minimum_guarantee_end_date AS minimum_guarantee_end_date,
        properties_minimum_guarantee_cancellation_date AS minimum_guarantee_cancellation_date,
        properties_active_mg_ AS active_mg,
        properties_deliverables___fanfix_posts AS fanfix_posts,
        properties_deliverables___message_blasts AS message_blasts,

        -- relaunch info
        properties_relaunch AS relaunch,
        properties_relaunch_date AS relaunch_date,
        properties_relaunch_manager AS relaunch_manager,

        properties_last_cs_check_in::date AS last_cs_check_in,

        -- related accounts
        properties_stripe_link AS stripe_link,
        properties_trackable_link_owner AS trackable_link_owner,
        properties_hubspot_link AS hubspot_link,
        properties_instagram_handle AS instagram_handle,
        properties_tiktok_handle AS tiktok_handle,

        -- modash info
        properties_age_group_modash AS age_group_modash,
        properties_audience_gender_female_modash AS audience_gender_female_modash,
        properties_audience_gender_male_modash AS audience_gender_male_modash,
        properties_audience_location_one_modash AS audience_location_one_modash,
        properties_audience_location_two_modash AS audience_location_two_modash,
        properties_audience_location_three_modash AS audience_location_three_modash,
        properties_audience_location_four_modash AS audience_location_four_modash,
        properties_audience_location_percentage_one_modash AS audience_location_percentage_one_modash,
        properties_audience_location_percentage_two_modash AS audience_location_percentage_two_modash,
        properties_audience_location_percentage_three_modash AS audience_location_percentage_three_modash,
        properties_audience_location_percentage_four_modash AS audience_location_percentage_four_modash,
        properties_audience_type_influencers_modash AS audience_type_influencers_modash,
        properties_audience_type_mass_followers_modash AS audience_type_mass_followers_modash,
        properties_audience_type_real_modash AS audience_type_real_modash,
        properties_audience_type_suspicious_modash AS audience_type_suspicious_modash,
        properties_average_likes_per_post_modash AS average_likes_per_post_modash,
        properties_country_modash AS country_modash,
        properties_gender_modash AS gender_modash,
        PROPERTIES_INSTAGRAM_FOLLOWERS AS instagram_followers_modash,
        properties_instagram_engagement_rate_modash AS instagram_engagement_rate_modash
    from source
)
select * from renamed