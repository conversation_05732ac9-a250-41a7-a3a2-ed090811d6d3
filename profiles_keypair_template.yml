default:
  outputs:
    default:
      account: yq69231.us-east-2.aws
      database: dbt_analytics
      private_key_path: "/Users/<USER>/Documents/FanfixProjects/snowflake_keys/jlin/jlin_rsa.p8"
      private_key_passphrase: "jhj8uyc7meb3YAT_zyc"
      role: dbt_transformer
      schema: dev_jlin
      threads: 1
      type: snowflake
      user: jlin
      warehouse: dbt_wh
  target: default

dbt_develop:
  outputs:
    dev:
      account: yq69231.us-east-2.aws
      database: dbt_analytics
      private_key_path: "/Users/<USER>/Documents/FanfixProjects/snowflake_keys/jlin/jlin_rsa.p8"
      private_key_passphrase: "jhj8uyc7meb3YAT_zyc"
      role: dbt_transformer
      schema: dev_jlin
      threads: 1
      type: snowflake
      user: jlin
      warehouse: dbt_wh
  target: dev

dbt_cloud:
  outputs:
    dev:
      account: yq69231.us-east-2.aws
      database: dbt_analytics
      private_key_path: "/Users/<USER>/Documents/FanfixProjects/snowflake_keys/jlin/jlin_rsa.p8"
      private_key_passphrase: "jhj8uyc7meb3YAT_zyc"
      role: dbt_transformer
      schema: dev_jlin
      threads: 1
      type: snowflake
      user: jlin
      warehouse: dbt_wh
    default:
      account: yq69231.us-east-2.aws
      database: dbt_analytics
      private_key_path: "/Users/<USER>/Documents/FanfixProjects/snowflake_keys/jlin/jlin_rsa.p8"
      private_key_passphrase: "jhj8uyc7meb3YAT_zyc"
      role: dbt_transformer
      schema: dev
      threads: 1
      type: snowflake
      user: jlin
      warehouse: dbt_wh
  target: default
