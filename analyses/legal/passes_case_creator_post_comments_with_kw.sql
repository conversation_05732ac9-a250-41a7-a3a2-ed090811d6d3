-- comments on the inscope creators' account
with comment_with_keywords  as (
    select c.* , p.creator_id, 
            author_id as fan_id,
            u.username as fan_username
    from fanfix_raw.fanfix.comments c
    left join fanfix_raw.fanfix.posts p on p.id = c.post_id
    left join fanfix_raw.fanfix_user.users u on u.id = c.author_id
    where (
                lower(comment) like '%fanfix%'
                or lower(comment) like '%platform%'
                or lower(comment) like '%passes%' 
                or lower(comment) like '%onlyfans%'
                or lower(comment) like '%patreon%' 
                or lower(comment) like '%fanhouse%'
                or lower(comment) like '%fansly%'
        )
),
comments as (
    select ic.fanfix_id, ic.username as creator_username,
            cwk.id as comment_id,
            cwk.created_at as comment_created_at, 
            comment, 
            fan_id,
            fan_username,
            case when fan_id = ic.fanfix_id then 'creator_response' else 'fan_comment' end as comment_type
    from FANFIX_DATAMART_PROD.ANALYTICS.PASSES_MESSAGE_REQUEST_INSCOPE_CREATOR ic
    left join comment_with_keywords cwk on cwk.creator_id = ic.fanfix_id
    left join fanfix_datamart_prod.dbt.dim_creator_profile cp on cp.fanfix_id = ic.fanfix_id
    where  datediff('month', cwk.created_at, last_activity_date) >= 0 and datediff('month', cwk.created_at, last_activity_date) <= 6
)
select fanfix_id, creator_username,
        comment_created_at,
        comment,
        fan_username,
        comment_type
from comments