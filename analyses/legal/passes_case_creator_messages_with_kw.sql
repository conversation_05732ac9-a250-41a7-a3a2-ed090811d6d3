-- message exchanges from inscope creators
with messages_with_inscope_creator_and_keyword as (
        select id as message_id, 
                message_blast_id,
                created_at, 
                sender_user_id, 
                recipient_user_id,
                case when sender_user_id in ( 
                    select fanfix_id from FANFIX_DATAMART_PROD.ANALYTICS.PASSES_MESSAGE_REQUEST_INSCOPE_CREATOR
            ) then sender_user_id else recipient_user_id end as inscope_creator_id,
                content,
                row_number() over (partition by sender_user_id, message_blast_id order by created_at) as message_blast_order
        from fanfix_raw.fanfix_messaging.messages m
        where (
            sender_user_id in (
                select fanfix_id from FANFIX_DATAMART_PROD.ANALYTICS.PASSES_MESSAGE_REQUEST_INSCOPE_CREATOR
            )
            or recipient_user_id in (
                select fanfix_id from FANFIX_DATAMART_PROD.ANALYTICS.PASSES_MESSAGE_REQUEST_INSCOPE_CREATOR
            )
        ) and (
            lower(content) like '%fanfix%'
            or lower(content) like '%platform%'
            or lower(content) like '%passes%' 
            or lower(content) like '%onlyfans%'
            or lower(content) like '%patreon%' 
            or lower(content) like '%fanhouse%'
            or lower(content) like '%fansly%'
            )
),
inscope_messages as (
    select mkw.created_at as message_created_time, 
            cp.fanfix_username as creator_username,
            inscope_creator_id as creator_id,
            
            u1.username as sender_username,
            u2.username as recipient_username,
            mkw.content,

            recipient_user_id,
            sender_user_id,
            message_id,
             last_activity_date
    from messages_with_inscope_creator_and_keyword mkw
    left join fanfix_raw.fanfix_user.users u1 on u1.id = mkw.sender_user_id
    left join fanfix_raw.fanfix_user.users u2 on u2.id = mkw.recipient_user_id
    left join fanfix_datamart_prod.dbt.dim_creator_profile cp on cp.fanfix_id = mkw.inscope_creator_id
    where datediff('month', mkw.created_at, last_activity_date) >= 0 and datediff('month', mkw.created_at, last_activity_date) <= 6
         and (message_blast_id is null or message_blast_id = '<nil>' ) --or message_blast_order = 1)
),
result_data as (
    -- select *,
    select  message_created_time, 
            creator_username,
            creator_id,
            sender_username,
            recipient_username,
            content,
        case when (sender_user_id, content) in (select creator_id, text from fanfix_raw.fanfix.greeting_messages) then 'greeting message' else 'not greeting message' end as greeting_message_flag
    from inscope_messages
    -- where greeting_message_flag = 'not greeting message'

)
select message_created_time, 
            creator_username,
            creator_id,
            sender_username,
            recipient_username,
            content,
            greeting_message_flag
from result_data
--where creator_username = 'jadennewman1'
-- summary as (
--     select creator_id, creator_username, last_activity_date, min(message_created_time) as min_message_date, max(message_created_time) as max_message_date,
--             count(distinct case when sender_user_id = creator_id then message_id else null end) as p6m_message_sent_with_kw,
--             count(distinct case when recipient_user_id = creator_id then message_id else null end) as p6m_message_received_with_kw
--     from result_data
--     group by 1,2,3 order by 1 asc
-- )
-- select ic.fanfix_id, ic.username, summary.*
-- from FANFIX_DATAMART_PROD.ANALYTICS.PASSES_MESSAGE_REQUEST_INSCOPE_CREATOR ic
-- left join summary on summary.creator_id = ic.fanfix_id
-- order by username
