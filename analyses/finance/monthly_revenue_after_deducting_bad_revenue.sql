with base as (
    select date_trunc('month', time) as month,
        sum(gross_revenue) as gmv,
        sum(case when charge_back_flag not like '%not%' then gross_revenue else 0 end ) as chargeback_revenue,
        sum(case when duplicated_subscription_flag not like '%not%' then gross_revenue else 0 end ) as duplicated_subscription_revenue,
        sum(case when duplicated_unlock_flag not like '%not%' then gross_revenue else 0 end ) as duplicated_unlock_revenue,
        sum(case when duplicated_tip_flag not like '%not%' then gross_revenue else 0 end ) as duplicated_tip_revenue,
        sum(case when charge_back_flag like '%not%' and duplicated_subscription_flag like '%not%' and duplicated_unlock_flag like '%not%' and duplicated_tip_flag like '%not%' then gross_revenue else 0 end) as gmv_after_adjustment
    from {{ref("fact_charges_with_issue_flag")}}
    group by 1
)
select *,
    gmv - gmv_after_adjustment as total_adjusted
from base
