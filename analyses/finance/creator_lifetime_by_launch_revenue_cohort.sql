with creator_cohort as (
    select fanfix_id, fanfix_username, first_earning_day,
            launch_date, launch_revenue,
            date_trunc('month',  launch_date) as first_month,
        case when launch_revenue < 1000 and launch_revenue >= 100 then '1. Launch Revenue $100 - $1000'
        when launch_revenue < 3000 and launch_revenue >= 100 then '1. Launch Revenue $1000 - $3000'
            when launch_revenue < 10000 and launch_revenue >= 3000 then '2. Launch Revenue $3000 - $10000'
            when launch_revenue < 20000 and launch_revenue >= 10000 then '3. Launch Revenue $10000 - $20000'
            when launch_revenue < 50000 and launch_revenue >= 20000 then '4. Launch Revenue $20000 - $50000'
            when launch_revenue < 100000 and launch_revenue >= 50000 then '5. Launch Revenue $50000 - $100000'
            when launch_revenue >= 100000 then '6. Launch Revenue > $100000'
            else 'other'
        end as creator_cohort
    from fanfix_datamart_prod.dbt.dim_creator_profile cp 
    -- where lead_source_main_category not like '%Sunroom%' -- potentially removing Sunroom because activity and revenue data won't line up
),
summary as (
    select creator_cohort, first_month, count(*) as total_creator_count from creator_cohort group by 1,2 order by 1 
),
activity as (
    select creator_id, username, created_day, post_count + blast_count + outgoing_dm_count + livestream_count as total_content
    from  fanfix_datamart_prod.dbt.fact_daily_creator_activity
),
first_pass as (
    select  creator_cohort, first_month, date_trunc('month', created_day) as activity_month, 
            datediff('month', first_month, activity_month) as month_diff,
            count(distinct case when total_content > 0 then a.creator_id else null end) as active_creator_count
    from activity a 
    left join creator_cohort on a.creator_id = creator_cohort.fanfix_id
    where  activity_month >= first_month and launch_date >= '2022-06-01'  -- and  activity_month >= '2024-08-01'
    group by 1,2,3 order by 1 asc, 2 asc
),
active_creator_retention_by_revenue_and_launch_cohort as (
    select first_pass.creator_cohort, first_pass.first_month, total_creator_count,
            sum(Case when month_diff = 0 then active_creator_count else 0 end) as "Month0",
            sum(Case when month_diff = 1 then active_creator_count else 0 end) as "Month1",
            sum(Case when month_diff = 2 then active_creator_count else 0 end) as "Month2",
            sum(Case when month_diff = 3 then active_creator_count else 0 end) as "Month3",
            sum(Case when month_diff = 4 then active_creator_count else 0 end) as "Month4",
            sum(Case when month_diff = 5 then active_creator_count else 0 end) as "Month5",
            sum(Case when month_diff = 6 then active_creator_count else 0 end) as "Month6",
            sum(Case when month_diff = 7 then active_creator_count else 0 end) as "Month7",
            sum(Case when month_diff = 8 then active_creator_count else 0 end) as "Month8",
            sum(Case when month_diff = 9 then active_creator_count else 0 end) as "Month9",
            sum(Case when month_diff = 10 then active_creator_count else 0 end) as "Month10",
            sum(Case when month_diff = 11 then active_creator_count else 0 end) as "Month11",
            sum(Case when month_diff = 12 then active_creator_count else 0 end) as "Month12",
            sum(Case when month_diff = 13 then active_creator_count else 0 end) as "Month13",
            sum(Case when month_diff = 14 then active_creator_count else 0 end) as "Month14",
            sum(Case when month_diff = 15 then active_creator_count else 0 end) as "Month15",
            sum(Case when month_diff = 16 then active_creator_count else 0 end) as "Month16",
            sum(Case when month_diff = 17 then active_creator_count else 0 end) as "Month17",
            sum(Case when month_diff = 18 then active_creator_count else 0 end) as "Month18",
            sum(Case when month_diff = 19 then active_creator_count else 0 end) as "Month19",
            sum(Case when month_diff = 20 then active_creator_count else 0 end) as "Month20",
            sum(Case when month_diff = 21 then active_creator_count else 0 end) as "Month21",
            sum(Case when month_diff = 22 then active_creator_count else 0 end) as "Month22",
            sum(Case when month_diff = 23 then active_creator_count else 0 end) as "Month23",
            sum(Case when month_diff = 24 then active_creator_count else 0 end) as "Month24",
            sum(Case when month_diff = 25 then active_creator_count else 0 end) as "Month25",
            sum(Case when month_diff = 26 then active_creator_count else 0 end) as "Month26",
            sum(Case when month_diff = 27 then active_creator_count else 0 end) as "Month27",
            sum(Case when month_diff = 28 then active_creator_count else 0 end) as "Month28",
            sum(Case when month_diff = 29 then active_creator_count else 0 end) as "Month29",
            sum(Case when month_diff = 30 then active_creator_count else 0 end) as "Month30",
            sum(Case when month_diff = 31 then active_creator_count else 0 end) as "Month31",
            sum(Case when month_diff = 32 then active_creator_count else 0 end) as "Month32",
            sum(Case when month_diff = 33 then active_creator_count else 0 end) as "Month33",
            sum(Case when month_diff = 34 then active_creator_count else 0 end) as "Month34",
            sum(Case when month_diff = 35 then active_creator_count else 0 end) as "Month35",
            sum(Case when month_diff = 36 then active_creator_count else 0 end) as "Month36",
            
    from first_pass 
    left join summary s on s.creator_cohort = first_pass.creator_cohort and s.first_month = first_pass.first_month
    group by 1,2,3 order by 1,2
),
active_ind_by_revenue_and_launch_cohort as(
    select creator_cohort, first_month, total_creator_count,
            case when "Month0" > 0 or datediff('month', first_month, current_date()) >= 0 then 1 else 0 end as "Month0",
            case when "Month1" > 0 or datediff('month', first_month, current_date()) >= 1 then 1 else 0 end as "Month1",
            case when "Month2" > 0 or datediff('month', first_month, current_date()) >= 2 then 1 else 0 end as "Month2",
            case when "Month3" > 0 or datediff('month', first_month, current_date()) >= 3 then 1 else 0 end as "Month3",
            case when "Month4" > 0 or datediff('month', first_month, current_date()) >= 4 then 1 else 0 end as "Month4",
            case when "Month5" > 0 or datediff('month', first_month, current_date()) >= 5 then 1 else 0 end as "Month5",
            case when "Month6" > 0 or datediff('month', first_month, current_date()) >= 6 then 1 else 0 end as "Month6",
            case when "Month7" > 0 or datediff('month', first_month, current_date()) >= 7 then 1 else 0 end as "Month7",
            case when "Month8" > 0 or datediff('month', first_month, current_date()) >= 8 then 1 else 0 end as "Month8",
            case when "Month9" > 0 or datediff('month', first_month, current_date()) >= 9 then 1 else 0 end as "Month9",
            case when "Month10" > 0 or datediff('month', first_month, current_date()) >= 10 then 1 else 0 end as "Month10",
            case when "Month11" > 0 or datediff('month', first_month, current_date()) >= 11 then 1 else 0 end as "Month11",
            case when "Month12" > 0 or datediff('month', first_month, current_date()) >= 12 then 1 else 0 end as "Month12",
            case when "Month13" > 0 or datediff('month', first_month, current_date()) >= 13 then 1 else 0 end as "Month13",
            case when "Month14" > 0 or datediff('month', first_month, current_date()) >= 14 then 1 else 0 end as "Month14",
            case when "Month15" > 0 or datediff('month', first_month, current_date()) >= 15 then 1 else 0 end as "Month15",
            case when "Month16" > 0 or datediff('month', first_month, current_date()) >= 16 then 1 else 0 end as "Month16",
            case when "Month17" > 0 or datediff('month', first_month, current_date()) >= 17 then 1 else 0 end as "Month17",
            case when "Month18" > 0 or datediff('month', first_month, current_date()) >= 18 then 1 else 0 end as "Month18",
            case when "Month19" > 0 or datediff('month', first_month, current_date()) >= 19 then 1 else 0 end as "Month19",
            case when "Month20" > 0 or datediff('month', first_month, current_date()) >= 20 then 1 else 0 end as "Month20",
            case when "Month21" > 0 or datediff('month', first_month, current_date()) >= 21 then 1 else 0 end as "Month21",
            case when "Month22" > 0 or datediff('month', first_month, current_date()) >= 22 then 1 else 0 end as "Month22",
            case when "Month23" > 0 or datediff('month', first_month, current_date()) >= 23 then 1 else 0 end as "Month23",
            case when "Month24" > 0 or datediff('month', first_month, current_date()) >= 24 then 1 else 0 end as "Month24",
            case when "Month25" > 0 or datediff('month', first_month, current_date()) >= 25 then 1 else 0 end as "Month25",
            case when "Month26" > 0 or datediff('month', first_month, current_date()) >= 26 then 1 else 0 end as "Month26",
            case when "Month27" > 0 or datediff('month', first_month, current_date()) >= 27 then 1 else 0 end as "Month27",
            case when "Month28" > 0 or datediff('month', first_month, current_date()) >= 28 then 1 else 0 end as "Month28",
            case when "Month29" > 0 or datediff('month', first_month, current_date()) >= 29 then 1 else 0 end as "Month29",
            case when "Month30" > 0 or datediff('month', first_month, current_date()) >= 30 then 1 else 0 end as "Month30",
            case when "Month31" > 0 or datediff('month', first_month, current_date()) >= 31 then 1 else 0 end as "Month31",
            case when "Month32" > 0 or datediff('month', first_month, current_date()) >= 32 then 1 else 0 end as "Month32",
            case when "Month33" > 0 or datediff('month', first_month, current_date()) >= 33 then 1 else 0 end as "Month33",
            case when "Month34" > 0 or datediff('month', first_month, current_date()) >= 34 then 1 else 0 end as "Month34",
            case when "Month35" > 0 or datediff('month', first_month, current_date()) >= 35 then 1 else 0 end as "Month35",
            case when "Month36" > 0 or datediff('month', first_month, current_date()) >= 36 then 1 else 0 end as "Month36",
    from active_creator_retention_by_revenue_and_launch_cohort
)
select aib.creator_cohort,  
        sum(aib.total_creator_count) as creator_count,
        sum(aib."Month0"*acr."Month0")/sum(aib."Month0"*aib.total_creator_count) as Month0Retention,
         sum(aib."Month0"*acr."Month1")/sum(aib."Month1"*aib.total_creator_count) as Month1Retention,
         sum(aib."Month0"*acr."Month2")/sum(aib."Month2"*aib.total_creator_count) as Month2Retention,
         sum(aib."Month0"*acr."Month3")/sum(aib."Month3"*aib.total_creator_count) as Month3Retention,
         sum(aib."Month0"*acr."Month4")/sum(aib."Month4"*aib.total_creator_count) as Month4Retention,
         sum(aib."Month0"*acr."Month5")/sum(aib."Month5"*aib.total_creator_count) as Month5Retention,
         sum(aib."Month0"*acr."Month6")/sum(aib."Month6"*aib.total_creator_count) as Month6Retention,
         sum(aib."Month0"*acr."Month7")/sum(aib."Month7"*aib.total_creator_count) as Month7Retention,
         sum(aib."Month0"*acr."Month8")/sum(aib."Month8"*aib.total_creator_count) as Month8Retention,
         sum(aib."Month0"*acr."Month9")/sum(aib."Month9"*aib.total_creator_count) as Month9Retention,
         sum(aib."Month0"*acr."Month10")/sum(aib."Month10"*aib.total_creator_count) as Month10Retention,
         sum(aib."Month0"*acr."Month11")/sum(aib."Month11"*aib.total_creator_count) as Month11Retention,
         sum(aib."Month0"*acr."Month12")/sum(aib."Month12"*aib.total_creator_count) as Month12Retention,
         sum(aib."Month0"*acr."Month13")/sum(aib."Month13"*aib.total_creator_count) as Month13Retention,
         sum(aib."Month0"*acr."Month14")/sum(aib."Month14"*aib.total_creator_count) as Month14Retention,
         sum(aib."Month0"*acr."Month15")/sum(aib."Month15"*aib.total_creator_count) as Month15Retention,
         sum(aib."Month0"*acr."Month16")/sum(aib."Month16"*aib.total_creator_count) as Month16Retention,
         sum(aib."Month0"*acr."Month17")/sum(aib."Month17"*aib.total_creator_count) as Month17Retention,
         sum(aib."Month0"*acr."Month18")/sum(aib."Month18"*aib.total_creator_count) as Month18Retention,
        sum(aib."Month0"*acr."Month19")/sum(aib."Month19"*aib.total_creator_count) as Month19Retention,
        sum(aib."Month0"*acr."Month20")/sum(aib."Month20"*aib.total_creator_count) as Month20Retention,
        sum(aib."Month0"*acr."Month21")/sum(aib."Month21"*aib.total_creator_count) as Month21Retention,
        sum(aib."Month0"*acr."Month22")/sum(aib."Month22"*aib.total_creator_count) as Month22Retention,
        sum(aib."Month0"*acr."Month23")/sum(aib."Month23"*aib.total_creator_count) as Month23Retention,
        sum(aib."Month0"*acr."Month24")/sum(aib."Month24"*aib.total_creator_count) as Month24Retention,
        
        sum(aib."Month0"*acr."Month25")/sum(aib."Month25"*aib.total_creator_count) as Month25Retention,
        sum(aib."Month0"*acr."Month26")/sum(aib."Month26"*aib.total_creator_count) as Month26Retention,
        sum(aib."Month0"*acr."Month27")/sum(aib."Month27"*aib.total_creator_count) as Month27Retention,
        sum(aib."Month0"*acr."Month28")/sum(aib."Month28"*aib.total_creator_count) as Month28Retention,
        sum(aib."Month0"*acr."Month29")/sum(aib."Month29"*aib.total_creator_count) as Month29Retention,
        sum(aib."Month0"*acr."Month30")/sum(aib."Month30"*aib.total_creator_count) as Month30Retention,
        sum(aib."Month0"*acr."Month31")/sum(aib."Month31"*aib.total_creator_count) as Month31Retention,
        sum(aib."Month0"*acr."Month32")/sum(aib."Month32"*aib.total_creator_count) as Month32Retention,
        sum(aib."Month0"*acr."Month33")/sum(aib."Month33"*aib.total_creator_count) as Month33Retention,
        sum(aib."Month0"*acr."Month34")/sum(aib."Month34"*aib.total_creator_count) as Month34Retention,
        sum(aib."Month0"*acr."Month35")/sum(aib."Month35"*aib.total_creator_count) as Month35Retention,
        sum(aib."Month0"*acr."Month36")/sum(aib."Month36"*aib.total_creator_count) as Month36Retention
from active_ind_by_revenue_and_launch_cohort aib
join active_creator_retention_by_revenue_and_launch_cohort  acr on acr.creator_cohort = aib.creator_cohort
                                                                and acr.first_month = aib.first_month
                                                                and aib.total_creator_count = acr.total_creator_count
group by 1 order by 1
