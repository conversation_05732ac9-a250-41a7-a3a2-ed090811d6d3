WITH promo_code_details AS (
SELECT DISTINCT 
    fanfix_id, fanfix_username,
    promotion_id, promotion_code,
    created_at AS promo_created_date,
    DATEDIFF(day, created_at, expires_at) AS days_active, 
    uses_count,
    CASE WHEN (expires_at <= getdate()) THEN 'expired' ELSE 'active' END AS code_active,
    target_audiences,
    discount_duration_in_months
FROM {{ ref ("dim_promo_code_creator_adoption") }}
)

SELECT  
    pc.fanfix_id, pc.fanfix_username, promotion_id, promotion_code, promo_created_date, days_active, uses_count, code_active, target_audiences, discount_duration_in_months,
    AVG(CASE WHEN day BETWEEN DATEADD(day, -7, pc.promo_created_date) 
        and DATEADD(day, -1, pc.promo_created_date) THEN kpi.new_subscribers ELSE NULL END) AS avg_daily_new_subs_7_days_before,
    AVG(CASE WHEN day BETWEEN DATEADD(day, 1, pc.promo_created_date) 
        and DATEADD(day, 7, pc.promo_created_date) THEN kpi.new_subscribers ELSE NULL END) AS avg_daily_new_subs_7_days_after,
    AVG(CASE WHEN day BETWEEN DATEADD(day, -7, pc.promo_created_date) 
        and DATEADD(day, -1, pc.promo_created_date) THEN kpi.subscriptions_revenue ELSE NULL END) AS avg_daily_sub_revenue_7_days_before,
    AVG(CASE WHEN day BETWEEN DATEADD(day, 1, pc.promo_created_date) 
        and DATEADD(day, 7, pc.promo_created_date) THEN kpi.subscriptions_revenue ELSE NULL END) AS avg_daily_sub_revenue_7_days_after,
    AVG(CASE WHEN day BETWEEN DATEADD(day, -7, pc.promo_created_date) 
        and DATEADD(day, -1, pc.promo_created_date) THEN kpi.total_pageview ELSE NULL END) AS avg_daily_pageviews_7_days_before,
    AVG(CASE WHEN day BETWEEN DATEADD(day, 1, pc.promo_created_date) 
        and DATEADD(day, 7, pc.promo_created_date) THEN kpi.total_pageview ELSE NULL END) AS avg_daily_pageviews_7_days_after,
    AVG(CASE WHEN day BETWEEN DATEADD(day, -7, pc.promo_created_date) 
        and DATEADD(day, -1, pc.promo_created_date) THEN kpi.total_revenue ELSE NULL END) AS avg_daily_gmv_7_days_before,
    AVG(CASE WHEN day BETWEEN DATEADD(day, 1, pc.promo_created_date) 
        and DATEADD(day, 7, pc.promo_created_date) THEN kpi.total_revenue ELSE NULL END) AS avg_daily_gmv_7_days_after,
    AVG(CASE WHEN day BETWEEN DATEADD(day, -7, pc.promo_created_date) 
        and DATEADD(day, -1, pc.promo_created_date) THEN kpi.incoming_dm_count ELSE NULL END) AS avg_daily_incoming_dm_7_days_before,
    AVG(CASE WHEN day BETWEEN DATEADD(day, 1, pc.promo_created_date) 
        and DATEADD(day, 7, pc.promo_created_date) THEN kpi.incoming_dm_count ELSE NULL END) AS avg_daily_incoming_dm_7_days_after,
    FROM {{ ref ("fact_creator_daily_kpis") }} kpi
INNER JOIN {{ ref ("dim_promo_code_creator_adoption") }} pc ON kpi.creator_id = pc.fanfix_id
WHERE 1=1
GROUP BY 1,2,3,4,5,6,7,8,9,10
--HAVING avg_daily_new_subs_7_days_before >= 5
--HAVING uses_count >= 20
ORDER BY fanfix_username, promo_created_date

