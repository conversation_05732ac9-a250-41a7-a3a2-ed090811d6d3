WITH dm_data AS (
    SELECT recipient_user_id, sender_user_id,
            DATE_TRUNC('day', created_at) AS day,
            COUNT(DISTINCT message_id) AS message_count,
    FROM {{ ref("stg_fanfix_messaging_messages") }}
    WHERE (message_blast_id = '<nil>' OR message_blast_id is null)
    GROUP BY 1,2
),

subscription_data AS (
    SELECT creator_fanfix_id, fan_fanfix_id, applied_promotion_id, subscription_created_date
    FROM {{ ref("fact_subscriptions") }}
    WHERE subscription_created_date >= '2025-10-01'
    and recurring_interval_count = 1
),

charges AS (
    SELECT creator_fanfix_id, fan_fanfix_id,
        DATE_TRUNC('day', time) AS day, 
        SUM(gross_revenue) as gross_revenue,
        COUNT(DISTINCT charge_id) AS num_payments
    FROM {{ ref("fact_creator_charges") }}
    WHERE time >= '2025-10-01'
    GROUP BY 1,2,3
)

fan_14d_metrics AS (
    SELECT
        s.creator_fanfix_id, s.fan_fanfix_id,
        CASE
            WHEN s.applied_promotion_id IS NOT NULL THEN 'yes'
            ELSE 'no'
        END AS used_promo_code,

        -- per-fan averages across days
        COALESCE(AVG(c.gross_revenue), 0) AS avg_daily_gross_revenue_14d,
        COALESCE(AVG(c.num_payments), 0) AS avg_daily_payment_count_14d,
        COALESCE(AVG(dd.message_count), 0) AS avg_daily_f2c_message_count_14d

    FROM subscription_data s
    LEFT JOIN charges c
        ON c.creator_fanfix_id = s.creator_fanfix_id
       AND c.fan_fanfix_id = s.fan_fanfix_id
       AND c.day BETWEEN s.subscription_created_date
                      AND DATEADD(day, 14, s.subscription_created_date)
    LEFT JOIN dm_data dd
        ON dd.recipient_user_id = s.creator_fanfix_id
       AND dd.sender_user_id = s.fan_fanfix_id
       AND dd.day BETWEEN s.subscription_created_date
                       AND DATEADD(day, 14, s.subscription_created_date)

    GROUP BY
        s.creator_fanfix_id,
        s.fan_fanfix_id,
        used_promo_code
)

SELECT
    p.fanfix_id, p.fanfix_username,

    -- Subscriber counts
    COUNT(DISTINCT CASE WHEN f.used_promo_code = 'yes' THEN f.fan_fanfix_id END) AS subscriber_count_promo,
    COUNT(DISTINCT CASE WHEN f.used_promo_code = 'no'  THEN f.fan_fanfix_id END) AS subscriber_count_reg,

    -- Avg daily gross revenue (avg of fan-level avgs)
    COALESCE(AVG(CASE WHEN f.used_promo_code = 'yes' THEN f.avg_daily_gross_revenue_14d END), 0)
        AS avg_daily_gross_revenue_14d_promo,
    COALESCE(AVG(CASE WHEN f.used_promo_code = 'no' THEN f.avg_daily_gross_revenue_14d END), 0)
        AS avg_daily_gross_revenue_14d_reg,

    -- Avg daily payment count
    COALESCE(AVG(CASE WHEN f.used_promo_code = 'yes' THEN f.avg_daily_payment_count_14d END), 0)
        AS avg_daily_payment_count_14d_promo,
    COALESCE(AVG(CASE WHEN f.used_promo_code = 'no' THEN f.avg_daily_payment_count_14d END), 0)
        AS avg_daily_payment_count_14d_reg,

    -- Avg daily message count
    COALESCE(AVG(CASE WHEN f.used_promo_code = 'yes' THEN f.avg_daily_f2c_message_count_14d END), 0)
        AS avg_daily_f2c_message_count_14d_promo,
    COALESCE(AVG(CASE WHEN f.used_promo_code = 'no' THEN f.avg_daily_f2c_message_count_14d END), 0)
        AS avg_daily_f2c_message_count_14d_reg

FROM fan_14d_metrics f
JOIN {{ ref ('dim_promo_code_creator_adoption') }} p
    ON p.fanfix_id = f.creator_fanfix_id

GROUP BY
    p.fanfix_id, p.fanfix_username
HAVING subscriber_count_promo > 0
ORDER BY
    p.fanfix_username