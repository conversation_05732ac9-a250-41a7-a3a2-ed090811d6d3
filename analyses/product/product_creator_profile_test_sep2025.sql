with kpi_data as (
    select 
        kpi.creator_id,
        kpi.day,
        kpi.total_followers,
        kpi.total_subscribers,
        kpi.incoming_dm_count,
        kpi.total_revenue,
        kpi.paying_user,
        case 
            when kpi.day >= '2025-08-15' and kpi.day <= '2025-08-27' then 'period 1'
            when kpi.day >= ic.start_date and kpi.day <= '2025-09-24' then 'period 2'
            else null
        end as period
    from {{ref("fact_creator_daily_kpis")}}  kpi 
    inner join {{ref("inscope_creator_sep2025")}} ic on kpi.creator_id = ic.creator_fanfix_id
    where (
        (kpi.day >= '2025-08-15' and kpi.day <= '2025-08-27') or
        (kpi.day >= ic.start_date and kpi.day <= '2025-09-24')
    )
),
period_stats as (
    select 
        creator_id,
        period,
        round(avg(total_followers), 2) as avg_daily_followers,
        round(avg(incoming_dm_count), 2) as avg_daily_incoming_messages,
        round(avg(total_subscribers), 2) as avg_daily_subscribers,
        round(avg(total_revenue), 2) as avg_daily_revenue,
        round(avg(paying_user), 2) as avg_daily_paying_users
    from kpi_data
    where period is not null
    group by creator_id, period
)
select 
    creator_id,
    fanfix_username,
    max(case when period = 'period 1' then avg_daily_followers end) as period1_avg_followers,
    max(case when period = 'period 2' then avg_daily_followers end) as period2_avg_followers,
    round(case 
    when max(case when period = 'period 1' then avg_daily_followers end) > 0 
    then (max(case when period = 'period 2' then avg_daily_followers end) - 
          max(case when period = 'period 1' then avg_daily_followers end)) / 
          max(case when period = 'period 1' then avg_daily_followers end) * 100
    end, 2) as followers_pct_change,
    
    max(case when period = 'period 1' then avg_daily_incoming_messages end) as period1_avg_incoming_messages,
    max(case when period = 'period 2' then avg_daily_incoming_messages end) as period2_avg_incoming_messages,
    round(case 
        when max(case when period = 'period 1' then avg_daily_incoming_messages end) > 0 
        then (max(case when period = 'period 2' then avg_daily_incoming_messages end) - 
              max(case when period = 'period 1' then avg_daily_incoming_messages end)) / 
              max(case when period = 'period 1' then avg_daily_incoming_messages end) * 100
    end, 2) as messages_pct_change,
    
    
    max(case when period = 'period 1' then avg_daily_subscribers end) as period1_avg_subscribers,
    max(case when period = 'period 2' then avg_daily_subscribers end) as period2_avg_subscribers,
    round(case 
        when max(case when period = 'period 1' then avg_daily_subscribers end) > 0 
        then (max(case when period = 'period 2' then avg_daily_subscribers end) - 
              max(case when period = 'period 1' then avg_daily_subscribers end)) / 
              max(case when period = 'period 1' then avg_daily_subscribers end) * 100
    end, 2) as subscribers_pct_change,


    
    max(case when period = 'period 1' then avg_daily_revenue end) as period1_avg_revenue,
    max(case when period = 'period 2' then avg_daily_revenue end) as period2_avg_revenue,
    round(case 
        when max(case when period = 'period 1' then avg_daily_revenue end) > 0 
        then (max(case when period = 'period 2' then avg_daily_revenue end) - 
              max(case when period = 'period 1' then avg_daily_revenue end)) / 
              max(case when period = 'period 1' then avg_daily_revenue end) * 100
    end, 2) as revenue_pct_change,

    max(case when period = 'period 1' then avg_daily_paying_users end) as period1_avg_paying_users,
    max(case when period = 'period 2' then avg_daily_paying_users end) as period2_avg_paying_users,
    round(case 
        when max(case when period = 'period 1' then avg_daily_paying_users end) > 0 
        then (max(case when period = 'period 2' then avg_daily_paying_users end) - 
              max(case when period = 'period 1' then avg_daily_paying_users end)) / 
              max(case when period = 'period 1' then avg_daily_paying_users end) * 100
    end, 2) as paying_users_pct_change
  
from period_stats ps
left join {{ref("dim_creator_profile")}} cp on ps.creator_id = cp.fanfix_id
group by 1,2
order by 6 desc