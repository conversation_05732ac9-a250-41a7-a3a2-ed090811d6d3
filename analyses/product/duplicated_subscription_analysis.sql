with subscription_with_new_id as (
    select s.*, c.revenue_category_detail,gross_revenue,c.time as payment_time,
            s.creator_fanfix_id || '-' || s.fan_fanfix_id || '-' || DATE_TRUNC('day', subscription_created_at)  as combined_id ,
            case when subscription_id like '%sub_%' then 'old' else 'new' end as subscription_type
    from {{ref("fact_subscriptions")}} s
    left join {{ref("fact_creator_charges")}} c on c.charge_id = s.charge_id
),
tmp as (
    select DATE_TRUNC('day', subscription_created_at) as created_at, combined_id, 
            count(distinct subscription_id) as dup_subscription_count,
            listagg(subscription_id, ',') WITHIN GROUP (ORDER BY subscription_id) as id_list,
            listagg(subscription_type, ',') WITHIN GROUP (ORDER BY subscription_period_start_ts) as subscription_type_list
    from  subscription_with_new_id
    group by 1,2
    having count(distinct subscription_id) > 1
    order by 3 desc
),
duplicated_records as (
    select * 
    from tmp 
    where created_at < '2025-01-01' -- and created_at >= '2024-01-01' 
    and ((subscription_type_list like '%old%' and subscription_type_list not like '%new%')
     or  (subscription_type_list not like '%old%' and subscription_type_list like '%new%'))
),
duplicated_subs as (
    select sn.* ,
        case when dr.id_list is not null then 'has_duplicated_records' else 'no_duplicated_records' end as dr_label
    from subscription_with_new_id sn
    left join duplicated_records dr on dr.combined_id = sn.combined_id
)
select date_trunc('year', payment_time) as payment_year,
       -- date_trunc('month', payment_time) as payment_month,
        count(distinct subscription_id) as unique_subs,
        count(distinct case when dr_label like '%has_%' then subscription_id else null end) as unique_subs_with_possible_dupe,
        count(distinct case when dr_label like '%has_%' then subscription_id else null end)/count(distinct subscription_id) as duplication_rate_volume,
        sum(gross_revenue) as total_subs_revenue,
        sum(case when dr_label like '%has_%' then gross_revenue else 0 end) as subs_revenue_from_possible_dup_subs,
        sum(case when dr_label like '%has_%' then gross_revenue else 0 end)/sum(gross_revenue) as duplicated_revenue_percentage
from duplicated_subs
group by 1 order by 1 desc