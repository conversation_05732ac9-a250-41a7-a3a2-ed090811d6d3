WITH params AS (
    SELECT
        10  AS min_content_len     -- exclude ultra-short content
),
-- 1) Base message set (non-greeting, non-media)
base AS (
    SELECT
        m.message_id
        , m.sender_user_id AS creator_id
        , u.fanfix_username
        , u.account_management_agency
        , m.recipient_user_id
        , m.created_at
        , m.created_at::date AS msg_date
        , m.content
        , m.message_blast_id
        -- normalization: lower, trim, collapse whitespace, remove common punctuation
        , REGEXP_REPLACE(
            REGEXP_REPLACE(LOWER(TRIM(m.content)), '\\s+', ' '),
            '[!@#$%^&*(),.?":{}|<>\\[\\]\\-_+=;\'`~]',
            ''
        ) AS content_norm
    FROM {{ ref('stg_fanfix_messaging_messages') }} m
    INNER JOIN {{ ref('dim_creator_profile') }} u ON m.sender_user_id = u.fanfix_id
    LEFT JOIN {{ ref('stg_fanfix_greeting_messages') }} gm ON m.content = gm.text and m.sender_user_id = gm.creator_id
    WHERE 1=1
        AND gm.id IS NULL
        AND m.content IS NOT NULL
        AND LENGTH(TRIM(m.content)) >= (SELECT min_content_len FROM params)
        AND m.created_at::date >= '2025-01-01'
        AND m.content NOT IN ('Media sent','Media Sent','Voice note sent','Test Message')
        AND (m.message_blast_id IS NULL OR m.message_blast_id = '<nil>')
),
-- 2) Blast templates per creator (normalized)
blast_templates AS (
    SELECT
        b.message_blast_id
        , b.creator_id
        , b.text AS blast_text
        , REGEXP_REPLACE(
            REGEXP_REPLACE(LOWER(TRIM(b.text)), '\\s+', ' '),
            '[!@#$%^&*(),.?":{}|<>\\[\\]\\-_+=;\'`~]',
            ''
        ) AS blast_text_norm
    FROM {{ ref('stg_fanfix_messaging_message_blast') }} b
    WHERE b.text IS NOT NULL
),
-- 3) Enrich base messages with blast template match signals
enriched AS (
    SELECT
        b.*
        , bt.message_blast_id AS matched_blast_id
        , IFF(bt.message_blast_id IS NOT NULL, 1, 0) AS is_blast_template_match
    FROM base b
    LEFT JOIN blast_templates bt ON b.creator_id = bt.creator_id AND b.content_norm = bt.blast_text_norm
)
SELECT * FROM enriched