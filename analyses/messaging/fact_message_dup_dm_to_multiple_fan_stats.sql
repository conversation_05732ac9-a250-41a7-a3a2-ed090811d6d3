WITH params AS (
    SELECT
        10  AS suspected_same_content_min_sends       -- >=10 identical sends
        , 60  AS suspected_same_content_max_minutes   -- within 60 minutes
),
-- Behavior metrics: per creator/content/day
creator_content_day AS (
    SELECT
        creator_id
        , msg_date
        , content_norm
        , COUNT(*) AS sends_same_content_day
        , COUNT(DISTINCT recipient_user_id) AS unique_recipients_same_content_day
        , MIN(created_at) AS first_sent_at
        , MAX(created_at) AS last_sent_at
        , DATEDIFF('minute', MIN(created_at), MAX(created_at)) AS span_minutes
    FROM fanfix_datamart_prod.dbt.fact_message_dup_dm_to_multiple_fans
    GROUP BY 1,2,3
),
final_stats AS (
    SELECT
        e.creator_id
        , e.fanfix_username
        , e.account_management_agency
        , e.recipient_user_id
        , e.msg_date
        , e.created_at
        , e.content
        , e.matched_blast_id
        -- , cd.total_sends_day
        -- , cd.unique_recipients_day
        , ccd.sends_same_content_day
        , ccd.unique_recipients_same_content_day
        , ccd.span_minutes
        -- velocity guard (avoid div by 0)
        , (ccd.sends_same_content_day / NULLIF(GREATEST(ccd.span_minutes, 1), 0)) AS sends_per_min_same_content
        , CASE
            -- A) blast-like manual (template match but missing blast_id)
            -- Definition: Looks like a blast (blast template text), but was sent as DMs.
            WHEN  e.matched_blast_id IS NOT NULL THEN 'blast_like_manual'

            -- B) suspected manual (behavior-based; conservative)
            -- Definition: Not a proper blast, not matching a known blast template, but behavior is blast-like.
            WHEN e.message_blast_id IS NULL
            AND (
                    -- High repeat identical content (within short time):
                    -- >=10 identical sends and within <= 10 minutes (velocity)
                ccd.sends_same_content_day >= (SELECT suspected_same_content_min_sends FROM params)
                AND ccd.span_minutes <= (SELECT suspected_same_content_max_minutes FROM params)
                )
            THEN 'suspected_manual'

            -- C) normal dm
            ELSE 'normal_dm'
        END AS classification

    FROM fanfix_datamart_prod.dbt.fact_message_dup_dm_to_multiple_fans e
    -- LEFT JOIN creator_day cd ON e.creator_id = cd.creator_id AND e.msg_date = cd.msg_date
    LEFT JOIN creator_content_day ccd ON e.creator_id = ccd.creator_id AND e.msg_date = ccd.msg_date AND e.content_norm = ccd.content_norm
)
SELECT 
    *
FROM final_stats
ORDER BY msg_date DESC
    , account_management_agency
    , fanfix_username
    , classification
    , sends_same_content_day DESC